<?php
/**
 * Test Tunch Calculator - Interactive Testing Interface
 */

require_once 'config/database.php';
require_once 'lib/TunchCalculator.php';

$db = getDB();
$calculator = new TunchCalculator($db);

// Handle form submission
$calculation = null;
$error = null;

if ($_POST) {
    try {
        $grossWeight = floatval($_POST['gross_weight']);
        $stoneWeight = floatval($_POST['stone_weight']);
        $tunchPercentage = floatval($_POST['tunch_percentage']);
        $baseRate = floatval($_POST['base_rate']);
        $makingCharges = floatval($_POST['making_charges']);
        $stoneCharges = floatval($_POST['stone_charges']);
        $wastagePercentage = floatval($_POST['wastage_percentage']);
        
        $calculation = $calculator->calculateTunch(
            $grossWeight, $stoneWeight, $tunchPercentage, $baseRate,
            $makingCharges, $stoneCharges, $wastagePercentage
        );
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get current daily rates
$dailyRates = $db->fetchAll("
    SELECT metal_type, purity, base_rate, tunch_rate 
    FROM daily_rates 
    WHERE rate_date = CURDATE() AND is_active = 1 
    ORDER BY metal_type, purity
");

// Get sample products
$sampleProducts = $db->fetchAll("
    SELECT p.id, p.product_name, p.gross_weight, p.stone_weight, 
           p.tunch_percentage, p.metal_type, p.purity,
           i.selling_price
    FROM products p 
    LEFT JOIN inventory i ON p.id = i.product_id 
    WHERE p.gross_weight > 0 
    ORDER BY p.id 
    LIMIT 5
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tunch Calculator Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result-card {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .calculation-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .calculation-table th,
        .calculation-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .calculation-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .highlight {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .rates-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        .rates-table th,
        .rates-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: center;
        }
        .rates-table th {
            background-color: #e9ecef;
        }
        .preset-btn {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <h1>🧮 Tunch Calculator Test Interface</h1>
    <p>Interactive testing tool for Indian Jewellery Tunch Calculations</p>

    <?php if ($error): ?>
    <div class="error">
        <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
    </div>
    <?php endif; ?>

    <div class="container">
        <!-- Input Form -->
        <div>
            <h2>📝 Calculation Input</h2>
            
            <!-- Current Daily Rates -->
            <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h4>📈 Today's Rates</h4>
                <?php if (count($dailyRates) > 0): ?>
                <table class="rates-table">
                    <tr>
                        <th>Metal</th>
                        <th>Purity</th>
                        <th>Base Rate</th>
                        <th>Tunch Rate</th>
                        <th>Action</th>
                    </tr>
                    <?php foreach ($dailyRates as $rate): ?>
                    <tr>
                        <td><?php echo $rate['metal_type']; ?></td>
                        <td><?php echo $rate['purity']; ?></td>
                        <td>₹<?php echo number_format($rate['base_rate'], 0); ?></td>
                        <td>₹<?php echo number_format($rate['tunch_rate'], 0); ?></td>
                        <td>
                            <button class="preset-btn" onclick="setRate(<?php echo $rate['base_rate']; ?>, <?php echo $rate['tunch_rate']; ?>)">Use</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </table>
                <?php else: ?>
                <p style="color: #856404;">No daily rates found. <a href="add-sample-jewellery-data.php">Add sample data</a> first.</p>
                <?php endif; ?>
            </div>

            <form method="POST">
                <div class="form-group">
                    <label for="gross_weight">Gross Weight (grams):</label>
                    <input type="number" id="gross_weight" name="gross_weight" step="0.001" 
                           value="<?php echo $_POST['gross_weight'] ?? '10.500'; ?>" required>
                </div>

                <div class="form-group">
                    <label for="stone_weight">Stone Weight (grams):</label>
                    <input type="number" id="stone_weight" name="stone_weight" step="0.001" 
                           value="<?php echo $_POST['stone_weight'] ?? '1.200'; ?>" required>
                </div>

                <div class="form-group">
                    <label for="tunch_percentage">Tunch Percentage (%):</label>
                    <select id="tunch_percentage" name="tunch_percentage" onchange="updateTunchPercentage()">
                        <option value="99.9" <?php echo ($_POST['tunch_percentage'] ?? '') == '99.9' ? 'selected' : ''; ?>>24K Gold (99.9%)</option>
                        <option value="91.6" <?php echo ($_POST['tunch_percentage'] ?? '91.6') == '91.6' ? 'selected' : ''; ?>>22K Gold (91.6%)</option>
                        <option value="87.5" <?php echo ($_POST['tunch_percentage'] ?? '') == '87.5' ? 'selected' : ''; ?>>21K Gold (87.5%)</option>
                        <option value="83.3" <?php echo ($_POST['tunch_percentage'] ?? '') == '83.3' ? 'selected' : ''; ?>>20K Gold (83.3%)</option>
                        <option value="75.0" <?php echo ($_POST['tunch_percentage'] ?? '') == '75.0' ? 'selected' : ''; ?>>18K Gold (75.0%)</option>
                        <option value="92.5" <?php echo ($_POST['tunch_percentage'] ?? '') == '92.5' ? 'selected' : ''; ?>>925 Silver (92.5%)</option>
                        <option value="99.9" <?php echo ($_POST['tunch_percentage'] ?? '') == '99.9' ? 'selected' : ''; ?>>999 Silver (99.9%)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="base_rate">Base Rate (₹ per gram):</label>
                    <input type="number" id="base_rate" name="base_rate" step="0.01" 
                           value="<?php echo $_POST['base_rate'] ?? '6600'; ?>" required>
                </div>

                <div class="form-group">
                    <label for="making_charges">Making Charges (₹):</label>
                    <input type="number" id="making_charges" name="making_charges" step="0.01" 
                           value="<?php echo $_POST['making_charges'] ?? '500'; ?>" required>
                </div>

                <div class="form-group">
                    <label for="stone_charges">Stone Charges (₹):</label>
                    <input type="number" id="stone_charges" name="stone_charges" step="0.01" 
                           value="<?php echo $_POST['stone_charges'] ?? '2000'; ?>" required>
                </div>

                <div class="form-group">
                    <label for="wastage_percentage">Wastage Percentage (%):</label>
                    <input type="number" id="wastage_percentage" name="wastage_percentage" step="0.1" 
                           value="<?php echo $_POST['wastage_percentage'] ?? '2.5'; ?>" required>
                </div>

                <button type="submit" class="btn">Calculate Tunch</button>
            </form>

            <!-- Sample Products -->
            <?php if (count($sampleProducts) > 0): ?>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h4>💍 Sample Products</h4>
                <?php foreach ($sampleProducts as $product): ?>
                <button class="preset-btn" onclick="loadProduct(
                    <?php echo $product['gross_weight']; ?>,
                    <?php echo $product['stone_weight']; ?>,
                    <?php echo $product['tunch_percentage']; ?>,
                    '<?php echo $product['product_name']; ?>'
                )"><?php echo $product['product_name']; ?></button>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Results -->
        <div>
            <h2>📊 Calculation Results</h2>
            
            <?php if ($calculation): ?>
            <div class="result-card">
                <h3>🧮 Tunch Calculation Breakdown</h3>
                
                <table class="calculation-table">
                    <tr>
                        <th>Parameter</th>
                        <th>Value</th>
                        <th>Formula/Notes</th>
                    </tr>
                    <tr>
                        <td><strong>Gross Weight</strong></td>
                        <td><?php echo $calculation['gross_weight']; ?> grams</td>
                        <td>Total weight including stones</td>
                    </tr>
                    <tr>
                        <td><strong>Stone Weight</strong></td>
                        <td><?php echo $calculation['stone_weight']; ?> grams</td>
                        <td>Weight of stones/gems only</td>
                    </tr>
                    <tr class="highlight">
                        <td><strong>Net Weight</strong></td>
                        <td><?php echo $calculation['net_weight']; ?> grams</td>
                        <td>Gross Weight - Stone Weight</td>
                    </tr>
                    <tr>
                        <td><strong>Tunch Percentage</strong></td>
                        <td><?php echo $calculation['tunch_percentage']; ?>%</td>
                        <td>Purity percentage</td>
                    </tr>
                    <tr class="highlight">
                        <td><strong>Tunch Weight</strong></td>
                        <td><?php echo $calculation['tunch_weight']; ?> grams</td>
                        <td>Net Weight × (Tunch % ÷ 100)</td>
                    </tr>
                    <tr>
                        <td><strong>Base Rate</strong></td>
                        <td>₹<?php echo number_format($calculation['base_rate'], 2); ?></td>
                        <td>Market rate per gram</td>
                    </tr>
                    <tr class="highlight">
                        <td><strong>Tunch Rate</strong></td>
                        <td>₹<?php echo number_format($calculation['tunch_rate'], 2); ?></td>
                        <td>Base Rate × (Tunch % ÷ 100)</td>
                    </tr>
                    <tr class="highlight">
                        <td><strong>Metal Value</strong></td>
                        <td>₹<?php echo number_format($calculation['metal_value'], 2); ?></td>
                        <td>Tunch Weight × Tunch Rate</td>
                    </tr>
                    <tr>
                        <td><strong>Making Charges</strong></td>
                        <td>₹<?php echo number_format($calculation['making_charges'], 2); ?></td>
                        <td>Labor and craftsmanship</td>
                    </tr>
                    <tr>
                        <td><strong>Stone Charges</strong></td>
                        <td>₹<?php echo number_format($calculation['stone_charges'], 2); ?></td>
                        <td>Cost of stones/gems</td>
                    </tr>
                    <tr>
                        <td><strong>Wastage Amount</strong></td>
                        <td>₹<?php echo number_format($calculation['wastage_amount'], 2); ?></td>
                        <td>Metal Value × (Wastage % ÷ 100)</td>
                    </tr>
                    <tr class="highlight" style="background-color: #d4edda; font-size: 16px;">
                        <td><strong>FINAL VALUE</strong></td>
                        <td><strong>₹<?php echo number_format($calculation['final_value'], 2); ?></strong></td>
                        <td><strong>Metal + Making + Stone + Wastage</strong></td>
                    </tr>
                </table>

                <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 15px;">
                    <h4>📐 Formula Applied:</h4>
                    <div style="font-family: monospace; font-size: 14px;">
                        <strong>Net Weight</strong> = <?php echo $calculation['gross_weight']; ?> - <?php echo $calculation['stone_weight']; ?> = <?php echo $calculation['net_weight']; ?> grams<br>
                        <strong>Tunch Weight</strong> = <?php echo $calculation['net_weight']; ?> × (<?php echo $calculation['tunch_percentage']; ?> ÷ 100) = <?php echo $calculation['tunch_weight']; ?> grams<br>
                        <strong>Tunch Rate</strong> = ₹<?php echo number_format($calculation['base_rate'], 2); ?> × (<?php echo $calculation['tunch_percentage']; ?> ÷ 100) = ₹<?php echo number_format($calculation['tunch_rate'], 2); ?><br>
                        <strong>Final Value</strong> = (<?php echo $calculation['tunch_weight']; ?> × ₹<?php echo number_format($calculation['tunch_rate'], 2); ?>) + ₹<?php echo number_format($calculation['making_charges'], 2); ?> + ₹<?php echo number_format($calculation['stone_charges'], 2); ?> + ₹<?php echo number_format($calculation['wastage_amount'], 2); ?> = <strong>₹<?php echo number_format($calculation['final_value'], 2); ?></strong>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="result-card">
                <h3>🔍 Ready for Calculation</h3>
                <p>Enter the jewellery details in the form and click "Calculate Tunch" to see the detailed breakdown.</p>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 15px;">
                    <h4>💡 Quick Tips:</h4>
                    <ul>
                        <li>Use the "Today's Rates" section to get current market rates</li>
                        <li>Click on sample products to load preset values</li>
                        <li>Tunch percentage varies by purity (22K = 91.6%, 18K = 75.0%)</li>
                        <li>Making charges are typically ₹500-2000 per piece</li>
                        <li>Wastage is usually 1.5-3.5% of metal value</li>
                    </ul>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="inventory.php" style="background-color: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">View Inventory</a>
        <a href="billing.php" style="background-color: #007bff; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">Test Billing</a>
        <a href="add-sample-jewellery-data.php" style="background-color: #ffc107; color: black; padding: 12px 20px; text-decoration: none; border-radius: 4px; margin: 5px;">Add More Data</a>
    </div>

    <script>
        function setRate(baseRate, tunchRate) {
            document.getElementById('base_rate').value = baseRate;
        }

        function loadProduct(grossWeight, stoneWeight, tunchPercentage, productName) {
            document.getElementById('gross_weight').value = grossWeight;
            document.getElementById('stone_weight').value = stoneWeight;
            document.getElementById('tunch_percentage').value = tunchPercentage;
            alert('Loaded: ' + productName);
        }

        function updateTunchPercentage() {
            // Auto-update base rate based on purity selection
            const tunchPercentage = document.getElementById('tunch_percentage').value;
            const baseRateField = document.getElementById('base_rate');
            
            if (tunchPercentage == '91.6' || tunchPercentage == '87.5' || tunchPercentage == '83.3' || tunchPercentage == '75.0') {
                // Gold rates
                baseRateField.value = '6600';
            } else if (tunchPercentage == '92.5' || tunchPercentage == '99.9') {
                // Silver rates
                baseRateField.value = '85';
            }
        }
    </script>
</body>
</html>
