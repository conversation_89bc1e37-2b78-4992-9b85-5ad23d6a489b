<?php
/**
 * Create Sample Notifications for Testing
 */

require_once 'config/database.php';
require_once 'includes/notifications.php';

try {
    echo "<h1>🔔 Creating Sample Notifications</h1>";
    
    // Clear existing notifications first
    $db = getDB();
    $db->query("DELETE FROM notifications");
    echo "<p>✅ Cleared existing notifications</p>";
    
    // Create various types of notifications
    $notifications = [
        [
            'title' => 'Low Stock Alert',
            'message' => '3 products are running low on stock and need to be restocked immediately.',
            'type' => 'warning',
            'action_url' => 'inventory.php?filter=low_stock'
        ],
        [
            'title' => 'Daily Sales Target Achieved!',
            'message' => 'Congratulations! Today\'s sales have reached ₹75,000, exceeding the daily target.',
            'type' => 'success',
            'action_url' => 'sales.php'
        ],
        [
            'title' => 'New Customer Registration',
            'message' => 'A new business customer "Sunita Jewellers" has been registered in the system.',
            'type' => 'info',
            'action_url' => 'customers.php'
        ],
        [
            'title' => 'Metal Rate Update Required',
            'message' => 'Gold and silver rates haven\'t been updated in the last 24 hours. Please update current market rates.',
            'type' => 'warning',
            'action_url' => 'metal-rates.php'
        ],
        [
            'title' => 'Backup Reminder',
            'message' => 'It\'s been 7 days since the last system backup. Consider creating a backup of your data.',
            'type' => 'info',
            'action_url' => 'backup.php'
        ],
        [
            'title' => 'High Value Transaction',
            'message' => 'A sale worth ₹1,25,000 has been completed for customer "VG Jewellery Store".',
            'type' => 'success',
            'action_url' => 'sales.php'
        ],
        [
            'title' => 'Inventory Audit Due',
            'message' => 'Monthly inventory audit is due. Please verify stock levels and update discrepancies.',
            'type' => 'info',
            'action_url' => 'inventory.php'
        ],
        [
            'title' => 'Payment Overdue',
            'message' => '2 customers have overdue payments totaling ₹45,000. Follow up required.',
            'type' => 'error',
            'action_url' => 'customers.php?filter=overdue'
        ]
    ];
    
    foreach ($notifications as $notification) {
        $id = createNotification(
            null, // Global notification (not user-specific)
            $notification['title'],
            $notification['message'],
            $notification['type'],
            $notification['action_url']
        );
        
        if ($id) {
            echo "<p>✅ Created notification: {$notification['title']}</p>";
        } else {
            echo "<p>❌ Failed to create notification: {$notification['title']}</p>";
        }
    }
    
    // Create some time-sensitive notifications
    $urgentNotifications = [
        [
            'title' => 'System Maintenance Scheduled',
            'message' => 'System maintenance is scheduled for tonight at 11:00 PM. Please complete all pending work.',
            'type' => 'warning',
            'action_url' => null,
            'expires_at' => date('Y-m-d 23:59:59') // Expires at end of today
        ],
        [
            'title' => 'Special Offer Reminder',
            'message' => 'Don\'t forget to inform customers about the 5% discount on gold jewelry valid until tomorrow.',
            'type' => 'info',
            'action_url' => 'products.php',
            'expires_at' => date('Y-m-d 23:59:59', strtotime('+1 day')) // Expires tomorrow
        ]
    ];
    
    foreach ($urgentNotifications as $notification) {
        $db->query("INSERT INTO notifications (user_id, title, message, type, action_url, expires_at) VALUES (?, ?, ?, ?, ?, ?)", [
            null,
            $notification['title'],
            $notification['message'],
            $notification['type'],
            $notification['action_url'],
            $notification['expires_at']
        ]);
        
        echo "<p>✅ Created time-sensitive notification: {$notification['title']}</p>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; color: #155724; margin: 30px 0;'>";
    echo "<h2>🎉 Sample Notifications Created Successfully!</h2>";
    echo "<p>Created various types of notifications:</p>";
    echo "<ul>";
    echo "<li>✅ Warning notifications (low stock, overdue payments)</li>";
    echo "<li>✅ Success notifications (sales achievements)</li>";
    echo "<li>✅ Info notifications (reminders, updates)</li>";
    echo "<li>✅ Error notifications (critical issues)</li>";
    echo "<li>✅ Time-sensitive notifications with expiry</li>";
    echo "</ul>";
    echo "<p><strong>Total notifications created:</strong> " . (count($notifications) + count($urgentNotifications)) . "</p>";
    echo "</div>";
    
    echo "<h3>🌐 Test the Notifications:</h3>";
    echo "<p><a href='index.php' target='_blank' class='btn btn-primary'>📊 View Dashboard with Notifications</a></p>";
    
    // Show current notification count
    $totalNotifications = $db->fetch("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0")['count'];
    echo "<p><strong>Current unread notifications:</strong> $totalNotifications</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Creating Notifications</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
