<?php
/**
 * Export Users Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Check if specific users are selected
    $selectedUsers = $_POST['selected_users'] ?? [];
    
    $whereClause = "u.is_active = 1";
    $params = [];
    
    if (!empty($selectedUsers)) {
        $placeholders = str_repeat('?,', count($selectedUsers) - 1) . '?';
        $whereClause .= " AND u.id IN ($placeholders)";
        $params = $selectedUsers;
    }

    // Get users data with comprehensive information
    $users = $db->fetchAll("
        SELECT u.*, 
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE created_by = u.id AND is_cancelled = 0) as total_revenue,
               (SELECT COUNT(*) FROM system_logs WHERE user_id = u.id) as total_activities,
               (SELECT MAX(created_at) FROM system_logs WHERE user_id = u.id AND log_type = 'access' AND message LIKE '%login%') as last_login
        FROM users u
        WHERE $whereClause
        ORDER BY u.created_at DESC
    ", $params);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'User ID', 'Username', 'Full Name', 'Email', 'Phone', 'Role', 
            'Sales Count', 'Total Revenue (₹)', 'Total Activities', 'Last Login', 'Status', 'Created Date'
        ];

        fputcsv($output, $headers);

        foreach ($users as $user) {
            $row = [
                $user['id'],
                $user['username'],
                $user['full_name'],
                $user['email'],
                $user['phone'] ?? '',
                ucfirst($user['role']),
                $user['sales_count'],
                $user['total_revenue'],
                $user['total_activities'],
                $user['last_login'] ? date('d/m/Y H:i:s', strtotime($user['last_login'])) : 'Never',
                $user['is_active'] ? 'Active' : 'Inactive',
                date('d/m/Y H:i:s', strtotime($user['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Users Report</title>
        </head>
        <body>
            <h1>Users Report</h1>
            <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            
            <table border='1'>
                <tr>
                    <th>User ID</th>
                    <th>Username</th>
                    <th>Full Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Role</th>
                    <th>Sales Count</th>
                    <th>Total Revenue</th>
                    <th>Total Activities</th>
                    <th>Last Login</th>
                    <th>Status</th>
                    <th>Created Date</th>
                </tr>";

        foreach ($users as $user) {
            echo "<tr>
                    <td>" . $user['id'] . "</td>
                    <td>" . htmlspecialchars($user['username']) . "</td>
                    <td>" . htmlspecialchars($user['full_name']) . "</td>
                    <td>" . htmlspecialchars($user['email']) . "</td>
                    <td>" . htmlspecialchars($user['phone'] ?? '') . "</td>
                    <td>" . ucfirst($user['role']) . "</td>
                    <td>" . $user['sales_count'] . "</td>
                    <td>" . $user['total_revenue'] . "</td>
                    <td>" . $user['total_activities'] . "</td>
                    <td>" . ($user['last_login'] ? date('d/m/Y H:i:s', strtotime($user['last_login'])) : 'Never') . "</td>
                    <td>" . ($user['is_active'] ? 'Active' : 'Inactive') . "</td>
                    <td>" . date('d/m/Y H:i:s', strtotime($user['created_at'])) . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'users_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // Group users by role
        $usersByRole = [];
        foreach ($users as $user) {
            $usersByRole[$user['role']][] = $user;
        }
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Users Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .role-section { margin-bottom: 30px; }
                .role-header { background-color: #e3f2fd; font-weight: bold; padding: 10px; }
                .stats { color: #666; font-size: 11px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Users Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>";
        
        foreach ($usersByRole as $role => $roleUsers) {
            echo "<div class='role-section'>";
            echo "<div class='role-header'>" . ucfirst($role) . " Users (" . count($roleUsers) . ")</div>";
            echo "<table>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>Name</th>";
            echo "<th>Username</th>";
            echo "<th>Email</th>";
            echo "<th>Sales</th>";
            echo "<th>Revenue</th>";
            echo "<th>Last Login</th>";
            echo "<th>Status</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($roleUsers as $user) {
                echo "<tr>";
                echo "<td><strong>" . htmlspecialchars($user['full_name']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . $user['sales_count'] . "</td>";
                echo "<td>₹" . number_format($user['total_revenue'], 2) . "</td>";
                echo "<td>" . ($user['last_login'] ? date('d/m/Y', strtotime($user['last_login'])) : 'Never') . "</td>";
                echo "<td>" . ($user['is_active'] ? 'Active' : 'Inactive') . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
        }
        
        echo "<script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting users: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='users.php'>← Back to Users</a></p>";
    echo "</div>";
}
?>
