<?php
/**
 * Sales Analytics AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Today's sales
    $todaySales = $db->fetch("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE DATE(sale_date) = CURDATE() AND is_cancelled = 0
    ")['total'];
    
    // This week's sales
    $weekSales = $db->fetch("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE YEARWEEK(sale_date, 1) = YEARWEEK(CURDATE(), 1) AND is_cancelled = 0
    ")['total'];
    
    // This month's sales
    $monthSales = $db->fetch("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE YEAR(sale_date) = YEAR(CURDATE()) 
        AND MONTH(sale_date) = MONTH(CURDATE()) 
        AND is_cancelled = 0
    ")['total'];
    
    // Outstanding amount
    $outstandingAmount = $db->fetch("
        SELECT COALESCE(SUM(s.grand_total - COALESCE(p.paid_amount, 0)), 0) as total
        FROM sales s
        LEFT JOIN (
            SELECT sale_id, SUM(amount) as paid_amount 
            FROM payments 
            GROUP BY sale_id
        ) p ON s.id = p.sale_id
        WHERE s.payment_status != 'paid' AND s.is_cancelled = 0
    ")['total'];
    
    // Top customers by total purchases
    $topCustomers = $db->fetchAll("
        SELECT 
            COALESCE(c.customer_name, 'Walk-in Customer') as name,
            COUNT(s.id) as orders,
            COALESCE(SUM(s.grand_total), 0) as total
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.is_cancelled = 0
        GROUP BY s.customer_id, c.customer_name
        ORDER BY total DESC
        LIMIT 5
    ");
    
    // Monthly sales trend (last 6 months)
    $monthlySales = $db->fetchAll("
        SELECT 
            DATE_FORMAT(sale_date, '%Y-%m') as month,
            DATE_FORMAT(sale_date, '%M %Y') as month_name,
            COUNT(*) as order_count,
            COALESCE(SUM(grand_total), 0) as total_amount
        FROM sales 
        WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        AND is_cancelled = 0
        GROUP BY DATE_FORMAT(sale_date, '%Y-%m')
        ORDER BY month DESC
    ");
    
    // Payment status distribution
    $paymentStatus = $db->fetchAll("
        SELECT 
            payment_status,
            COUNT(*) as count,
            COALESCE(SUM(grand_total), 0) as total_amount
        FROM sales 
        WHERE is_cancelled = 0
        GROUP BY payment_status
    ");
    
    // Product performance (top selling products)
    $topProducts = $db->fetchAll("
        SELECT 
            p.product_name,
            SUM(si.quantity) as total_quantity,
            COALESCE(SUM(si.total_price), 0) as total_revenue
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        JOIN sales s ON si.sale_id = s.id
        WHERE s.is_cancelled = 0
        GROUP BY si.product_id, p.product_name
        ORDER BY total_revenue DESC
        LIMIT 5
    ");
    
    // Format top customers for display
    $formattedTopCustomers = [];
    foreach ($topCustomers as $customer) {
        $formattedTopCustomers[] = [
            'name' => $customer['name'],
            'orders' => $customer['orders'],
            'total' => number_format($customer['total'], 2)
        ];
    }
    
    // Format monthly sales for charts
    $formattedMonthlySales = [];
    foreach ($monthlySales as $month) {
        $formattedMonthlySales[] = [
            'month' => $month['month_name'],
            'orders' => $month['order_count'],
            'amount' => $month['total_amount']
        ];
    }
    
    // Format payment status for charts
    $formattedPaymentStatus = [];
    foreach ($paymentStatus as $status) {
        $formattedPaymentStatus[] = [
            'status' => ucfirst($status['payment_status']),
            'count' => $status['count'],
            'amount' => $status['total_amount']
        ];
    }
    
    // Format top products
    $formattedTopProducts = [];
    foreach ($topProducts as $product) {
        $formattedTopProducts[] = [
            'name' => $product['product_name'],
            'quantity' => $product['total_quantity'],
            'revenue' => number_format($product['total_revenue'], 2)
        ];
    }
    
    // Return analytics data
    echo json_encode([
        'success' => true,
        'todaySales' => number_format($todaySales, 2),
        'weekSales' => number_format($weekSales, 2),
        'monthSales' => number_format($monthSales, 2),
        'outstandingAmount' => number_format($outstandingAmount, 2),
        'topCustomers' => $formattedTopCustomers,
        'monthlySales' => $formattedMonthlySales,
        'paymentStatus' => $formattedPaymentStatus,
        'topProducts' => $formattedTopProducts,
        'summary' => [
            'totalSales' => $db->fetch("SELECT COUNT(*) as count FROM sales WHERE is_cancelled = 0")['count'],
            'totalRevenue' => number_format($db->fetch("SELECT COALESCE(SUM(grand_total), 0) as total FROM sales WHERE is_cancelled = 0")['total'], 2),
            'averageOrderValue' => number_format($db->fetch("SELECT COALESCE(AVG(grand_total), 0) as avg FROM sales WHERE is_cancelled = 0")['avg'], 2),
            'totalCustomers' => $db->fetch("SELECT COUNT(DISTINCT customer_id) as count FROM sales WHERE is_cancelled = 0 AND customer_id IS NOT NULL")['count']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
