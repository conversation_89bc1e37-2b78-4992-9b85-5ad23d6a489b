<?php
/**
 * Financial Reports - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
$report_type = $_GET['report_type'] ?? 'summary';

try {
    // Get sales data for the period
    $salesData = $db->fetch("
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(grand_total), 0) as total_revenue,
            COALESCE(SUM(paid_amount), 0) as total_collected,
            COALESCE(SUM(balance_amount), 0) as total_outstanding,
            COALESCE(AVG(grand_total), 0) as average_sale_value
        FROM sales 
        WHERE sale_date BETWEEN ? AND ? AND is_cancelled = 0
    ", [$date_from, $date_to]);

    // Get payment method breakdown
    $paymentMethods = $db->fetchAll("
        SELECT 
            payment_method,
            COUNT(*) as transaction_count,
            COALESCE(SUM(paid_amount), 0) as total_amount
        FROM sales 
        WHERE sale_date BETWEEN ? AND ? AND is_cancelled = 0 AND paid_amount > 0
        GROUP BY payment_method
        ORDER BY total_amount DESC
    ", [$date_from, $date_to]);

    // Get daily sales trend
    $dailySales = $db->fetchAll("
        SELECT 
            sale_date,
            COUNT(*) as sales_count,
            COALESCE(SUM(grand_total), 0) as daily_revenue,
            COALESCE(SUM(paid_amount), 0) as daily_collection
        FROM sales 
        WHERE sale_date BETWEEN ? AND ? AND is_cancelled = 0
        GROUP BY sale_date
        ORDER BY sale_date ASC
    ", [$date_from, $date_to]);

    // Get top customers by revenue
    $topCustomers = $db->fetchAll("
        SELECT 
            c.customer_name,
            c.business_name,
            COUNT(s.id) as purchase_count,
            COALESCE(SUM(s.grand_total), 0) as total_spent,
            COALESCE(SUM(s.balance_amount), 0) as outstanding_amount
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.sale_date BETWEEN ? AND ? AND s.is_cancelled = 0
        GROUP BY s.customer_id, c.customer_name, c.business_name
        ORDER BY total_spent DESC
        LIMIT 10
    ", [$date_from, $date_to]);

    // Get category-wise sales
    $categorySales = $db->fetchAll("
        SELECT 
            cat.category_name,
            COUNT(DISTINCT s.id) as sales_count,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as category_revenue
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE s.sale_date BETWEEN ? AND s.is_cancelled = 0
        GROUP BY cat.id, cat.category_name
        ORDER BY category_revenue DESC
    ", [$date_from, $date_to]);

    // Calculate profit margins (simplified)
    $profitData = $db->fetch("
        SELECT 
            COALESCE(SUM(si.quantity * p.cost_price), 0) as total_cost,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as total_selling_price
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.sale_date BETWEEN ? AND ? AND s.is_cancelled = 0
    ", [$date_from, $date_to]);

    $grossProfit = $profitData['total_selling_price'] - $profitData['total_cost'];
    $profitMargin = $profitData['total_selling_price'] > 0 ? ($grossProfit / $profitData['total_selling_price']) * 100 : 0;

} catch (Exception $e) {
    $error = "Error loading financial data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reports - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Financial Reports</h2>
                    <p class="text-muted mb-0">Analyze revenue, profit, and financial performance</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Report Period</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select" name="report_type">
                                <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>>Summary</option>
                                <option value="detailed" <?php echo $report_type == 'detailed' ? 'selected' : ''; ?>>Detailed</option>
                                <option value="profit_loss" <?php echo $report_type == 'profit_loss' ? 'selected' : ''; ?>>Profit & Loss</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-chart-line me-2"></i>Generate Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Key Financial Metrics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary">₹<?php echo number_format($salesData['total_revenue'], 2); ?></h3>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success">₹<?php echo number_format($salesData['total_collected'], 2); ?></h3>
                            <small class="text-muted">Total Collected</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">₹<?php echo number_format($salesData['total_outstanding'], 2); ?></h3>
                            <small class="text-muted">Outstanding</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">₹<?php echo number_format($grossProfit, 2); ?></h3>
                            <small class="text-muted">Gross Profit</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary"><?php echo number_format($profitMargin, 1); ?>%</h3>
                            <small class="text-muted">Profit Margin</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-dark">₹<?php echo number_format($salesData['average_sale_value'], 2); ?></h3>
                            <small class="text-muted">Avg. Sale Value</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Daily Sales Trend -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Daily Sales Trend</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailySalesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Payment Methods</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($paymentMethods)): ?>
                                <p class="text-muted text-center">No payment data available</p>
                            <?php else: ?>
                                <?php foreach ($paymentMethods as $method): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong><?php echo ucfirst($method['payment_method']); ?></strong>
                                            <br><small class="text-muted"><?php echo $method['transaction_count']; ?> transactions</small>
                                        </div>
                                        <div class="text-end">
                                            <strong>₹<?php echo number_format($method['total_amount'], 2); ?></strong>
                                        </div>
                                    </div>
                                    <hr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Top Customers -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Top Customers by Revenue</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead>
                                        <tr>
                                            <th>Customer</th>
                                            <th>Purchases</th>
                                            <th>Revenue</th>
                                            <th>Outstanding</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($topCustomers)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center py-3 text-muted">No customer data available</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($topCustomers as $customer): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($customer['customer_name'] ?? 'Walk-in Customer'); ?></strong>
                                                        <?php if ($customer['business_name']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($customer['business_name']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><span class="badge bg-info"><?php echo $customer['purchase_count']; ?></span></td>
                                                    <td><strong>₹<?php echo number_format($customer['total_spent'], 2); ?></strong></td>
                                                    <td>₹<?php echo number_format($customer['outstanding_amount'], 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Performance -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Performance</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead>
                                        <tr>
                                            <th>Category</th>
                                            <th>Sales</th>
                                            <th>Revenue</th>
                                            <th>Share</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($categorySales)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center py-3 text-muted">No category data available</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php 
                                            $totalCategoryRevenue = array_sum(array_column($categorySales, 'category_revenue'));
                                            foreach ($categorySales as $category): 
                                                $share = $totalCategoryRevenue > 0 ? ($category['category_revenue'] / $totalCategoryRevenue) * 100 : 0;
                                            ?>
                                                <tr>
                                                    <td><strong><?php echo htmlspecialchars($category['category_name'] ?? 'Uncategorized'); ?></strong></td>
                                                    <td><span class="badge bg-info"><?php echo $category['sales_count']; ?></span></td>
                                                    <td><strong>₹<?php echo number_format($category['category_revenue'], 2); ?></strong></td>
                                                    <td><?php echo number_format($share, 1); ?>%</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../assets/js/app.js"></script>
    
    <script>
        // Daily Sales Chart
        const ctx = document.getElementById('dailySalesChart').getContext('2d');
        const dailySalesData = <?php echo json_encode($dailySales); ?>;
        
        const labels = dailySalesData.map(item => {
            const date = new Date(item.sale_date);
            return date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit' });
        });
        
        const revenueData = dailySalesData.map(item => parseFloat(item.daily_revenue));
        const collectionData = dailySalesData.map(item => parseFloat(item.daily_collection));
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue',
                    data: revenueData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Collection',
                    data: collectionData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
        
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open('export-financial-report.php?' + params.toString(), '_blank');
        }
    </script>
</body>
</html>
