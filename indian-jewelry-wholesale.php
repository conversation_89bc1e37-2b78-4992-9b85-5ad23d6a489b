<?php
session_start();

// Indian Jewelry Wholesale Management System
// Features: Stock Tracking, Profit Calculations, Tunch-based Billing

class IndianJewelryWholesale {
    private $pdo;
    
    public function __construct() {
        $this->initDatabase();
        $this->createTables();
        $this->insertSampleData();
    }
    
    private function initDatabase() {
        try {
            $this->pdo = new PDO('sqlite:jewelry_wholesale.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("Database Error: " . $e->getMessage());
        }
    }
    
    private function createTables() {
        // Stock Management Table
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS stock (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sl_no INTEGER,
            supplier_name TEXT,
            location TEXT,
            product_name TEXT,
            
            -- Weight Management (Indian Jewelry Standard)
            gross_weight REAL DEFAULT 0,
            stone_weight REAL DEFAULT 0,
            net_weight REAL DEFAULT 0,
            
            -- Cost Management
            cost_per_gram REAL DEFAULT 0,
            total_cost REAL DEFAULT 0,
            
            -- 24K Calculations
            purity_percentage REAL DEFAULT 0,
            pure_gold_weight REAL DEFAULT 0,
            
            -- Stock Tracking
            opening_stock REAL DEFAULT 0,
            purchased_qty REAL DEFAULT 0,
            sold_qty REAL DEFAULT 0,
            current_stock REAL DEFAULT 0,
            
            -- Pricing
            selling_price_per_gram REAL DEFAULT 0,
            total_value REAL DEFAULT 0,
            
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Sales/Billing Table with Tunch Calculations
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_no TEXT UNIQUE,
            customer_name TEXT,
            customer_location TEXT,
            
            -- Product Details
            product_id INTEGER,
            product_name TEXT,
            
            -- Weight Details (Indian Standard)
            gross_weight REAL DEFAULT 0,
            stone_weight REAL DEFAULT 0,
            net_weight REAL DEFAULT 0,
            
            -- Tunch Calculation (Indian Jewelry Standard)
            tunch_percentage REAL DEFAULT 0,
            tunch_weight REAL DEFAULT 0,
            
            -- Pricing
            gold_rate_per_gram REAL DEFAULT 0,
            metal_value REAL DEFAULT 0,
            making_charges REAL DEFAULT 0,
            stone_charges REAL DEFAULT 0,
            
            -- Final Amount
            total_amount REAL DEFAULT 0,
            
            -- Profit Tracking
            cost_price REAL DEFAULT 0,
            profit_amount REAL DEFAULT 0,
            profit_percentage REAL DEFAULT 0,
            
            sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES stock (id)
        )");
        
        // Profit Analysis Table
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS profit_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            period_type TEXT, -- daily, monthly, yearly
            period_value TEXT, -- 2024-01, 2024-01-15, etc
            
            total_sales REAL DEFAULT 0,
            total_cost REAL DEFAULT 0,
            total_profit REAL DEFAULT 0,
            profit_margin REAL DEFAULT 0,
            
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
    }
    
    private function insertSampleData() {
        // Check if data already exists
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM stock");
        $stmt->execute();
        if ($stmt->fetchColumn() > 0) return;
        
        // Insert your exact spreadsheet data with proper stock tracking
        $stockData = [
            // Row 5: Emerald Jewel Industry - Chain
            [5, 'Emerald Jewel Industry', 'Coimbatore', 'Chain', 106.420, 0, 106.420, 87.5, 9310.50, 96, 102.163, 100, 0, 0, 100, 95.0, 10070.00],
            
            // Row 6: Nala Gold - Bangles  
            [6, 'Nala Gold', 'Mumbai', 'Bangles', 246.340, 0, 246.340, 85.2, 20996.25, 94, 231.560, 50, 0, 0, 50, 92.0, 22663.28],
            
            // Row 7: Surat - Studs
            [7, 'Surat', 'Surat', 'Studs', 110.325, 21.205, 89.120, 88.7, 7905.34, 95, 84.664, 25, 0, 0, 25, 96.5, 8600.48]
        ];
        
        foreach ($stockData as $item) {
            $stmt = $this->pdo->prepare("INSERT INTO stock (
                sl_no, supplier_name, location, product_name, gross_weight, stone_weight, net_weight,
                cost_per_gram, total_cost, purity_percentage, pure_gold_weight, opening_stock,
                purchased_qty, sold_qty, current_stock, selling_price_per_gram, total_value
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($item);
        }
        
        // Insert sample sales data with tunch calculations
        $salesData = [
            ['INV001', 'VG Jewellery', 'Coimbatore', 1, 'Chain', 10.160, 0, 10.160, 96, 9.754, 6500, 63401, 2500, 500, 66401, 8750, 57651, 86.8],
            ['INV002', 'Nishma Jewels', 'Mumbai', 2, 'Bangles', 82.220, 0, 82.220, 98, 80.576, 6800, 547916, 15000, 2000, 564916, 18500, 546416, 96.7],
            ['INV003', 'Dhanpal Jewels', 'Surat', 3, 'Studs', 1.170, 0.050, 1.120, 97, 1.086, 7200, 7819, 800, 100, 8719, 950, 7769, 89.1]
        ];
        
        foreach ($salesData as $sale) {
            $stmt = $this->pdo->prepare("INSERT INTO sales (
                invoice_no, customer_name, customer_location, product_id, product_name,
                gross_weight, stone_weight, net_weight, tunch_percentage, tunch_weight,
                gold_rate_per_gram, metal_value, making_charges, stone_charges, total_amount,
                cost_price, profit_amount, profit_percentage
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($sale);
        }
    }
    
    // STOCK TRACKING METHODS
    public function getStockSummary() {
        $stmt = $this->pdo->prepare("
            SELECT 
                sl_no,
                supplier_name,
                location,
                product_name,
                gross_weight,
                net_weight,
                current_stock,
                cost_per_gram,
                selling_price_per_gram,
                (current_stock * selling_price_per_gram) as stock_value,
                CASE 
                    WHEN current_stock < 10 THEN 'Low Stock'
                    WHEN current_stock < 25 THEN 'Medium Stock'
                    ELSE 'Good Stock'
                END as stock_status
            FROM stock 
            ORDER BY sl_no
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function updateStock($productId, $soldQty) {
        $stmt = $this->pdo->prepare("
            UPDATE stock 
            SET sold_qty = sold_qty + ?, 
                current_stock = current_stock - ?
            WHERE id = ?
        ");
        return $stmt->execute([$soldQty, $soldQty, $productId]);
    }
    
    // PROFIT CALCULATION METHODS
    public function getProfitAnalysis() {
        $stmt = $this->pdo->prepare("
            SELECT 
                SUM(total_amount) as total_sales,
                SUM(cost_price) as total_cost,
                SUM(profit_amount) as total_profit,
                AVG(profit_percentage) as avg_profit_margin,
                COUNT(*) as total_transactions
            FROM sales
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getProductWiseProfit() {
        $stmt = $this->pdo->prepare("
            SELECT 
                product_name,
                COUNT(*) as sales_count,
                SUM(total_amount) as total_sales,
                SUM(cost_price) as total_cost,
                SUM(profit_amount) as total_profit,
                AVG(profit_percentage) as avg_profit_margin
            FROM sales 
            GROUP BY product_name
            ORDER BY total_profit DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // TUNCH BILLING METHODS
    public function calculateTunchBill($grossWeight, $stoneWeight, $tunchPercentage, $goldRate, $makingCharges = 0, $stoneCharges = 0) {
        $netWeight = $grossWeight - $stoneWeight;
        $tunchWeight = $netWeight * ($tunchPercentage / 100);
        $metalValue = $tunchWeight * $goldRate;
        $totalAmount = $metalValue + $makingCharges + $stoneCharges;
        
        return [
            'net_weight' => $netWeight,
            'tunch_weight' => $tunchWeight,
            'metal_value' => $metalValue,
            'total_amount' => $totalAmount
        ];
    }
    
    public function createSale($data) {
        $calculation = $this->calculateTunchBill(
            $data['gross_weight'],
            $data['stone_weight'],
            $data['tunch_percentage'],
            $data['gold_rate'],
            $data['making_charges'] ?? 0,
            $data['stone_charges'] ?? 0
        );
        
        // Get cost price from stock
        $stmt = $this->pdo->prepare("SELECT cost_per_gram FROM stock WHERE id = ?");
        $stmt->execute([$data['product_id']]);
        $costPerGram = $stmt->fetchColumn();
        $costPrice = $calculation['net_weight'] * $costPerGram;
        
        $profitAmount = $calculation['total_amount'] - $costPrice;
        $profitPercentage = ($profitAmount / $costPrice) * 100;
        
        // Insert sale
        $stmt = $this->pdo->prepare("INSERT INTO sales (
            invoice_no, customer_name, customer_location, product_id, product_name,
            gross_weight, stone_weight, net_weight, tunch_percentage, tunch_weight,
            gold_rate_per_gram, metal_value, making_charges, stone_charges, total_amount,
            cost_price, profit_amount, profit_percentage
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $result = $stmt->execute([
            $data['invoice_no'], $data['customer_name'], $data['customer_location'],
            $data['product_id'], $data['product_name'],
            $data['gross_weight'], $data['stone_weight'], $calculation['net_weight'],
            $data['tunch_percentage'], $calculation['tunch_weight'],
            $data['gold_rate'], $calculation['metal_value'],
            $data['making_charges'] ?? 0, $data['stone_charges'] ?? 0,
            $calculation['total_amount'], $costPrice, $profitAmount, $profitPercentage
        ]);
        
        if ($result) {
            // Update stock
            $this->updateStock($data['product_id'], $calculation['net_weight']);
        }
        
        return $result;
    }
    
    public function getSalesHistory() {
        $stmt = $this->pdo->prepare("
            SELECT * FROM sales 
            ORDER BY sale_date DESC 
            LIMIT 20
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // LOGIN SYSTEM
    public function login($username, $password) {
        if ($username === 'admin' && $password === 'admin123') {
            $_SESSION['logged_in'] = true;
            $_SESSION['username'] = $username;
            return true;
        }
        return false;
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'];
    }
    
    public function logout() {
        session_destroy();
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Initialize system
$system = new IndianJewelryWholesale();

// Handle login
if (!$system->isLoggedIn()) {
    if ($_POST['login'] ?? false) {
        if ($system->login($_POST['username'], $_POST['password'])) {
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            $error = "Invalid credentials";
        }
    }
    // Show login form (will be added in next part)
    $showLogin = true;
} else {
    $showLogin = false;
}

// Handle logout
if ($_GET['action'] ?? '' === 'logout') {
    $system->logout();
}

// Get current page
$page = $_GET['page'] ?? 'dashboard';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$showLogin) {
    if (isset($_POST['create_sale'])) {
        if ($system->createSale($_POST)) {
            $message = "✅ Sale created successfully! Stock updated.";
        } else {
            $error = "❌ Error creating sale.";
        }
    }
}

// Get data for current page
$data = [];
if (!$showLogin) {
    switch ($page) {
        case 'stock':
            $data['stock'] = $system->getStockSummary();
            break;
        case 'profit':
            $data['profit_analysis'] = $system->getProfitAnalysis();
            $data['product_profit'] = $system->getProductWiseProfit();
            break;
        case 'billing':
            $data['sales_history'] = $system->getSalesHistory();
            $data['stock'] = $system->getStockSummary();
            break;
        default:
            $data['stock'] = $system->getStockSummary();
            $data['profit_analysis'] = $system->getProfitAnalysis();
            $data['sales_history'] = $system->getSalesHistory();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indian Jewelry Wholesale Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
        }
        .sidebar .nav-link {
            color: #8b4513;
            font-weight: 600;
            padding: 15px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(139, 69, 19, 0.2);
            color: #654321;
            transform: translateX(5px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-3px);
        }
        .stat-card {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            color: #8b4513;
        }
        .profit-card {
            background: linear-gradient(135deg, #32cd32 0%, #228b22 100%);
            color: white;
        }
        .stock-card {
            background: linear-gradient(135deg, #ff6b35 0%, #ff9a56 100%);
            color: white;
        }
        .indian-header {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            color: #8b4513;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .tunch-calculator {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #8b4513;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #daa520;
        }
        .btn-primary {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            border: none;
            color: #8b4513;
            font-weight: 600;
        }
        .btn-success {
            background: linear-gradient(135deg, #32cd32 0%, #228b22 100%);
            border: none;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
        }
        .login-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
        }
        .low-stock { background-color: #ffebee; }
        .medium-stock { background-color: #fff3e0; }
        .good-stock { background-color: #e8f5e8; }
    </style>
</head>
<body>

<?php if ($showLogin): ?>
    <!-- LOGIN PAGE -->
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="login-card p-5" style="width: 400px;">
            <div class="text-center mb-4">
                <i class="fas fa-gem fa-3x text-warning mb-3"></i>
                <h2>Indian Jewelry Wholesale</h2>
                <p class="text-muted">Stock • Profit • Tunch Billing</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> Login to Wholesale System
                </button>
            </form>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    Default: admin / admin123<br>
                    <strong>🇮🇳 Indian Jewelry Wholesale Management</strong>
                </small>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- MAIN APPLICATION -->
    <div class="container-fluid">
        <div class="row">
            <!-- SIDEBAR -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-4">
                    <div class="text-center mb-4" style="color: #8b4513;">
                        <i class="fas fa-gem fa-2x mb-2"></i>
                        <h5>Jewelry Wholesale</h5>
                        <small>Management System</small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="?page=dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a class="nav-link <?php echo $page === 'stock' ? 'active' : ''; ?>" href="?page=stock">
                            <i class="fas fa-boxes me-2"></i> Stock Tracking
                        </a>
                        <a class="nav-link <?php echo $page === 'profit' ? 'active' : ''; ?>" href="?page=profit">
                            <i class="fas fa-chart-line me-2"></i> Profit Analysis
                        </a>
                        <a class="nav-link <?php echo $page === 'billing' ? 'active' : ''; ?>" href="?page=billing">
                            <i class="fas fa-calculator me-2"></i> Tunch Billing
                        </a>
                        <hr style="color: #8b4513;">
                        <a class="nav-link" href="?action=logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- MAIN CONTENT -->
            <div class="col-md-10 main-content p-4">
                <!-- Header -->
                <div class="indian-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2><i class="fas fa-gem"></i> <?php echo ucfirst($page); ?></h2>
                            <p class="mb-0">Welcome, <?php echo $_SESSION['username']; ?> | Indian Jewelry Wholesale System</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h6><?php echo date('l, F j, Y'); ?></h6>
                            <small>Stock • Profit • Tunch Calculations</small>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- PAGE CONTENT -->
                <?php if ($page === 'dashboard'): ?>
                    <!-- DASHBOARD -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stock-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-boxes fa-2x mb-3"></i>
                                    <h3><?php echo count($data['stock']); ?></h3>
                                    <p class="mb-0">Stock Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card profit-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-rupee-sign fa-2x mb-3"></i>
                                    <h3>₹<?php echo number_format($data['profit_analysis']['total_profit'] ?? 0, 0); ?></h3>
                                    <p class="mb-0">Total Profit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-percentage fa-2x mb-3"></i>
                                    <h3><?php echo number_format($data['profit_analysis']['avg_profit_margin'] ?? 0, 1); ?>%</h3>
                                    <p class="mb-0">Profit Margin</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-file-invoice fa-2x mb-3"></i>
                                    <h3><?php echo $data['profit_analysis']['total_transactions'] ?? 0; ?></h3>
                                    <p class="mb-0">Total Sales</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QUICK OVERVIEW -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-warehouse"></i> Current Stock Status</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Current Stock</th>
                                                    <th>Stock Value</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['stock'] as $item): ?>
                                                <tr class="<?php echo strtolower(str_replace(' ', '-', $item['stock_status'])); ?>">
                                                    <td><strong><?php echo $item['product_name']; ?></strong></td>
                                                    <td><?php echo number_format($item['current_stock'], 2); ?>g</td>
                                                    <td>₹<?php echo number_format($item['stock_value'], 0); ?></td>
                                                    <td><span class="badge bg-<?php echo $item['stock_status'] === 'Low Stock' ? 'danger' : ($item['stock_status'] === 'Medium Stock' ? 'warning' : 'success'); ?>"><?php echo $item['stock_status']; ?></span></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-line"></i> Recent Sales</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Invoice</th>
                                                    <th>Customer</th>
                                                    <th>Product</th>
                                                    <th>Amount</th>
                                                    <th>Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($data['sales_history'], 0, 5) as $sale): ?>
                                                <tr>
                                                    <td><?php echo $sale['invoice_no']; ?></td>
                                                    <td><?php echo $sale['customer_name']; ?></td>
                                                    <td><?php echo $sale['product_name']; ?></td>
                                                    <td>₹<?php echo number_format($sale['total_amount'], 0); ?></td>
                                                    <td class="text-success">₹<?php echo number_format($sale['profit_amount'], 0); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php elseif ($page === 'stock'): ?>
                    <!-- STOCK TRACKING PAGE -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-warehouse"></i> Stock Tracking & Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <strong>📦 Real-time Stock Management:</strong><br>
                                Track inventory levels, monitor stock movement, and get low stock alerts for your jewelry wholesale business.
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Sl.No</th>
                                            <th>Supplier</th>
                                            <th>Product</th>
                                            <th>Gross Weight</th>
                                            <th>Net Weight</th>
                                            <th>Current Stock</th>
                                            <th>Cost/Gram</th>
                                            <th>Selling Price/Gram</th>
                                            <th>Stock Value</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data['stock'] as $item): ?>
                                        <tr class="<?php echo strtolower(str_replace(' ', '-', $item['stock_status'])); ?>">
                                            <td><strong><?php echo $item['sl_no']; ?></strong></td>
                                            <td><?php echo $item['supplier_name']; ?><br><small class="text-muted"><?php echo $item['location']; ?></small></td>
                                            <td><strong><?php echo $item['product_name']; ?></strong></td>
                                            <td><?php echo number_format($item['gross_weight'], 3); ?>g</td>
                                            <td><?php echo number_format($item['net_weight'], 3); ?>g</td>
                                            <td><strong><?php echo number_format($item['current_stock'], 2); ?>g</strong></td>
                                            <td>₹<?php echo number_format($item['cost_per_gram'], 2); ?></td>
                                            <td>₹<?php echo number_format($item['selling_price_per_gram'], 2); ?></td>
                                            <td><strong>₹<?php echo number_format($item['stock_value'], 0); ?></strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $item['stock_status'] === 'Low Stock' ? 'danger' : ($item['stock_status'] === 'Medium Stock' ? 'warning' : 'success'); ?>">
                                                    <?php echo $item['stock_status']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php elseif ($page === 'profit'): ?>
                    <!-- PROFIT ANALYSIS PAGE -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card profit-card text-center">
                                <div class="card-body">
                                    <h4>₹<?php echo number_format($data['profit_analysis']['total_sales'] ?? 0, 0); ?></h4>
                                    <p class="mb-0">Total Sales</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stock-card text-center">
                                <div class="card-body">
                                    <h4>₹<?php echo number_format($data['profit_analysis']['total_cost'] ?? 0, 0); ?></h4>
                                    <p class="mb-0">Total Cost</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <h4>₹<?php echo number_format($data['profit_analysis']['total_profit'] ?? 0, 0); ?></h4>
                                    <p class="mb-0">Net Profit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card profit-card text-center">
                                <div class="card-body">
                                    <h4><?php echo number_format($data['profit_analysis']['avg_profit_margin'] ?? 0, 1); ?>%</h4>
                                    <p class="mb-0">Profit Margin</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar"></i> Product-wise Profit Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Product</th>
                                            <th>Sales Count</th>
                                            <th>Total Sales</th>
                                            <th>Total Cost</th>
                                            <th>Total Profit</th>
                                            <th>Avg Profit Margin</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data['product_profit'] as $product): ?>
                                        <tr>
                                            <td><strong><?php echo $product['product_name']; ?></strong></td>
                                            <td><?php echo $product['sales_count']; ?></td>
                                            <td>₹<?php echo number_format($product['total_sales'], 0); ?></td>
                                            <td>₹<?php echo number_format($product['total_cost'], 0); ?></td>
                                            <td class="text-success"><strong>₹<?php echo number_format($product['total_profit'], 0); ?></strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $product['avg_profit_margin'] > 90 ? 'success' : ($product['avg_profit_margin'] > 80 ? 'warning' : 'danger'); ?>">
                                                    <?php echo number_format($product['avg_profit_margin'], 1); ?>%
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php elseif ($page === 'billing'): ?>
                    <!-- TUNCH BILLING PAGE -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-calculator"></i> Create New Sale with Tunch Calculation</h5>
                                </div>
                                <div class="card-body">
                                    <div class="tunch-calculator mb-4">
                                        <h6><i class="fas fa-info-circle"></i> Indian Jewelry Tunch Formula:</h6>
                                        <p class="mb-0">
                                            <strong>Net Weight</strong> = Gross Weight - Stone Weight<br>
                                            <strong>Tunch Weight</strong> = Net Weight × (Tunch % ÷ 100)<br>
                                            <strong>Metal Value</strong> = Tunch Weight × Gold Rate<br>
                                            <strong>Total Amount</strong> = Metal Value + Making Charges + Stone Charges
                                        </p>
                                    </div>

                                    <form method="POST">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Invoice Number</label>
                                                    <input type="text" class="form-control" name="invoice_no" value="INV<?php echo date('YmdHis'); ?>" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Customer Name</label>
                                                    <input type="text" class="form-control" name="customer_name" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Customer Location</label>
                                                    <input type="text" class="form-control" name="customer_location" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Select Product</label>
                                                    <select class="form-control" name="product_id" required>
                                                        <option value="">Choose Product</option>
                                                        <?php foreach ($data['stock'] as $item): ?>
                                                        <option value="<?php echo $item['sl_no']; ?>" data-name="<?php echo $item['product_name']; ?>">
                                                            <?php echo $item['product_name']; ?> (Stock: <?php echo number_format($item['current_stock'], 2); ?>g)
                                                        </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                    <input type="hidden" name="product_name" id="product_name">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Gross Weight (grams)</label>
                                                    <input type="number" step="0.001" class="form-control" name="gross_weight" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Stone Weight (grams)</label>
                                                    <input type="number" step="0.001" class="form-control" name="stone_weight" value="0">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Tunch Percentage (%)</label>
                                                    <input type="number" step="0.1" class="form-control" name="tunch_percentage" placeholder="e.g., 96.5" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Gold Rate per Gram (₹)</label>
                                                    <input type="number" step="0.01" class="form-control" name="gold_rate" placeholder="e.g., 6500" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Making Charges (₹)</label>
                                                    <input type="number" step="0.01" class="form-control" name="making_charges" value="0">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Stone Charges (₹)</label>
                                                    <input type="number" step="0.01" class="form-control" name="stone_charges" value="0">
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" name="create_sale" class="btn btn-success btn-lg">
                                            <i class="fas fa-plus"></i> Create Sale & Update Stock
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-history"></i> Recent Sales</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Invoice</th>
                                                    <th>Customer</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($data['sales_history'], 0, 8) as $sale): ?>
                                                <tr>
                                                    <td><?php echo $sale['invoice_no']; ?></td>
                                                    <td><?php echo $sale['customer_name']; ?></td>
                                                    <td>₹<?php echo number_format($sale['total_amount'], 0); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SALES HISTORY -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-invoice-dollar"></i> Sales History with Tunch Calculations</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Invoice</th>
                                            <th>Customer</th>
                                            <th>Product</th>
                                            <th>Net Weight</th>
                                            <th>Tunch %</th>
                                            <th>Tunch Weight</th>
                                            <th>Gold Rate</th>
                                            <th>Metal Value</th>
                                            <th>Total Amount</th>
                                            <th>Profit</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data['sales_history'] as $sale): ?>
                                        <tr>
                                            <td><strong><?php echo $sale['invoice_no']; ?></strong></td>
                                            <td><?php echo $sale['customer_name']; ?><br><small class="text-muted"><?php echo $sale['customer_location']; ?></small></td>
                                            <td><?php echo $sale['product_name']; ?></td>
                                            <td><?php echo number_format($sale['net_weight'], 3); ?>g</td>
                                            <td><?php echo $sale['tunch_percentage']; ?>%</td>
                                            <td><?php echo number_format($sale['tunch_weight'], 3); ?>g</td>
                                            <td>₹<?php echo number_format($sale['gold_rate_per_gram'], 0); ?></td>
                                            <td>₹<?php echo number_format($sale['metal_value'], 0); ?></td>
                                            <td><strong>₹<?php echo number_format($sale['total_amount'], 0); ?></strong></td>
                                            <td class="text-success">₹<?php echo number_format($sale['profit_amount'], 0); ?> (<?php echo number_format($sale['profit_percentage'], 1); ?>%)</td>
                                            <td><?php echo date('d/m/Y', strtotime($sale['sale_date'])); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php else: ?>
                    <div class="alert alert-info">
                        <h5>🇮🇳 Indian Jewelry Wholesale Management System</h5>
                        <p>Navigate using the sidebar to access:</p>
                        <ul>
                            <li><strong>Stock Tracking</strong> - Real-time inventory management</li>
                            <li><strong>Profit Analysis</strong> - Business profitability insights</li>
                            <li><strong>Tunch Billing</strong> - Indian jewelry standard billing</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Auto-fill product name when product is selected
document.querySelector('select[name="product_id"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const productName = selectedOption.getAttribute('data-name');
    document.getElementById('product_name').value = productName || '';
});
</script>
</body>
</html>
