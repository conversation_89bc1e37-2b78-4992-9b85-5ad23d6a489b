<?php
/**
 * Notification Management System
 * Handles creation, display, and management of system notifications
 */

require_once 'config/database.php';

/**
 * Create a new notification
 */
function createNotification($userId, $title, $message, $type = 'info', $actionUrl = null, $expiresAt = null) {
    try {
        $db = getDB();
        
        $stmt = $db->query("INSERT INTO notifications (user_id, title, message, type, action_url, expires_at) VALUES (?, ?, ?, ?, ?, ?)", [
            $userId, $title, $message, $type, $actionUrl, $expiresAt
        ]);
        
        return $db->lastInsertId();
    } catch (Exception $e) {
        logError("Failed to create notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Get unread notifications for a user
 */
function getUnreadNotifications($userId = null, $limit = 10) {
    try {
        $db = getDB();
        
        $sql = "SELECT * FROM notifications WHERE is_read = 0";
        $params = [];
        
        if ($userId) {
            $sql .= " AND (user_id = ? OR user_id IS NULL)";
            $params[] = $userId;
        } else {
            $sql .= " AND user_id IS NULL";
        }
        
        $sql .= " AND (expires_at IS NULL OR expires_at > NOW()) ORDER BY created_at DESC LIMIT ?";
        $params[] = $limit;
        
        return $db->fetchAll($sql, $params);
    } catch (Exception $e) {
        logError("Failed to get notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark notification as read
 */
function markNotificationAsRead($notificationId) {
    try {
        $db = getDB();
        return $db->query("UPDATE notifications SET is_read = 1 WHERE id = ?", [$notificationId]);
    } catch (Exception $e) {
        logError("Failed to mark notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate system notifications based on business rules (optimized for less frequency)
 */
function generateSystemNotifications() {
    try {
        $db = getDB();

        // Only generate notifications once per hour to reduce frequency
        $lastGenerated = $db->fetch("
            SELECT MAX(created_at) as last_time
            FROM notifications
            WHERE title IN ('Low Stock Alert', 'Pending Payments', 'Sales Milestone Achieved!', 'Stagnant Inventory Alert')
        ")['last_time'];

        if ($lastGenerated && strtotime($lastGenerated) > strtotime('-1 hour')) {
            return; // Skip if notifications were generated in the last hour
        }

        // Check for low stock items (only if critical - less than 5 items)
        $criticalStockItems = $db->fetchAll("
            SELECT p.product_name, i.quantity_in_stock, i.minimum_stock_level
            FROM products p
            JOIN inventory i ON p.id = i.product_id
            WHERE i.quantity_in_stock <= GREATEST(i.minimum_stock_level, 5)
            AND i.minimum_stock_level > 0
            AND p.is_active = 1
        ");

        if (count($criticalStockItems) >= 3) { // Only notify if 3+ items are critically low
            $count = count($criticalStockItems);
            $title = "Critical Stock Alert";
            $message = "$count product(s) are critically low on stock and need immediate restocking.";

            // Check if this notification already exists today
            $existing = $db->fetch("
                SELECT id FROM notifications
                WHERE title = ? AND DATE(created_at) = CURDATE() AND is_read = 0
            ", [$title]);

            if (!$existing) {
                createNotification(null, $title, $message, 'error', 'inventory.php?filter=low_stock');
            }
        }
        
        // Check for high value pending sales (if any)
        $pendingSales = $db->fetchAll("
            SELECT COUNT(*) as count, SUM(grand_total) as total
            FROM sales 
            WHERE payment_status = 'pending' AND is_cancelled = 0
        ");
        
        if (!empty($pendingSales) && $pendingSales[0]['count'] > 0) {
            $count = $pendingSales[0]['count'];
            $total = $pendingSales[0]['total'];
            $title = "Pending Payments";
            $message = "$count sale(s) with total value of " . formatCurrency($total) . " are pending payment.";
            
            $existing = $db->fetch("
                SELECT id FROM notifications 
                WHERE title = ? AND DATE(created_at) = CURDATE() AND is_read = 0
            ", [$title]);
            
            if (!$existing) {
                createNotification(null, $title, $message, 'warning', 'sales.php?status=pending');
            }
        }
        
        // Check for significant sales milestones (only major ones)
        $todaySales = $db->fetch("
            SELECT COALESCE(SUM(grand_total), 0) as total
            FROM sales
            WHERE DATE(sale_date) = CURDATE() AND is_cancelled = 0
        ")['total'];

        // Only notify for significant milestones: 1 lakh, 2 lakh, 5 lakh, 10 lakh
        $milestones = [100000, 200000, 500000, 1000000];
        foreach ($milestones as $milestone) {
            if ($todaySales >= $milestone) {
                $title = "Sales Milestone: " . formatCurrency($milestone);
                $message = "Congratulations! Today's sales have reached " . formatCurrency($todaySales) . ".";

                $existing = $db->fetch("
                    SELECT id FROM notifications
                    WHERE title = ? AND DATE(created_at) = CURDATE()
                ", [$title]);

                if (!$existing) {
                    createNotification(null, $title, $message, 'success', 'sales.php');
                    break; // Only create one milestone notification per day
                }
            }
        }
        
        // Check for products without recent sales (stagnant inventory)
        $stagnantProducts = $db->fetchAll("
            SELECT p.product_name, p.id
            FROM products p
            LEFT JOIN sale_items si ON p.id = si.product_id
            LEFT JOIN sales s ON si.sale_id = s.id AND s.sale_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            WHERE p.is_active = 1 AND s.id IS NULL
            GROUP BY p.id, p.product_name
            LIMIT 5
        ");
        
        if (count($stagnantProducts) >= 3) {
            $count = count($stagnantProducts);
            $title = "Stagnant Inventory Alert";
            $message = "$count products haven't sold in the last 30 days. Consider promotional strategies.";
            
            $existing = $db->fetch("
                SELECT id FROM notifications 
                WHERE title = ? AND DATE(created_at) = CURDATE() AND is_read = 0
            ", [$title]);
            
            if (!$existing) {
                createNotification(null, $title, $message, 'info', 'products.php');
            }
        }
        
    } catch (Exception $e) {
        logError("Failed to generate system notifications: " . $e->getMessage());
    }
}

/**
 * Clean up old notifications
 */
function cleanupOldNotifications($daysOld = 30) {
    try {
        $db = getDB();
        
        // Delete read notifications older than specified days
        $db->query("
            DELETE FROM notifications 
            WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ", [$daysOld]);
        
        // Delete expired notifications
        $db->query("
            DELETE FROM notifications 
            WHERE expires_at IS NOT NULL AND expires_at < NOW()
        ");
        
    } catch (Exception $e) {
        logError("Failed to cleanup notifications: " . $e->getMessage());
    }
}

/**
 * Get notification count for a user
 */
function getUnreadNotificationCount($userId = null) {
    try {
        $db = getDB();
        
        $sql = "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0";
        $params = [];
        
        if ($userId) {
            $sql .= " AND (user_id = ? OR user_id IS NULL)";
            $params[] = $userId;
        } else {
            $sql .= " AND user_id IS NULL";
        }
        
        $sql .= " AND (expires_at IS NULL OR expires_at > NOW())";
        
        return $db->fetch($sql, $params)['count'];
    } catch (Exception $e) {
        logError("Failed to get notification count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Display notifications HTML
 */
function displayNotifications($notifications) {
    if (empty($notifications)) {
        return '<div class="text-center py-4 text-muted">
                    <i class="fas fa-bell-slash fa-2x mb-2 opacity-25"></i>
                    <p>No new notifications</p>
                </div>';
    }
    
    $html = '';
    foreach ($notifications as $notification) {
        $iconClass = [
            'info' => 'fas fa-info-circle text-info',
            'success' => 'fas fa-check-circle text-success',
            'warning' => 'fas fa-exclamation-triangle text-warning',
            'error' => 'fas fa-times-circle text-danger'
        ][$notification['type']] ?? 'fas fa-bell text-primary';
        
        $html .= '<div class="notification-item d-flex align-items-start p-3 border-bottom">';
        $html .= '<div class="notification-icon me-3"><i class="' . $iconClass . '"></i></div>';
        $html .= '<div class="notification-content flex-grow-1">';
        $html .= '<h6 class="notification-title mb-1">' . htmlspecialchars($notification['title']) . '</h6>';
        $html .= '<p class="notification-message mb-1 text-muted small">' . htmlspecialchars($notification['message']) . '</p>';
        $html .= '<small class="text-muted">' . formatDateTime($notification['created_at']) . '</small>';
        $html .= '</div>';
        
        if ($notification['action_url']) {
            $html .= '<div class="notification-action">';
            $html .= '<a href="' . htmlspecialchars($notification['action_url']) . '" class="btn btn-sm btn-outline-primary">View</a>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
    }
    
    return $html;
}
?>
