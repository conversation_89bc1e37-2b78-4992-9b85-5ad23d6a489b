# Indian Jewellery Wholesale Management System v2.0

A comprehensive, modern web-based management system designed specifically for Indian jewellery wholesale businesses. Built with PHP, MySQL, and modern web technologies.

## 🌟 Features

### ✨ **Modern UI/UX**
- **Responsive Sidebar Navigation** - Collapsible sidebar with organized menu structure
- **Mobile-First Design** - Works perfectly on all devices
- **Professional Theme** - Modern gradient design with smooth animations
- **Dark/Light Mode Support** - Comfortable viewing in any environment

### 💎 **Core Functionality**
- **Product Management** - Complete jewellery inventory with categories, weights, and specifications
- **Customer Management** - Detailed customer profiles with credit management
- **Sales & Billing** - Professional invoicing with PDF generation
- **Inventory Tracking** - Real-time stock management with low stock alerts
- **Metal Rates** - Daily gold, silver, and platinum rate management
- **Reports & Analytics** - Comprehensive business insights and reports

### 🔧 **Advanced Features**
- **PDF Invoice Generation** - Professional invoices using jsPDF
- **Real-time Notifications** - System alerts and updates
- **Audit Logging** - Complete activity tracking
- **Multi-user Support** - Role-based access control
- **Data Export** - Excel/CSV export capabilities
- **Backup & Restore** - Automated database backups

## 🚀 **Quick Start**

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Installation

1. **Download & Extract**
   ```bash
   # Extract the v2 folder to your web server directory
   # Example: /var/www/html/jewellery-v2/ or C:\xampp\htdocs\jewellery-v2\
   ```

2. **Run Installation**
   ```
   Open your browser and navigate to:
   http://localhost/jewellery-v2/install.php
   ```

3. **Follow Installation Steps**
   - **Step 1**: Configure database connection
   - **Step 2**: Install database tables and default data
   - **Step 3**: Complete setup

4. **Login**
   ```
   Default Credentials:
   Username: admin
   Password: admin123
   ```

## 📱 **System Requirements**

### Server Requirements
- **PHP**: 7.4+ with PDO MySQL extension
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: 512MB RAM minimum
- **Storage**: 1GB free space

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🎯 **Key Components**

### 1. **Enhanced Sidebar Navigation**
```
📁 Main
  └── Dashboard

📁 Inventory
  ├── Products
  ├── Categories
  ├── Stock Management
  └── Suppliers

📁 Sales
  ├── New Sale
  ├── Sales History
  └── Customers

📁 Finance
  ├── Metal Rates
  └── Reports
      ├── Sales Report
      ├── Inventory Report
      └── Customer Report

📁 System
  ├── Settings
  ├── Users
  └── Backup
```

### 2. **Modern Dashboard**
- **Real-time Statistics** - Products, customers, sales, stock alerts
- **Quick Actions** - Fast access to common tasks
- **Sales Charts** - Visual analytics with Chart.js
- **Recent Activity** - Latest sales and notifications
- **Low Stock Alerts** - Automatic inventory warnings

### 3. **Professional Product Management**
- **Detailed Product Info** - Name, code, description, category
- **Metal Specifications** - Type, purity, weights (base, stone, net)
- **Pricing Structure** - Stone cost, making charges, tax rates
- **Inventory Integration** - Stock levels, minimum thresholds
- **HSN Code Support** - GST compliance

### 4. **Advanced Features**
- **PDF Generation** - Professional invoices with company branding
- **Real-time Updates** - Live notifications and alerts
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Keyboard Shortcuts** - Ctrl+B (toggle sidebar), Ctrl+N (new sale)

## 🔧 **Configuration**

### Database Configuration
Located in `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'jewellery_wholesale_v2');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### Application Settings
```php
define('APP_NAME', 'Indian Jewellery Wholesale');
define('CURRENCY_SYMBOL', '₹');
define('DEFAULT_TIMEZONE', 'Asia/Kolkata');
define('RECORDS_PER_PAGE', 25);
```

## 📊 **Database Schema**

### Core Tables
- **users** - System users and authentication
- **products** - Jewellery inventory
- **categories** - Product categorization
- **customers** - Customer database
- **suppliers** - Supplier management
- **sales** - Sales transactions
- **sale_items** - Individual sale items
- **inventory** - Stock management
- **metal_rates** - Daily metal prices
- **settings** - System configuration

### Advanced Tables
- **stock_movements** - Inventory tracking
- **notifications** - System alerts
- **audit_logs** - Activity logging

## 🎨 **Customization**

### CSS Variables
Located in `assets/css/app.css`:
```css
:root {
    --primary: #667eea;
    --secondary: #764ba2;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
}
```

### Sidebar Menu
Modify `includes/sidebar.php` to customize navigation structure.

## 🔒 **Security Features**

- **SQL Injection Protection** - Prepared statements
- **XSS Prevention** - Input sanitization
- **CSRF Protection** - Token validation
- **Session Management** - Secure session handling
- **Password Hashing** - bcrypt encryption
- **Input Validation** - Server-side validation

## 📈 **Performance**

- **Optimized Queries** - Indexed database tables
- **Lazy Loading** - Efficient data loading
- **Caching** - Browser and server-side caching
- **Minified Assets** - Compressed CSS/JS
- **CDN Integration** - Fast asset delivery

## 🛠️ **Development**

### File Structure
```
v2/
├── assets/
│   ├── css/app.css          # Main stylesheet
│   └── js/app.js            # Main JavaScript
├── config/
│   └── database.php         # Database configuration
├── database/
│   └── schema.sql           # Database schema
├── includes/
│   └── sidebar.php          # Sidebar component
├── index.php                # Dashboard
├── products.php             # Product management
├── install.php              # Installation script
└── README.md                # This file
```

### Adding New Features
1. Create new PHP file in root directory
2. Include `config/database.php`
3. Use `includes/sidebar.php` for layout
4. Follow existing code patterns

## 🆘 **Support**

### Common Issues
1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running

2. **Sidebar Not Showing**
   - Clear browser cache
   - Check JavaScript console for errors

3. **Permission Errors**
   - Set proper file permissions (755 for directories, 644 for files)

### Getting Help
- Check the installation guide
- Review error logs in `logs/` directory
- Ensure all requirements are met

## 📝 **License**

This software is proprietary and designed specifically for Indian jewellery wholesale businesses.

## 🎉 **What's New in v2.0**

- ✅ **Complete UI Overhaul** - Modern, responsive design
- ✅ **Enhanced Sidebar** - Collapsible navigation with better UX
- ✅ **Improved Database** - Comprehensive schema with relationships
- ✅ **PDF Generation** - Professional invoice generation
- ✅ **Real-time Features** - Live notifications and updates
- ✅ **Mobile Responsive** - Works perfectly on all devices
- ✅ **Better Security** - Enhanced authentication and validation
- ✅ **Performance Optimized** - Faster loading and better caching

---

**Ready to revolutionize your jewellery business management!** 💎✨
