<?php
/**
 * Test PDF Generation - Comprehensive Testing
 */

require_once 'config/database.php';

// Load PDF classes if available
$enhancedPDFAvailable = file_exists('lib/EnhancedPDF.php');
if ($enhancedPDFAvailable) {
    require_once 'lib/EnhancedPDF.php';
}

try {
    $db = getDB();
    
    echo "<h2>PDF Generation Testing</h2>";
    
    // Check PDF library status
    echo "<h3>PDF Library Status</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 10px;'>Component</th><th style='padding: 10px;'>Status</th><th style='padding: 10px;'>Description</th></tr>";
    
    // Check TCPDF
    $tcpdfPath = 'vendor/tecnickcom/tcpdf/tcpdf.php';
    $tcpdfAvailable = file_exists($tcpdfPath);
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>TCPDF Library</strong></td>";
    echo "<td style='padding: 10px; color: " . ($tcpdfAvailable ? 'green' : 'red') . ";'>" . ($tcpdfAvailable ? '✅ Available' : '❌ Not Found') . "</td>";
    echo "<td style='padding: 10px;'>" . ($tcpdfAvailable ? 'Professional PDF generation available' : 'Install with: composer require tecnickcom/tcpdf') . "</td>";
    echo "</tr>";
    
    // Check Enhanced PDF class
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Enhanced PDF Class</strong></td>";
    echo "<td style='padding: 10px; color: " . ($enhancedPDFAvailable ? 'green' : 'red') . ";'>" . ($enhancedPDFAvailable ? '✅ Available' : '❌ Not Found') . "</td>";
    echo "<td style='padding: 10px;'>" . ($enhancedPDFAvailable ? 'Custom PDF wrapper available' : 'Run create-proper-pdf.php to create') . "</td>";
    echo "</tr>";
    
    // Check if EnhancedPDF class works
    $enhancedPDFWorks = false;
    if ($enhancedPDFAvailable) {
        try {
            $testPDF = new EnhancedPDF();
            $enhancedPDFWorks = true;
        } catch (Exception $e) {
            $enhancedPDFWorks = false;
        }
    }
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>PDF Class Functionality</strong></td>";
    echo "<td style='padding: 10px; color: " . ($enhancedPDFWorks ? 'green' : 'orange') . ";'>" . ($enhancedPDFWorks ? '✅ Working' : '⚠️ Fallback Mode') . "</td>";
    echo "<td style='padding: 10px;'>" . ($enhancedPDFWorks ? 'PDF generation fully functional' : 'Using enhanced HTML method') . "</td>";
    echo "</tr>";
    
    echo "</table>";
    
    // Get sales for testing
    $sales = $db->fetchAll("SELECT id, bill_number, sale_date, grand_total FROM sales ORDER BY id DESC LIMIT 5");
    
    if (count($sales) == 0) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ No Sales Data</h4>";
        echo "<p>No sales records found. Create a sale first to test PDF generation.</p>";
        echo "<p><a href='billing.php' style='background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Create New Sale</a></p>";
        echo "</div>";
    } else {
        echo "<h3>Test PDF Generation</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Sale ID</th>";
        echo "<th style='padding: 10px;'>Bill Number</th>";
        echo "<th style='padding: 10px;'>Date</th>";
        echo "<th style='padding: 10px;'>Amount</th>";
        echo "<th style='padding: 10px;'>PDF Tests</th>";
        echo "</tr>";
        
        foreach ($sales as $sale) {
            echo "<tr>";
            echo "<td style='padding: 10px; text-align: center;'>{$sale['id']}</td>";
            echo "<td style='padding: 10px;'>{$sale['bill_number']}</td>";
            echo "<td style='padding: 10px;'>" . date('d/m/Y', strtotime($sale['sale_date'])) . "</td>";
            echo "<td style='padding: 10px; text-align: right;'>₹" . number_format($sale['grand_total'], 2) . "</td>";
            echo "<td style='padding: 10px; text-align: center;'>";
            
            // Different PDF generation methods
            echo "<a href='generate-pdf.php?type=sale&id={$sale['id']}' target='_blank' style='background-color: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px; display: inline-block;'>Enhanced PDF</a><br>";
            echo "<a href='print-bill.php?id={$sale['id']}' target='_blank' style='background-color: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px; display: inline-block;'>Print View</a><br>";
            
            if ($tcpdfAvailable) {
                echo "<a href='test-tcpdf.php?id={$sale['id']}' target='_blank' style='background-color: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px; display: inline-block;'>TCPDF Test</a>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Installation guide
    echo "<h3>📋 PDF Enhancement Guide</h3>";
    
    if (!$tcpdfAvailable) {
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🚀 Upgrade to Professional PDF</h4>";
        echo "<p>For the best PDF generation experience, install TCPDF:</p>";
        
        echo "<h5>Method 1: Composer (Recommended)</h5>";
        echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;'>";
        echo "composer require tecnickcom/tcpdf";
        echo "</div>";
        
        echo "<h5>Method 2: Manual Installation</h5>";
        echo "<ol>";
        echo "<li>Download TCPDF from <a href='https://tcpdf.org/' target='_blank'>https://tcpdf.org/</a></li>";
        echo "<li>Extract to <code>vendor/tecnickcom/tcpdf/</code></li>";
        echo "<li>Ensure the main file is at <code>vendor/tecnickcom/tcpdf/tcpdf.php</code></li>";
        echo "<li>Refresh this page to test</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ Professional PDF Ready</h4>";
        echo "<p>TCPDF is installed and ready for professional PDF generation!</p>";
        echo "</div>";
    }
    
    // Current capabilities
    echo "<h3>📊 Current PDF Capabilities</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    
    if ($tcpdfAvailable && $enhancedPDFWorks) {
        echo "<h4>🎯 Professional Mode</h4>";
        echo "<ul>";
        echo "<li>✅ True PDF generation (not HTML conversion)</li>";
        echo "<li>✅ Vector graphics and text</li>";
        echo "<li>✅ Proper page breaks and formatting</li>";
        echo "<li>✅ Embedded fonts and styling</li>";
        echo "<li>✅ Optimized file size</li>";
        echo "<li>✅ Print-ready quality</li>";
        echo "</ul>";
    } else {
        echo "<h4>📄 Enhanced HTML Mode</h4>";
        echo "<ul>";
        echo "<li>✅ Professional bill layout</li>";
        echo "<li>✅ Print-optimized styling</li>";
        echo "<li>✅ Browser PDF generation</li>";
        echo "<li>✅ Cross-platform compatibility</li>";
        echo "<li>⚠️ Requires browser for PDF conversion</li>";
        echo "</ul>";
    }
    echo "</div>";
    
    // Quick actions
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>Quick Actions</h3>";
    if (!$enhancedPDFAvailable) {
        echo "<a href='create-proper-pdf.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px; display: inline-block;'>Setup PDF Classes</a>";
    }
    echo "<a href='sales.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px; display: inline-block;'>Go to Sales</a>";
    echo "<a href='billing.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px; display: inline-block;'>Create New Sale</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
