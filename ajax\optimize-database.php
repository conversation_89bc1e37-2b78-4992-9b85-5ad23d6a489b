<?php
/**
 * Database Optimization AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Get database name
    $dbName = $db->fetch("SELECT DATABASE() as db_name")['db_name'];
    
    // Get all tables
    $tables = $db->fetchAll("SHOW TABLES");
    $tableKey = "Tables_in_$dbName";
    
    $optimizedTables = [];
    $totalSizeBefore = 0;
    $totalSizeAfter = 0;
    
    foreach ($tables as $table) {
        $tableName = $table[$tableKey];
        
        // Get table size before optimization
        $sizeInfo = $db->fetch("
            SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = ? AND table_name = ?
        ", [$dbName, $tableName]);
        
        $sizeBefore = $sizeInfo['size_mb'] ?? 0;
        $totalSizeBefore += $sizeBefore;
        
        // Optimize table
        $result = $db->query("OPTIMIZE TABLE `$tableName`");
        
        // Get table size after optimization
        $sizeInfo = $db->fetch("
            SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = ? AND table_name = ?
        ", [$dbName, $tableName]);
        
        $sizeAfter = $sizeInfo['size_mb'] ?? 0;
        $totalSizeAfter += $sizeAfter;
        
        $optimizedTables[] = [
            'table' => $tableName,
            'size_before' => $sizeBefore,
            'size_after' => $sizeAfter,
            'saved' => $sizeBefore - $sizeAfter,
            'status' => 'optimized'
        ];
    }
    
    // Additional cleanup operations
    
    // Clean up old notifications (older than 30 days)
    $oldNotifications = $db->query("
        DELETE FROM notifications 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    
    // Clean up old system logs (older than 90 days)
    $oldLogs = $db->query("
        DELETE FROM system_logs 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
    ");
    
    // Update table statistics
    $db->query("ANALYZE TABLE products, categories, customers, suppliers, sales, inventory");
    
    // Calculate total savings
    $totalSaved = $totalSizeBefore - $totalSizeAfter;
    
    // Log the optimization
    $db->query("
        INSERT INTO system_logs (log_type, level, message, context, created_at) 
        VALUES ('system', 'info', 'Database optimization completed', ?, NOW())
    ", [json_encode([
        'tables_optimized' => count($optimizedTables),
        'size_before' => $totalSizeBefore . 'MB',
        'size_after' => $totalSizeAfter . 'MB',
        'space_saved' => $totalSaved . 'MB'
    ])]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Database optimization completed successfully',
        'results' => [
            'tables_optimized' => count($optimizedTables),
            'size_before' => round($totalSizeBefore, 2) . ' MB',
            'size_after' => round($totalSizeAfter, 2) . ' MB',
            'space_saved' => round($totalSaved, 2) . ' MB',
            'old_notifications_cleaned' => $oldNotifications,
            'old_logs_cleaned' => $oldLogs
        ],
        'table_details' => $optimizedTables
    ]);
    
} catch (Exception $e) {
    // Log the error
    try {
        $db->query("
            INSERT INTO system_logs (log_type, level, message, context, created_at) 
            VALUES ('system', 'error', 'Database optimization failed', ?, NOW())
        ", [json_encode(['error' => $e->getMessage()])]);
    } catch (Exception $logError) {
        // Ignore logging errors
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
