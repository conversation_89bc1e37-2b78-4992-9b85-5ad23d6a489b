<?php
/**
 * Export Categories Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Check if specific categories are selected
    $selectedCategories = $_POST['selected_categories'] ?? [];
    
    $whereClause = "c.is_active = 1";
    $params = [];
    
    if (!empty($selectedCategories)) {
        $placeholders = str_repeat('?,', count($selectedCategories) - 1) . '?';
        $whereClause .= " AND c.id IN ($placeholders)";
        $params = $selectedCategories;
    }

    // Get categories data with comprehensive information
    $categories = $db->fetchAll("
        SELECT c.*, 
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count,
               (SELECT COUNT(*) FROM categories WHERE parent_id = c.id AND is_active = 1) as subcategory_count,
               (SELECT COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0) 
                FROM products pr 
                JOIN inventory i ON pr.id = i.product_id 
                WHERE pr.category_id = c.id AND pr.is_active = 1) as total_stock_value
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE $whereClause
        ORDER BY c.sort_order, c.category_name
    ", $params);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'categories_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Category ID', 'Category Name', 'Parent Category', 'Description', 
            'Sort Order', 'Product Count', 'Subcategory Count', 'Total Stock Value (₹)', 'Created Date'
        ];

        fputcsv($output, $headers);

        foreach ($categories as $category) {
            $row = [
                $category['id'],
                $category['category_name'],
                $category['parent_name'] ?? 'Main Category',
                $category['description'],
                $category['sort_order'],
                $category['product_count'],
                $category['subcategory_count'],
                $category['total_stock_value'],
                date('d/m/Y H:i:s', strtotime($category['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'categories_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // Group categories by parent
        $mainCategories = [];
        $subCategories = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id']) {
                $subCategories[] = $category;
            } else {
                $mainCategories[] = $category;
            }
        }
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Categories Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .main-category { background-color: #e3f2fd; font-weight: bold; }
                .sub-category { background-color: #f3e5f5; padding-left: 20px; }
                .stats { color: #666; font-size: 11px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Categories Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>
            
            <h3>Main Categories</h3>
            <table>
                <thead>
                    <tr>
                        <th>Category Name</th>
                        <th>Description</th>
                        <th>Products</th>
                        <th>Subcategories</th>
                        <th>Stock Value</th>
                        <th>Sort Order</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($mainCategories as $category) {
            echo "<tr class='main-category'>
                    <td><strong>" . htmlspecialchars($category['category_name']) . "</strong></td>
                    <td>" . htmlspecialchars($category['description'] ?? '-') . "</td>
                    <td>" . $category['product_count'] . "</td>
                    <td>" . $category['subcategory_count'] . "</td>
                    <td>₹" . number_format($category['total_stock_value'], 2) . "</td>
                    <td>" . $category['sort_order'] . "</td>
                  </tr>";
        }
        
        echo "</tbody>
            </table>";
        
        if (!empty($subCategories)) {
            echo "<h3>Subcategories</h3>
            <table>
                <thead>
                    <tr>
                        <th>Category Name</th>
                        <th>Parent Category</th>
                        <th>Description</th>
                        <th>Products</th>
                        <th>Stock Value</th>
                        <th>Sort Order</th>
                    </tr>
                </thead>
                <tbody>";
            
            foreach ($subCategories as $category) {
                echo "<tr class='sub-category'>
                        <td>" . htmlspecialchars($category['category_name']) . "</td>
                        <td>" . htmlspecialchars($category['parent_name']) . "</td>
                        <td>" . htmlspecialchars($category['description'] ?? '-') . "</td>
                        <td>" . $category['product_count'] . "</td>
                        <td>₹" . number_format($category['total_stock_value'], 2) . "</td>
                        <td>" . $category['sort_order'] . "</td>
                      </tr>";
            }
            
            echo "</tbody>
            </table>";
        }
        
        echo "<script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'categories_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Categories Report</title>
        </head>
        <body>
            <h1>Categories Report</h1>
            <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            
            <table border='1'>
                <tr>
                    <th>Category ID</th>
                    <th>Category Name</th>
                    <th>Parent Category</th>
                    <th>Description</th>
                    <th>Sort Order</th>
                    <th>Product Count</th>
                    <th>Subcategory Count</th>
                    <th>Total Stock Value</th>
                    <th>Created Date</th>
                </tr>";

        foreach ($categories as $category) {
            echo "<tr>
                    <td>" . $category['id'] . "</td>
                    <td>" . htmlspecialchars($category['category_name']) . "</td>
                    <td>" . htmlspecialchars($category['parent_name'] ?? 'Main Category') . "</td>
                    <td>" . htmlspecialchars($category['description'] ?? '') . "</td>
                    <td>" . $category['sort_order'] . "</td>
                    <td>" . $category['product_count'] . "</td>
                    <td>" . $category['subcategory_count'] . "</td>
                    <td>" . $category['total_stock_value'] . "</td>
                    <td>" . date('d/m/Y H:i:s', strtotime($category['created_at'])) . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting categories: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='categories.php'>← Back to Categories</a></p>";
    echo "</div>";
}
?>
