<?php
/**
 * Notification Settings - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';
require_once 'includes/notifications.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$success = '';
$error = '';

// Handle form submission
if ($_POST) {
    try {
        $settings = [
            'notification_frequency' => $_POST['notification_frequency'] ?? 'normal',
            'low_stock_alerts' => isset($_POST['low_stock_alerts']) ? '1' : '0',
            'sales_milestones' => isset($_POST['sales_milestones']) ? '1' : '0',
            'payment_reminders' => isset($_POST['payment_reminders']) ? '1' : '0',
            'inventory_alerts' => isset($_POST['inventory_alerts']) ? '1' : '0',
            'system_updates' => isset($_POST['system_updates']) ? '1' : '0',
            'auto_cleanup_days' => intval($_POST['auto_cleanup_days'] ?? 30),
            'notification_sound' => isset($_POST['notification_sound']) ? '1' : '0'
        ];
        
        foreach ($settings as $key => $value) {
            $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
            
            if ($existing) {
                $db->query("UPDATE settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            } else {
                $db->query("
                    INSERT INTO settings (setting_key, setting_value, setting_type, description, category) 
                    VALUES (?, ?, 'notification', ?, 'notifications')
                ", [$key, $value, "Notification setting: $key"]);
            }
        }
        
        $success = 'Notification settings updated successfully!';
        
    } catch (Exception $e) {
        $error = 'Failed to update settings: ' . $e->getMessage();
    }
}

// Get current settings
$currentSettings = [];
$settingKeys = ['notification_frequency', 'low_stock_alerts', 'sales_milestones', 'payment_reminders', 
                'inventory_alerts', 'system_updates', 'auto_cleanup_days', 'notification_sound'];

foreach ($settingKeys as $key) {
    $setting = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = ?", [$key]);
    $currentSettings[$key] = $setting['setting_value'] ?? '';
}

// Set defaults
$currentSettings['notification_frequency'] = $currentSettings['notification_frequency'] ?: 'normal';
$currentSettings['auto_cleanup_days'] = $currentSettings['auto_cleanup_days'] ?: '30';

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Settings - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Notification Settings</h2>
                    <p class="text-muted mb-0">Configure how and when you receive notifications</p>
                </div>
                <a href="settings.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- General Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>General Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Notification Frequency</label>
                                    <select class="form-select" name="notification_frequency">
                                        <option value="minimal" <?php echo $currentSettings['notification_frequency'] === 'minimal' ? 'selected' : ''; ?>>
                                            Minimal - Only critical alerts
                                        </option>
                                        <option value="normal" <?php echo $currentSettings['notification_frequency'] === 'normal' ? 'selected' : ''; ?>>
                                            Normal - Important notifications
                                        </option>
                                        <option value="all" <?php echo $currentSettings['notification_frequency'] === 'all' ? 'selected' : ''; ?>>
                                            All - Every notification
                                        </option>
                                    </select>
                                    <small class="text-muted">Controls how many notifications you receive</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Auto-cleanup Period</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="auto_cleanup_days" 
                                               value="<?php echo htmlspecialchars($currentSettings['auto_cleanup_days']); ?>" 
                                               min="1" max="365">
                                        <span class="input-group-text">days</span>
                                    </div>
                                    <small class="text-muted">Automatically delete read notifications after this many days</small>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="notification_sound" 
                                           <?php echo $currentSettings['notification_sound'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        Enable notification sounds
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Types -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notification Types</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="low_stock_alerts" 
                                           <?php echo $currentSettings['low_stock_alerts'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        <strong>Low Stock Alerts</strong>
                                        <br><small class="text-muted">When products are running low on inventory</small>
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="sales_milestones" 
                                           <?php echo $currentSettings['sales_milestones'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        <strong>Sales Milestones</strong>
                                        <br><small class="text-muted">When daily sales reach significant amounts</small>
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="payment_reminders" 
                                           <?php echo $currentSettings['payment_reminders'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        <strong>Payment Reminders</strong>
                                        <br><small class="text-muted">When customers have pending payments</small>
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="inventory_alerts" 
                                           <?php echo $currentSettings['inventory_alerts'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        <strong>Inventory Alerts</strong>
                                        <br><small class="text-muted">Stagnant inventory and stock issues</small>
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="system_updates" 
                                           <?php echo $currentSettings['system_updates'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">
                                        <strong>System Updates</strong>
                                        <br><small class="text-muted">System maintenance and update notifications</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Notifications Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Notification Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="h4 text-primary"><?php echo getUnreadNotificationCount(); ?></div>
                                <small class="text-muted">Unread Notifications</small>
                            </div>
                            <div class="col-md-3">
                                <div class="h4 text-success">
                                    <?php 
                                    $todayCount = $db->fetch("SELECT COUNT(*) as count FROM notifications WHERE DATE(created_at) = CURDATE()")['count'];
                                    echo $todayCount;
                                    ?>
                                </div>
                                <small class="text-muted">Today's Notifications</small>
                            </div>
                            <div class="col-md-3">
                                <div class="h4 text-warning">
                                    <?php 
                                    $weekCount = $db->fetch("SELECT COUNT(*) as count FROM notifications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'];
                                    echo $weekCount;
                                    ?>
                                </div>
                                <small class="text-muted">This Week</small>
                            </div>
                            <div class="col-md-3">
                                <div class="h4 text-info">
                                    <?php 
                                    $readCount = $db->fetch("SELECT COUNT(*) as count FROM notifications WHERE is_read = 1 AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['count'];
                                    echo $readCount;
                                    ?>
                                </div>
                                <small class="text-muted">Read (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-warning" onclick="cleanupNotifications()">
                            <i class="fas fa-broom me-2"></i>Cleanup Old Notifications
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-2"></i>Mark All as Read
                        </button>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function cleanupNotifications() {
            if (confirm('This will delete all read notifications older than the specified cleanup period. Continue?')) {
                fetch('ajax/cleanup-notifications.php', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Cleaned up ${data.deleted} old notifications`);
                        location.reload();
                    } else {
                        alert('Cleanup failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }

        function markAllAsRead() {
            if (confirm('Mark all notifications as read?')) {
                fetch('ajax/mark_notifications_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({action: 'mark_all_read'})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('All notifications marked as read');
                        location.reload();
                    } else {
                        alert('Failed to mark notifications as read');
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
