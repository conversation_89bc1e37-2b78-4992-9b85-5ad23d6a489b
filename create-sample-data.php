<?php
/**
 * Create Sample Data for Testing
 */

require_once 'config/database.php';

try {
    $db = getDB();
    $db->beginTransaction();
    
    echo "<h1>🔧 Creating Sample Data</h1>";
    
    // 1. Create sample suppliers
    echo "<h2>🚚 Creating Suppliers...</h2>";
    $suppliers = [
        ['<PERSON>esh Jewellers', '<PERSON><PERSON>', '9876543210', 'r<PERSON><PERSON>@jewellers.com', 'Mumbai', 'Maharashtra', '27ABCDE1234F1Z5'],
        ['Golden Crafts', 'Amit Shah', '9876543211', '<EMAIL>', 'Surat', 'Gujarat', '24BCDEF2345G2Z6'],
        ['Diamond Palace', 'Suresh Patel', '9876543212', '<EMAIL>', 'Jaipur', 'Rajasthan', '08CDEFG3456H3Z7']
    ];
    
    foreach ($suppliers as $supplier) {
        $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, city, state, gst_number, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)", $supplier);
        echo "<p>✅ Added supplier: {$supplier[0]}</p>";
    }
    
    // 2. Create sample products
    echo "<h2>💎 Creating Products...</h2>";
    $products = [
        ['Gold Chain 22K', 'GC001', 1, 1, 'Gold', '22K', 15.500, 0.000, 15.500, 0, 800, 5800, '71171900'],
        ['Silver Bangle', 'SB001', 2, 1, 'Silver', '925', 25.000, 0.000, 25.000, 0, 200, 85, '71131900'],
        ['Diamond Ring', 'DR001', 4, 1, 'Gold', '18K', 8.500, 0.500, 8.000, 15000, 1200, 4650, '71131100'],
        ['Gold Earrings', 'GE001', 3, 1, 'Gold', '22K', 12.000, 0.000, 12.000, 0, 600, 5800, '71171900'],
        ['Platinum Necklace', 'PN001', 5, 2, 'Platinum', '950', 35.000, 2.000, 33.000, 25000, 2000, 3200, '71171900']
    ];
    
    foreach ($products as $product) {
        $db->query("INSERT INTO products (product_name, product_code, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $product);
        $productId = $db->lastInsertId();
        
        // Create inventory record
        $stock = rand(5, 50);
        $costPrice = ($product[9] + ($product[10] * $product[11])) * 1.1; // stone_cost + (weight * rate) * margin
        $sellingPrice = $costPrice * 1.3; // 30% markup
        
        $db->query("INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, minimum_stock_level, reorder_point, location) VALUES (?, ?, ?, ?, 5, 10, 'A-01')", [
            $productId, $stock, $costPrice, $sellingPrice
        ]);
        
        echo "<p>✅ Added product: {$product[0]} (Stock: $stock)</p>";
    }
    
    // 3. Create sample customers
    echo "<h2>👥 Creating Customers...</h2>";
    $customers = [
        ['Priya Sharma', 'Sharma Jewellery Store', 'business', '9876543220', '<EMAIL>', 'Delhi', 'Delhi', '07**********4Z8', '**********', 100000, 30],
        ['Rahul Gupta', '', 'individual', '9876543221', '<EMAIL>', 'Mumbai', 'Maharashtra', '', '', 0, 0],
        ['Meera Textiles', 'Meera Fashion Hub', 'business', '9876543222', '<EMAIL>', 'Bangalore', 'Karnataka', '29**********5Z9', '**********', 50000, 15]
    ];
    
    foreach ($customers as $customer) {
        $db->query("INSERT INTO customers (customer_name, business_name, customer_type, phone, email, city, state, gst_number, pan_number, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $customer);
        echo "<p>✅ Added customer: {$customer[0]}</p>";
    }
    
    // 4. Create sample sales
    echo "<h2>🛒 Creating Sample Sales...</h2>";
    $sales = [
        ['BILL-20250729-0001', 1, '2025-07-29', '10:30:00', 15000.00, 500.00, 450.00, 14950.00, 'paid', 'cash'],
        ['BILL-20250729-0002', 2, '2025-07-29', '14:15:00', 8500.00, 0.00, 255.00, 8755.00, 'paid', 'upi'],
        ['BILL-20250729-0003', 3, '2025-07-29', '16:45:00', 25000.00, 1000.00, 720.00, 24720.00, 'pending', 'credit']
    ];
    
    foreach ($sales as $sale) {
        $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_amount, tax_amount, grand_total, payment_status, payment_method, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0)", $sale);
        $saleId = $db->lastInsertId();
        
        // Add sample sale items
        $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, 1, 1, 15.500, 15.500, ?, ?, 3.00, ?, ?)", [
            $saleId, $sale[4], $sale[4], $sale[5], $sale[4] + $sale[5]
        ]);
        
        echo "<p>✅ Added sale: {$sale[0]} - ₹{$sale[6]}</p>";
    }
    
    $db->commit();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 20px 0;'>";
    echo "<h2>🎉 Sample Data Created Successfully!</h2>";
    echo "<p>The system now has sample data for testing all functionality:</p>";
    echo "<ul>";
    echo "<li>✅ 3 Suppliers with contact information</li>";
    echo "<li>✅ 5 Products with inventory records</li>";
    echo "<li>✅ 3 Customers (business and individual)</li>";
    echo "<li>✅ 3 Sample sales transactions</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Test the System Now:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>📊 Dashboard (should show statistics)</a></li>";
    echo "<li><a href='products.php' target='_blank'>💎 Products (should show 5 products)</a></li>";
    echo "<li><a href='inventory.php' target='_blank'>📦 Inventory (should show stock levels)</a></li>";
    echo "<li><a href='customers.php' target='_blank'>👥 Customers (should show 3 customers)</a></li>";
    echo "<li><a href='sales.php' target='_blank'>📊 Sales (should show 3 sales)</a></li>";
    echo "<li><a href='billing.php' target='_blank'>🛒 New Sale (should show products to select)</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Creating Sample Data</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
