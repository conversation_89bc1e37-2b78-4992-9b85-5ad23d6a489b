<!-- INVENTORY PAGE -->
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-plus"></i> Add Inventory Item</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="add_inventory" value="1">
                    
                    <div class="mb-3">
                        <label class="form-label">Supplier *</label>
                        <select class="form-control" name="supplier_id" required>
                            <option value="">Select Supplier</option>
                            <?php foreach ($data['suppliers'] as $supplier): ?>
                                <option value="<?php echo $supplier['id']; ?>">
                                    <?php echo htmlspecialchars($supplier['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Product Code</label>
                        <input type="text" class="form-control" name="product_code" placeholder="e.g., CH001">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Product Name *</label>
                        <input type="text" class="form-control" name="product_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <select class="form-control" name="category">
                            <option value="chain">Chain</option>
                            <option value="bangles">Bangles</option>
                            <option value="earrings">Earrings</option>
                            <option value="rings">Rings</option>
                            <option value="necklace">Necklace</option>
                            <option value="pendant">Pendant</option>
                            <option value="bracelet">Bracelet</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="2"></textarea>
                    </div>
                    
                    <!-- Weight Information (as per your spreadsheet) -->
                    <h6 class="text-primary">Weight Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Current Wt (g) *</label>
                                <input type="number" step="0.001" class="form-control" name="current_wt" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Without Stone Wt (g) *</label>
                                <input type="number" step="0.001" class="form-control" name="without_stone_wt" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cost Information -->
                    <h6 class="text-primary">Cost Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">With Stone Cost (₹) *</label>
                                <input type="number" step="0.01" class="form-control" name="with_stone_cost" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Without Stone Cost (₹) *</label>
                                <input type="number" step="0.01" class="form-control" name="without_stone_cost" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 24K Information -->
                    <h6 class="text-primary">24K Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Procured in 24K</label>
                                <input type="number" step="0.001" class="form-control" name="procured_in_24k" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">With Stone 24K</label>
                                <input type="number" step="0.001" class="form-control" name="with_stone_24k" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Without Stone 24K</label>
                                <input type="number" step="0.001" class="form-control" name="without_stone_24k" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Weight in 24K</label>
                                <input type="number" step="0.001" class="form-control" name="weight_in_24k" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pricing -->
                    <h6 class="text-primary">Pricing</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Unit Price (₹) *</label>
                                <input type="number" step="0.01" class="form-control" name="unit_price" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Stone Price (₹)</label>
                                <input type="number" step="0.01" class="form-control" name="stone_price" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Total Value (₹) *</label>
                        <input type="number" step="0.01" class="form-control" name="total_value" required>
                    </div>
                    
                    <!-- Stock Management -->
                    <h6 class="text-primary">Stock Management</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Balance in Stock *</label>
                                <input type="number" step="0.001" class="form-control" name="balance_in_stock" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Min Stock Level</label>
                                <input type="number" step="0.001" class="form-control" name="min_stock_level" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plus"></i> Add to Inventory
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-boxes"></i> Current Inventory</h5>
                <div>
                    <span class="badge bg-primary"><?php echo count($data['inventory']); ?> items</span>
                    <span class="badge bg-success">₹<?php echo number_format(array_sum(array_column($data['inventory'], 'total_value')), 0); ?> total value</span>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($data['inventory'])): ?>
                    <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                        <table class="table table-hover table-sm">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    <th>Code</th>
                                    <th>Supplier</th>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Current Wt</th>
                                    <th>Without Stone Wt</th>
                                    <th>With Stone Cost</th>
                                    <th>Without Stone Cost</th>
                                    <th>Procured 24K</th>
                                    <th>Balance Stock</th>
                                    <th>Unit Price</th>
                                    <th>Total Value</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['inventory'] as $item): ?>
                                <tr class="<?php echo ($item['balance_in_stock'] <= $item['min_stock_level'] && $item['min_stock_level'] > 0) ? 'table-warning' : ''; ?>">
                                    <td><strong><?php echo htmlspecialchars($item['product_code'] ?? 'N/A'); ?></strong></td>
                                    <td><?php echo htmlspecialchars($item['supplier_name'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo ucfirst($item['category']); ?></span>
                                    </td>
                                    <td><?php echo number_format($item['current_wt'], 3); ?>g</td>
                                    <td><?php echo number_format($item['without_stone_wt'], 3); ?>g</td>
                                    <td>₹<?php echo number_format($item['with_stone_cost'], 2); ?></td>
                                    <td>₹<?php echo number_format($item['without_stone_cost'], 2); ?></td>
                                    <td><?php echo number_format($item['procured_in_24k'], 3); ?></td>
                                    <td>
                                        <?php echo number_format($item['balance_in_stock'], 3); ?>g
                                        <?php if ($item['balance_in_stock'] <= $item['min_stock_level'] && $item['min_stock_level'] > 0): ?>
                                            <i class="fas fa-exclamation-triangle text-warning" title="Low Stock"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>₹<?php echo number_format($item['unit_price'], 2); ?></td>
                                    <td><strong>₹<?php echo number_format($item['total_value'], 2); ?></strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo $item['status'] === 'in_stock' ? 'success' : ($item['status'] === 'low_stock' ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $item['status'])); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No inventory items yet</h5>
                        <p class="text-muted">Add your first inventory item using the form on the left</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
