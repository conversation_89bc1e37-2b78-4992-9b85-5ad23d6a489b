<?php
/**
 * Final System Test - Comprehensive Database and Functionality Check
 */

require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><title>Final System Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.test-section { margin: 20px 0; padding: 15px; border-radius: 8px; }
.success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
.error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
.info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
.warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
h1 { color: #333; text-align: center; }
h2 { color: #495057; border-bottom: 2px solid #dee2e6; padding-bottom: 10px; }
.stats { display: flex; justify-content: space-around; margin: 20px 0; }
.stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; min-width: 120px; }
.links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0; }
.link-card { background: #e9ecef; padding: 10px; border-radius: 5px; text-align: center; }
.link-card a { text-decoration: none; color: #495057; font-weight: bold; }
.link-card:hover { background: #dee2e6; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔧 Indian Jewellery Wholesale Management System v2.0</h1>";
echo "<h1>📋 Final System Test Report</h1>";

$totalTests = 0;
$passedTests = 0;

function testResult($title, $status, $message, $details = '') {
    global $totalTests, $passedTests;
    $totalTests++;
    if ($status === 'success') $passedTests++;
    
    echo "<div class='test-section $status'>";
    echo "<h3>$title</h3>";
    echo "<p>$message</p>";
    if ($details) echo "<small>$details</small>";
    echo "</div>";
}

try {
    $db = getDB();
    
    // Test 1: Database Connection
    testResult("🗄️ Database Connection", "success", "✅ Successfully connected to MySQL database", "Connection established with jewellery_wholesale_v2 database");
    
    // Test 2: Table Structure
    $tables = $db->fetchAll("SHOW TABLES");
    $tableNames = array_column($tables, array_keys($tables[0])[0]);
    $expectedTables = ['users', 'settings', 'metal_rates', 'suppliers', 'categories', 'products', 'inventory', 'customers', 'sales', 'sale_items', 'stock_movements', 'notifications', 'audit_logs'];
    $missingTables = array_diff($expectedTables, $tableNames);
    
    if (empty($missingTables)) {
        testResult("📊 Database Schema", "success", "✅ All " . count($tableNames) . " required tables exist", "Tables: " . implode(', ', $tableNames));
    } else {
        testResult("📊 Database Schema", "error", "❌ Missing tables: " . implode(', ', $missingTables));
    }
    
    // Test 3: Data Verification
    $dataStats = [];
    foreach (['users', 'categories', 'settings', 'metal_rates', 'suppliers', 'products', 'customers', 'sales'] as $table) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            $dataStats[$table] = $count;
        } catch (Exception $e) {
            $dataStats[$table] = 'ERROR';
        }
    }
    
    echo "<div class='test-section info'>";
    echo "<h3>📈 Data Statistics</h3>";
    echo "<div class='stats'>";
    foreach ($dataStats as $table => $count) {
        echo "<div class='stat-card'>";
        echo "<strong>" . ucfirst($table) . "</strong><br>";
        echo "<span style='font-size: 1.5em; color: #007bff;'>$count</span>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // Test 4: Critical Queries
    $criticalQueries = [
        'Dashboard Stats' => "SELECT 
            (SELECT COUNT(*) FROM products WHERE is_active = 1) as products,
            (SELECT COUNT(*) FROM customers WHERE is_active = 1) as customers,
            (SELECT COUNT(*) FROM sales WHERE is_cancelled = 0) as sales,
            (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE is_cancelled = 0) as revenue",
        
        'Products with Categories' => "SELECT p.product_name, c.category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.is_active = 1 LIMIT 5",
        
        'Inventory Status' => "SELECT i.quantity_in_stock, p.product_name 
            FROM inventory i 
            JOIN products p ON i.product_id = p.id 
            WHERE i.quantity_in_stock > 0 LIMIT 5",
        
        'Current Metal Rates' => "SELECT metal_type, purity, rate_per_gram 
            FROM metal_rates 
            WHERE rate_date = CURDATE() AND is_active = 1",
        
        'Recent Sales' => "SELECT s.bill_number, c.customer_name, s.grand_total 
            FROM sales s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            WHERE s.is_cancelled = 0 
            ORDER BY s.created_at DESC LIMIT 5"
    ];
    
    foreach ($criticalQueries as $queryName => $query) {
        try {
            $result = $db->fetchAll($query);
            testResult("🔍 $queryName", "success", "✅ Query executed successfully", "Returned " . count($result) . " records");
        } catch (Exception $e) {
            testResult("🔍 $queryName", "error", "❌ Query failed: " . $e->getMessage());
        }
    }
    
    // Test 5: Authentication System
    try {
        $adminUser = $db->fetch("SELECT * FROM users WHERE username = 'admin'");
        if ($adminUser && password_verify('admin123', $adminUser['password_hash'])) {
            testResult("🔐 Authentication System", "success", "✅ Admin user exists and password verification works", "Username: admin, Password hash verified");
        } else {
            testResult("🔐 Authentication System", "error", "❌ Admin user not found or password verification failed");
        }
    } catch (Exception $e) {
        testResult("🔐 Authentication System", "error", "❌ Authentication test failed: " . $e->getMessage());
    }
    
    // Test 6: Business Logic Functions
    try {
        $currency = formatCurrency(1234.56);
        $weight = formatWeight(12.345);
        $billNumber = generateBillNumber();
        $emailValid = validateEmail('<EMAIL>');
        $phoneValid = validatePhone('9876543210');
        $gstValid = validateGST('27ABCDE1234F1Z5');
        
        if ($currency && $weight && $billNumber && $emailValid && $phoneValid && $gstValid) {
            testResult("💼 Business Logic Functions", "success", "✅ All utility functions working correctly", 
                "Currency: $currency, Weight: $weight, Bill: $billNumber");
        } else {
            testResult("💼 Business Logic Functions", "error", "❌ Some utility functions failed");
        }
    } catch (Exception $e) {
        testResult("💼 Business Logic Functions", "error", "❌ Business logic test failed: " . $e->getMessage());
    }
    
    // Test Summary
    echo "<div class='test-section " . ($passedTests == $totalTests ? 'success' : 'warning') . "'>";
    echo "<h2>📋 Test Summary</h2>";
    echo "<div class='stats'>";
    echo "<div class='stat-card'><strong>Total Tests</strong><br><span style='font-size: 1.5em;'>$totalTests</span></div>";
    echo "<div class='stat-card'><strong>Passed</strong><br><span style='font-size: 1.5em; color: #28a745;'>$passedTests</span></div>";
    echo "<div class='stat-card'><strong>Failed</strong><br><span style='font-size: 1.5em; color: #dc3545;'>" . ($totalTests - $passedTests) . "</span></div>";
    echo "<div class='stat-card'><strong>Success Rate</strong><br><span style='font-size: 1.5em; color: #007bff;'>" . round(($passedTests / $totalTests) * 100, 1) . "%</span></div>";
    echo "</div>";
    
    if ($passedTests == $totalTests) {
        echo "<h3 style='color: #28a745; text-align: center;'>🎉 ALL TESTS PASSED! SYSTEM IS FULLY OPERATIONAL!</h3>";
        echo "<p style='text-align: center;'>Your Indian Jewellery Wholesale Management System v2.0 is ready for production use.</p>";
    } else {
        echo "<h3 style='color: #856404; text-align: center;'>⚠️ SOME TESTS FAILED</h3>";
        echo "<p style='text-align: center;'>Please review the failed tests above before using the system.</p>";
    }
    echo "</div>";
    
    // Page Links
    echo "<div class='test-section info'>";
    echo "<h2>🌐 Test All System Pages</h2>";
    echo "<p>Click on each link below to test the individual pages:</p>";
    echo "<div class='links'>";
    
    $pages = [
        'index.php' => '📊 Dashboard',
        'login.php' => '🔑 Login',
        'products.php' => '💎 Products',
        'categories.php' => '📂 Categories',
        'inventory.php' => '📦 Inventory',
        'suppliers.php' => '🚚 Suppliers',
        'customers.php' => '👥 Customers',
        'billing.php' => '🛒 New Sale',
        'sales.php' => '📊 Sales History',
        'metal-rates.php' => '💰 Metal Rates',
        'settings.php' => '⚙️ Settings',
        'users.php' => '👤 Users',
        'backup.php' => '💾 Backup'
    ];
    
    foreach ($pages as $page => $title) {
        echo "<div class='link-card'>";
        echo "<a href='$page' target='_blank'>$title</a>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // System Information
    echo "<div class='test-section info'>";
    echo "<h2>ℹ️ System Information</h2>";
    echo "<div class='stats'>";
    echo "<div class='stat-card'><strong>PHP Version</strong><br>" . PHP_VERSION . "</div>";
    echo "<div class='stat-card'><strong>Database</strong><br>MySQL " . $db->getConnection()->getAttribute(PDO::ATTR_SERVER_VERSION) . "</div>";
    echo "<div class='stat-card'><strong>App Version</strong><br>" . APP_VERSION . "</div>";
    echo "<div class='stat-card'><strong>Timezone</strong><br>" . date_default_timezone_get() . "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    testResult("💥 Critical Error", "error", "❌ System test failed: " . $e->getMessage());
}

echo "</div></body></html>";
?>
