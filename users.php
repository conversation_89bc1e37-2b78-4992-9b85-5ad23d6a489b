<?php
/**
 * Users Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'username' => sanitizeInput($_POST['username']),
                'email' => sanitizeInput($_POST['email']),
                'full_name' => sanitizeInput($_POST['full_name']),
                'role' => $_POST['role'],
                'phone' => sanitizeInput($_POST['phone']),
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];
            
            // Validate email
            if (!validateEmail($data['email'])) {
                throw new Exception("Invalid email format");
            }
            
            // Check for duplicate username/email
            if ($action === 'add') {
                $existing = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", [$data['username'], $data['email']]);
                if ($existing) {
                    throw new Exception("Username or email already exists");
                }
            } else {
                $existing = $db->fetch("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?", [$data['username'], $data['email'], $user_id]);
                if ($existing) {
                    throw new Exception("Username or email already exists");
                }
            }
            
            if ($action === 'add') {
                // Hash password
                $password = $_POST['password'];
                if (strlen($password) < 6) {
                    throw new Exception("Password must be at least 6 characters long");
                }
                $data['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
                
                $sql = "INSERT INTO users (username, email, password_hash, full_name, role, phone, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "User added successfully!";
            } else {
                // Update password only if provided
                if (!empty($_POST['password'])) {
                    $password = $_POST['password'];
                    if (strlen($password) < 6) {
                        throw new Exception("Password must be at least 6 characters long");
                    }
                    $data['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
                    $sql = "UPDATE users SET username=?, email=?, password_hash=?, full_name=?, role=?, phone=?, is_active=? WHERE id=?";
                    $params = array_values($data);
                    $params[] = $user_id;
                } else {
                    unset($data['password_hash']);
                    $sql = "UPDATE users SET username=?, email=?, full_name=?, role=?, phone=?, is_active=? WHERE id=?";
                    $params = array_values($data);
                    $params[] = $user_id;
                }
                
                $db->query($sql, $params);
                $success = "User updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $user_id) {
            // Don't allow deleting the last admin
            $admin_count = $db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = 1")['count'];
            $user_role = $db->fetch("SELECT role FROM users WHERE id = ?", [$user_id])['role'];
            
            if ($user_role === 'admin' && $admin_count <= 1) {
                $error = "Cannot delete the last admin user.";
            } else {
                $db->query("UPDATE users SET is_active = 0 WHERE id = ?", [$user_id]);
                $success = "User deleted successfully!";
            }
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get users list
if ($action === 'list') {
    $users = $db->fetchAll("
        SELECT u.*, 
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as sales_count
        FROM users u
        WHERE u.is_active = 1
        ORDER BY u.created_at DESC
    ");
}

// Get single user for edit
if ($action === 'edit' && $user_id) {
    $user = $db->fetch("SELECT * FROM users WHERE id = ? AND is_active = 1", [$user_id]);
    if (!$user) {
        $error = "User not found!";
        $action = 'list';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Users List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">User Management</h2>
                    <p class="text-muted mb-0">Manage system users and permissions</p>
                </div>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add User
                </a>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Sales</th>
                                    <th>Last Login</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4 text-muted">
                                            <i class="fas fa-users fa-3x mb-3 opacity-25"></i>
                                            <p>No users found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle me-3">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                                        <?php if ($user['phone']): ?>
                                                            <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($user['username']); ?></code>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'manager' ? 'warning' : 'primary'); ?>">
                                                    <?php echo ucfirst($user['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $user['sales_count']; ?> sales
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <small class="text-muted">
                                                        <?php echo formatDateTime($user['last_login']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=edit&id=<?php echo $user['id']; ?>" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['role'] !== 'admin' || $user['sales_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteUser(<?php echo $user['id']; ?>)" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit User Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New User' : 'Edit User'; ?></h2>
                    <p class="text-muted mb-0">Enter user details below</p>
                </div>
                <a href="users.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">User Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username *</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" 
                                               pattern="[6-9][0-9]{9}" placeholder="10-digit mobile number">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">Role *</label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">Select Role</option>
                                            <option value="admin" <?php echo ($user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrator</option>
                                            <option value="manager" <?php echo ($user['role'] ?? '') === 'manager' ? 'selected' : ''; ?>>Manager</option>
                                            <option value="staff" <?php echo ($user['role'] ?? '') === 'staff' ? 'selected' : ''; ?>>Staff</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            Password <?php echo $action === 'add' ? '*' : '(leave blank to keep current)'; ?>
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               minlength="6" <?php echo $action === 'add' ? 'required' : ''; ?>>
                                        <div class="form-text">Minimum 6 characters</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               <?php echo ($user['is_active'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active User
                                        </label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="users.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action === 'add' ? 'Add User' : 'Update User'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">User Roles</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-user-shield me-2"></i>Administrator</h6>
                                <p class="mb-0 small">Full system access including user management, settings, and all features.</p>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-user-tie me-2"></i>Manager</h6>
                                <p class="mb-0 small">Access to sales, inventory, customers, and reports. Cannot manage users or system settings.</p>
                            </div>
                            
                            <div class="alert alert-primary">
                                <h6><i class="fas fa-user me-2"></i>Staff</h6>
                                <p class="mb-0 small">Basic access to create sales and view inventory. Limited reporting access.</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Security Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt me-2"></i>Password Security</h6>
                                <ul class="mb-0 small">
                                    <li>Minimum 6 characters required</li>
                                    <li>Use combination of letters and numbers</li>
                                    <li>Avoid common passwords</li>
                                    <li>Change passwords regularly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteUser(id) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }
    </script>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
    </style>
</body>
</html>
