<?php
/**
 * Users Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'username' => sanitizeInput($_POST['username']),
                'email' => sanitizeInput($_POST['email']),
                'full_name' => sanitizeInput($_POST['full_name']),
                'role' => $_POST['role'],
                'phone' => sanitizeInput($_POST['phone']),
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];
            
            // Validate email
            if (!validateEmail($data['email'])) {
                throw new Exception("Invalid email format");
            }
            
            // Check for duplicate username/email
            if ($action === 'add') {
                $existing = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", [$data['username'], $data['email']]);
                if ($existing) {
                    throw new Exception("Username or email already exists");
                }
            } else {
                $existing = $db->fetch("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?", [$data['username'], $data['email'], $user_id]);
                if ($existing) {
                    throw new Exception("Username or email already exists");
                }
            }
            
            if ($action === 'add') {
                // Hash password
                $password = $_POST['password'];
                if (strlen($password) < 6) {
                    throw new Exception("Password must be at least 6 characters long");
                }
                $data['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
                
                $sql = "INSERT INTO users (username, email, password_hash, full_name, role, phone, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "User added successfully!";
            } else {
                // Update password only if provided
                if (!empty($_POST['password'])) {
                    $password = $_POST['password'];
                    if (strlen($password) < 6) {
                        throw new Exception("Password must be at least 6 characters long");
                    }
                    $data['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
                    $sql = "UPDATE users SET username=?, email=?, password_hash=?, full_name=?, role=?, phone=?, is_active=? WHERE id=?";
                    $params = array_values($data);
                    $params[] = $user_id;
                } else {
                    unset($data['password_hash']);
                    $sql = "UPDATE users SET username=?, email=?, full_name=?, role=?, phone=?, is_active=? WHERE id=?";
                    $params = array_values($data);
                    $params[] = $user_id;
                }
                
                $db->query($sql, $params);
                $success = "User updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $user_id) {
            // Don't allow deleting the last admin
            $admin_count = $db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = 1")['count'];
            $user_role = $db->fetch("SELECT role FROM users WHERE id = ?", [$user_id])['role'];
            
            if ($user_role === 'admin' && $admin_count <= 1) {
                $error = "Cannot delete the last admin user.";
            } else {
                $db->query("UPDATE users SET is_active = 0 WHERE id = ?", [$user_id]);
                $success = "User deleted successfully!";
            }
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get users list
if ($action === 'list') {
    $users = $db->fetchAll("
        SELECT u.*,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE created_by = u.id AND is_cancelled = 0) as total_sales_amount,
               0 as today_activities,
               u.last_login
        FROM users u
        WHERE u.is_active = 1
        ORDER BY u.created_at DESC
    ");
}

// Get single user for edit
if ($action === 'edit' && $user_id) {
    $user = $db->fetch("SELECT * FROM users WHERE id = ? AND is_active = 1", [$user_id]);
    if (!$user) {
        $error = "User not found!";
        $action = 'list';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Users List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">User Management</h2>
                    <p class="text-muted mb-0">Manage system users and permissions</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group">
                        <button class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="export-users.php?format=csv"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                            <li><a class="dropdown-item" href="export-users.php?format=excel"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                            <li><a class="dropdown-item" href="export-users.php?format=pdf"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-outline-info" onclick="showUserAnalytics()">
                        <i class="fas fa-chart-bar me-2"></i>Analytics
                    </button>
                    <button class="btn btn-outline-warning" onclick="showBulkOperations()">
                        <i class="fas fa-tasks me-2"></i>Bulk Operations
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showActivityLogs()">
                        <i class="fas fa-history me-2"></i>Activity Logs
                    </button>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add User
                    </a>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAllUsers()">
                                    </th>
                                    <th>User</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Sales</th>
                                    <th>Revenue</th>
                                    <th>Activity</th>
                                    <th>Last Login</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="11" class="text-center py-4 text-muted">
                                            <i class="fas fa-users fa-3x mb-3 opacity-25"></i>
                                            <p>No users found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="user-checkbox" value="<?php echo $user['id']; ?>" onchange="updateBulkSelection()">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle me-3">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                                        <?php if ($user['phone']): ?>
                                                            <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($user['username']); ?></code>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'manager' ? 'warning' : 'primary'); ?>">
                                                    <?php echo ucfirst($user['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $user['sales_count']; ?> sales
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo formatCurrency($user['total_sales_amount']); ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($user['today_activities'] > 0): ?>
                                                    <span class="badge bg-success">
                                                        <?php echo $user['today_activities']; ?> today
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">No activity</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <small class="text-muted">
                                                        <?php echo formatDateTime($user['last_login']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" title="View Details"
                                                            onclick="viewUserDetails(<?php echo $user['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <a href="?action=edit&id=<?php echo $user['id']; ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-warning" title="Reset Password"
                                                            onclick="resetUserPassword(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <?php if ($user['is_active']): ?>
                                                        <button class="btn btn-outline-secondary" title="Suspend User"
                                                                onclick="toggleUserStatus(<?php echo $user['id']; ?>, 0)">
                                                            <i class="fas fa-pause"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-success" title="Activate User"
                                                                onclick="toggleUserStatus(<?php echo $user['id']; ?>, 1)">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($user['role'] !== 'admin' || $user['sales_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger"
                                                                onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')"
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit User Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New User' : 'Edit User'; ?></h2>
                    <p class="text-muted mb-0">Enter user details below</p>
                </div>
                <a href="users.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">User Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username *</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" 
                                               pattern="[6-9][0-9]{9}" placeholder="10-digit mobile number">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">Role *</label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">Select Role</option>
                                            <option value="admin" <?php echo ($user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrator</option>
                                            <option value="manager" <?php echo ($user['role'] ?? '') === 'manager' ? 'selected' : ''; ?>>Manager</option>
                                            <option value="staff" <?php echo ($user['role'] ?? '') === 'staff' ? 'selected' : ''; ?>>Staff</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            Password <?php echo $action === 'add' ? '*' : '(leave blank to keep current)'; ?>
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               minlength="6" <?php echo $action === 'add' ? 'required' : ''; ?>>
                                        <div class="form-text">Minimum 6 characters</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               <?php echo ($user['is_active'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active User
                                        </label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="users.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action === 'add' ? 'Add User' : 'Update User'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">User Roles</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-user-shield me-2"></i>Administrator</h6>
                                <p class="mb-0 small">Full system access including user management, settings, and all features.</p>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-user-tie me-2"></i>Manager</h6>
                                <p class="mb-0 small">Access to sales, inventory, customers, and reports. Cannot manage users or system settings.</p>
                            </div>
                            
                            <div class="alert alert-primary">
                                <h6><i class="fas fa-user me-2"></i>Staff</h6>
                                <p class="mb-0 small">Basic access to create sales and view inventory. Limited reporting access.</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Security Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt me-2"></i>Password Security</h6>
                                <ul class="mb-0 small">
                                    <li>Minimum 6 characters required</li>
                                    <li>Use combination of letters and numbers</li>
                                    <li>Avoid common passwords</li>
                                    <li>Change passwords regularly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- User Analytics Modal -->
    <div class="modal fade" id="userAnalyticsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Analytics</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>User Activity Overview</h6>
                            <canvas id="userActivityChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h6>User Statistics</h6>
                            <div class="list-group" id="userStatistics">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>Top Performers</h6>
                            <div id="topPerformers">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Role Distribution</h6>
                            <div id="roleDistribution">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportUserAnalytics()">Export Report</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Operations Modal -->
    <div class="modal fade" id="bulkOperationsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Operations</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Operation</label>
                        <select class="form-select" id="bulkOperation">
                            <option value="">Choose operation...</option>
                            <option value="export_selected">Export Selected Users</option>
                            <option value="suspend_users">Suspend Selected Users</option>
                            <option value="activate_users">Activate Selected Users</option>
                            <option value="reset_passwords">Reset Passwords</option>
                            <option value="change_role">Change Role</option>
                            <option value="send_notifications">Send Notifications</option>
                        </select>
                    </div>
                    <div id="bulkOperationDetails" style="display: none;">
                        <!-- Dynamic content based on selected operation -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processBulkOperation()">Execute</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Logs Modal -->
    <div class="modal fade" id="activityLogsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Activity Logs</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="activityUser">
                                <option value="">All Users</option>
                                <!-- Dynamic user options -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="activityType">
                                <option value="">All Activities</option>
                                <option value="login">Login/Logout</option>
                                <option value="sales">Sales Activities</option>
                                <option value="inventory">Inventory Changes</option>
                                <option value="settings">Settings Changes</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="activityDate" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary" onclick="loadActivityLogs()">
                                <i class="fas fa-search me-2"></i>Load
                            </button>
                        </div>
                    </div>
                    <div id="activityLogsContent" style="max-height: 400px; overflow-y: auto;">
                        <!-- Dynamic content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportActivityLogs()">Export Logs</button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="userDetailsContent">
                        <!-- Dynamic content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Selection Bar -->
    <div class="card mb-3" id="bulkSelectionBar" style="display: none; position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 1000; width: 400px;">
        <div class="card-body py-2">
            <div class="d-flex justify-content-between align-items-center">
                <span id="selectedUserCount">0 users selected</span>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showBulkOperations()">
                        <i class="fas fa-tasks me-1"></i>Actions
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteUser(id, username) {
            if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }

        // Enhanced user management functions
        function toggleSelectAllUsers() {
            const selectAll = document.getElementById('selectAllUsers');
            const checkboxes = document.querySelectorAll('.user-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkSelection();
        }

        function updateBulkSelection() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            const bulkBar = document.getElementById('bulkSelectionBar');
            const countSpan = document.getElementById('selectedUserCount');

            if (checkboxes.length > 0) {
                bulkBar.style.display = 'block';
                countSpan.textContent = checkboxes.length + ' users selected';
            } else {
                bulkBar.style.display = 'none';
            }
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            const selectAll = document.getElementById('selectAllUsers');

            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAll.checked = false;

            updateBulkSelection();
        }

        function showUserAnalytics() {
            const modal = new bootstrap.Modal(document.getElementById('userAnalyticsModal'));
            modal.show();
            loadUserAnalytics();
        }

        function loadUserAnalytics() {
            fetch('ajax/user-analytics.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAnalyticsDisplay(data);
                        drawUserActivityChart(data.activity);
                    }
                })
                .catch(error => {
                    console.error('Error loading analytics:', error);
                });
        }

        function updateAnalyticsDisplay(data) {
            // Update statistics
            const statisticsHtml = data.statistics.map(stat => `
                <div class="list-group-item d-flex justify-content-between">
                    <span>${stat.label}</span>
                    <strong>${stat.value}</strong>
                </div>
            `).join('');
            document.getElementById('userStatistics').innerHTML = statisticsHtml;

            // Update top performers
            const performersHtml = data.topPerformers.map(user => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                    <div>
                        <strong>${user.name}</strong><br>
                        <small class="text-muted">${user.role}</small>
                    </div>
                    <span class="badge bg-success">₹${user.revenue}</span>
                </div>
            `).join('');
            document.getElementById('topPerformers').innerHTML = performersHtml;

            // Update role distribution
            const roleHtml = data.roleDistribution.map(role => `
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>${role.role}</span>
                        <span>${role.count} users</span>
                    </div>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar" style="width: ${role.percentage}%"></div>
                    </div>
                </div>
            `).join('');
            document.getElementById('roleDistribution').innerHTML = roleHtml;
        }

        function drawUserActivityChart(activity) {
            // This would integrate with Chart.js to draw the activity chart
            console.log('Drawing user activity chart with data:', activity);
        }

        function showBulkOperations() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');

            if (checkboxes.length === 0) {
                alert('Please select users first');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('bulkOperationsModal'));
            modal.show();
        }

        function processBulkOperation() {
            const operation = document.getElementById('bulkOperation').value;
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');

            if (!operation || checkboxes.length === 0) {
                alert('Please select an operation and users');
                return;
            }

            const userIds = Array.from(checkboxes).map(cb => cb.value);

            switch (operation) {
                case 'export_selected':
                    exportSelectedUsers(userIds);
                    break;
                case 'suspend_users':
                    bulkSuspendUsers(userIds);
                    break;
                case 'activate_users':
                    bulkActivateUsers(userIds);
                    break;
                case 'reset_passwords':
                    bulkResetPasswords(userIds);
                    break;
                case 'change_role':
                    showRoleChangeInterface(userIds);
                    break;
                case 'send_notifications':
                    showNotificationInterface(userIds);
                    break;
            }
        }

        function exportSelectedUsers(userIds) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export-users.php';

            userIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_users[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }

        function bulkSuspendUsers(userIds) {
            if (confirm(`Are you sure you want to suspend ${userIds.length} selected users?`)) {
                fetch('ajax/bulk-user-operations.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'suspend',
                        user_ids: userIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Successfully suspended ${data.affected} users`);
                        location.reload();
                    } else {
                        alert('Operation failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }

        function resetUserPassword(userId, username) {
            if (confirm(`Reset password for user "${username}"?`)) {
                fetch('ajax/reset-user-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ user_id: userId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Password reset successfully. New password: ${data.new_password}`);
                    } else {
                        alert('Password reset failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }

        function toggleUserStatus(userId, status) {
            const action = status ? 'activate' : 'suspend';

            if (confirm(`Are you sure you want to ${action} this user?`)) {
                fetch('ajax/toggle-user-status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`User ${action}d successfully`);
                        location.reload();
                    } else {
                        alert(`Failed to ${action} user: ` + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }

        function viewUserDetails(userId) {
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            modal.show();

            // Load user details
            document.getElementById('userDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

            fetch(`ajax/user-details.php?id=${userId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('userDetailsContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('userDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading details</div>';
                });
        }

        function showActivityLogs() {
            const modal = new bootstrap.Modal(document.getElementById('activityLogsModal'));
            modal.show();
            loadActivityLogs();
        }

        function loadActivityLogs() {
            const user = document.getElementById('activityUser').value;
            const type = document.getElementById('activityType').value;
            const date = document.getElementById('activityDate').value;

            document.getElementById('activityLogsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

            fetch(`ajax/user-activity-logs.php?user=${user}&type=${type}&date=${date}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('activityLogsContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('activityLogsContent').innerHTML = '<div class="alert alert-danger">Error loading logs</div>';
                });
        }

        function exportUserAnalytics() {
            window.open('export-user-analytics.php', '_blank');
        }

        function exportActivityLogs() {
            const user = document.getElementById('activityUser').value;
            const type = document.getElementById('activityType').value;
            const date = document.getElementById('activityDate').value;
            window.open(`export-activity-logs.php?user=${user}&type=${type}&date=${date}`, '_blank');
        }
    </script>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
    </style>
</body>
</html>
