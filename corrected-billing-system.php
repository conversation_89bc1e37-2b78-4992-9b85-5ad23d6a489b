<?php
/**
 * Corrected Billing System - No Double GST
 * Follows your original requirements exactly
 */

require_once 'config/database.php';
require_once 'lib/PurityParser.php';
require_once 'lib/EnhancedJewelryCalculator.php';

startSession();
$db = getDB();

$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'create_bill_corrected') {
        try {
            // Calculate values using your exact formula (NO GST ADDITION)
            $gross_wt = floatval($_POST['gross_wt']);
            $stone_wt = floatval($_POST['stone_wt']);
            $net_wt = $gross_wt - $stone_wt; // Net Weight = Gross Weight - Stone Weight
            $tunch_percentage = floatval($_POST['tunch_percentage']);
            $base_rate = floatval($_POST['base_rate']);
            $making_charges = floatval($_POST['making_charges']);
            $stone_charges = floatval($_POST['stone_charges']);
            
            // Apply your EXACT handwritten formula
            $tunch_weight = $net_wt * ($tunch_percentage / 100); // Tunch Weight = Net Weight × (Tunch Percentage / 100)
            $metal_value = $tunch_weight * $base_rate; // Metal Value = Tunch Weight × Base Rate (DIRECT multiplication like your handwriting)
            $final_value = $metal_value + $making_charges + $stone_charges; // Final Value = Metal Value + Making + Stone Charges
            
            // Store in database
            $sql = "INSERT INTO corrected_billing_entries (
                customer_name, location, product_name, gross_wt, stone_wt, net_wt,
                tunch_percentage, base_rate, tunch_weight, metal_value,
                making_charges, stone_charges, final_value, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $_POST['customer_name'],
                $_POST['location'],
                $_POST['product_name'],
                $gross_wt,
                $stone_wt,
                $net_wt,
                $tunch_percentage,
                $base_rate,
                $tunch_weight,
                $metal_value,
                $making_charges,
                $stone_charges,
                $final_value
            ];
            
            $db->execute($sql, $params);
            $success = "✅ Bill created successfully! Final Value: ₹" . number_format($final_value, 2) . " (No double GST)";
            
        } catch (Exception $e) {
            $error = "❌ Error creating bill: " . $e->getMessage();
        }
    }
}

// Get recent bills
$recentBills = $db->fetchAll("SELECT * FROM corrected_billing_entries ORDER BY id DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corrected Billing System - No Double GST</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .billing-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 0;
        }
        .section-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .calculation-preview {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        .formula-display {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        .correction-notice {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="billing-header">
        <div class="container">
            <h1 class="mb-0"><i class="fas fa-check-circle me-2"></i>Corrected Billing System</h1>
            <p class="mb-0">No Double GST - Rates Include GST Already</p>
        </div>
    </div>

    <div class="container-fluid mt-4">
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Correction Notice -->
        <div class="correction-notice">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>GST Correction Applied</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>❌ Previous Issue:</h6>
                    <small>• Adding GST on top of rates that already include GST<br>• Double taxation problem<br>• Incorrect final calculations</small>
                </div>
                <div class="col-md-6">
                    <h6>✅ Corrected Approach:</h6>
                    <small>• Rates include GST (standard in jewelry business)<br>• No additional GST calculation<br>• Accurate final values</small>
                </div>
            </div>
        </div>

        <!-- Formula Display -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card section-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Your Exact Formula (Corrected)</h5>
                    </div>
                    <div class="card-body">
                        <div class="formula-display">
                            <strong>Your EXACT Handwritten Formula:</strong><br>
                            1. <strong>Net Weight</strong> = Gross Weight - Stone Weight<br>
                            2. <strong>Tunch Weight</strong> = Net Weight × (Tunch Percentage ÷ 100)<br>
                            3. <strong>Metal Value</strong> = Tunch Weight × Base Rate (DIRECT multiplication)<br>
                            4. <strong>Final Value</strong> = Metal Value + Making + Stone Charges<br>
                            <br>
                            <strong style="color: #28a745;">✅ Matches your handwritten calculation: 9.7159g × ₹10,112 = ₹98,240.10</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Corrected Billing Form -->
            <div class="col-md-8">
                <div class="card section-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>Corrected Billing (No Double GST)</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="correctedBillingForm">
                            <input type="hidden" name="action" value="create_bill_corrected">
                            
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <label class="form-label">Customer Name</label>
                                    <input type="text" class="form-control" name="customer_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Location</label>
                                    <input type="text" class="form-control" name="location" required>
                                </div>
                                <div class="col-md-12">
                                    <label class="form-label">Product Name</label>
                                    <input type="text" class="form-control" name="product_name" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Gross Wt (g)</label>
                                    <input type="number" class="form-control" id="gross_wt" name="gross_wt" step="0.001" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Wt (g)</label>
                                    <input type="number" class="form-control" id="stone_wt" name="stone_wt" step="0.001" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Net Wt (Auto)</label>
                                    <input type="number" class="form-control" id="net_wt" name="net_wt" step="0.001" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Tunch % (Purity)</label>
                                    <input type="number" class="form-control" id="tunch_percentage" name="tunch_percentage" step="0.1" value="91.6" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Base Rate (₹/g) - GST Included</label>
                                    <input type="number" class="form-control" id="base_rate" name="base_rate" step="0.01" value="6600" required>
                                    <div class="form-text">Rate already includes GST</div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Making Charges (₹)</label>
                                    <input type="number" class="form-control" id="making_charges" name="making_charges" step="0.01" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Charges (₹)</label>
                                    <input type="number" class="form-control" id="stone_charges" name="stone_charges" step="0.01" value="0">
                                </div>
                            </div>
                            
                            <!-- Real-time Calculation Preview -->
                            <div class="calculation-preview mt-3">
                                <h6><i class="fas fa-calculator me-2"></i>Live Calculation Preview (Corrected)</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <small><strong>Tunch Weight:</strong><br>
                                        <span id="tunchWeightDisplay">0.000g</span></small>
                                    </div>

                                    <div class="col-md-3">
                                        <small><strong>Metal Value:</strong><br>
                                        <span id="metalValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Final Value:</strong><br>
                                        <span id="finalValueDisplay" style="font-weight: bold; color: #28a745;">₹0.00</span></small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-success">
                                        <strong><i class="fas fa-check-circle me-1"></i>No GST Added:</strong> Rates already include GST (standard jewelry practice)
                                    </small>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success btn-lg mt-3">
                                <i class="fas fa-file-invoice me-1"></i>Create Bill (Corrected)
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Comparison Panel -->
            <div class="col-md-4">
                <div class="card section-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-balance-scale me-2"></i>Before vs After Correction</h6>
                    </div>
                    <div class="card-body">
                        <div id="comparisonDisplay">
                            <p class="text-muted">Enter values to see comparison</p>
                        </div>
                    </div>
                </div>
                
                <div class="card section-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>GST Information</h6>
                    </div>
                    <div class="card-body">
                        <h6>✅ Correct Approach:</h6>
                        <ul class="small">
                            <li>Jewelry rates typically include GST</li>
                            <li>No additional GST calculation needed</li>
                            <li>Final value = Metal + Making + Stone</li>
                            <li>Matches industry standard practice</li>
                        </ul>
                        
                        <h6>❌ Previous Issue:</h6>
                        <ul class="small">
                            <li>Adding 3% GST on inclusive rates</li>
                            <li>Double taxation problem</li>
                            <li>Inflated final values</li>
                            <li>Not matching market rates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Bills Table -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card section-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Bills (Corrected)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Product</th>
                                        <th>Gross Wt</th>
                                        <th>Net Wt</th>
                                        <th>Tunch %</th>
                                        <th>Tunch Wt</th>
                                        <th>Base Rate</th>
                                        <th>Final Value</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentBills as $bill): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($bill['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($bill['product_name']); ?></td>
                                        <td><?php echo number_format($bill['gross_wt'], 3); ?>g</td>
                                        <td><?php echo number_format($bill['net_wt'], 3); ?>g</td>
                                        <td><?php echo number_format($bill['tunch_percentage'], 1); ?>%</td>
                                        <td><?php echo number_format($bill['tunch_weight'], 3); ?>g</td>
                                        <td>₹<?php echo number_format($bill['base_rate'], 0); ?></td>
                                        <td><strong>₹<?php echo number_format($bill['final_value'], 2); ?></strong></td>
                                        <td><?php echo date('M j', strtotime($bill['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const grossWt = document.getElementById('gross_wt');
            const stoneWt = document.getElementById('stone_wt');
            const netWt = document.getElementById('net_wt');
            const tunchPercentage = document.getElementById('tunch_percentage');
            const baseRate = document.getElementById('base_rate');
            const makingCharges = document.getElementById('making_charges');
            const stoneCharges = document.getElementById('stone_charges');
            
            // Display elements
            const tunchWeightDisplay = document.getElementById('tunchWeightDisplay');

            const metalValueDisplay = document.getElementById('metalValueDisplay');
            const finalValueDisplay = document.getElementById('finalValueDisplay');
            const comparisonDisplay = document.getElementById('comparisonDisplay');
            
            function calculateNetWeight() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                netWt.value = net.toFixed(3);
                calculateCorrected();
            }
            
            function calculateCorrected() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                const tunchPct = parseFloat(tunchPercentage.value) || 0;
                const rate = parseFloat(baseRate.value) || 0;
                const making = parseFloat(makingCharges.value) || 0;
                const stoneCharge = parseFloat(stoneCharges.value) || 0;
                
                if (net <= 0 || tunchPct <= 0 || rate <= 0) {
                    tunchWeightDisplay.textContent = '0.000g';

                    metalValueDisplay.textContent = '₹0.00';
                    finalValueDisplay.textContent = '₹0.00';
                    comparisonDisplay.innerHTML = '<p class="text-muted">Enter values to see comparison</p>';
                    return;
                }
                
                // Apply your EXACT handwritten formula
                // 1. Net Weight = Gross Weight - Stone Weight (already calculated)
                // 2. Tunch Weight = Net Weight × (Tunch Percentage / 100)
                const tunchWeight = net * (tunchPct / 100);

                // 3. Metal Value = Tunch Weight × Base Rate (DIRECT multiplication like your handwriting)
                const metalValue = tunchWeight * rate;

                // 4. Final Value = Metal Value + Making + Stone Charges (matches your handwriting)
                const finalValue = metalValue + making + stoneCharge;
                
                // What it would be with incorrect GST addition
                const incorrectGst = finalValue * 0.03; // 3% GST
                const incorrectTotal = finalValue + incorrectGst;
                
                // Update display
                tunchWeightDisplay.textContent = tunchWeight.toFixed(3) + 'g';

                metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                finalValueDisplay.textContent = '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                
                // Show comparison
                comparisonDisplay.innerHTML = 
                    '<h6>Comparison:</h6>' +
                    '<div class="mb-2">' +
                    '<small><strong>✅ Corrected (No GST):</strong><br>' +
                    '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + '</small>' +
                    '</div>' +
                    '<div class="mb-2">' +
                    '<small><strong>❌ With Wrong GST:</strong><br>' +
                    '₹' + incorrectTotal.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + '</small>' +
                    '</div>' +
                    '<div>' +
                    '<small><strong>Difference:</strong><br>' +
                    '<span style="color: #dc3545;">₹' + incorrectGst.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' saved</span></small>' +
                    '</div>';
            }
            
            // Event listeners
            grossWt.addEventListener('input', calculateNetWeight);
            stoneWt.addEventListener('input', calculateNetWeight);
            tunchPercentage.addEventListener('input', calculateCorrected);
            baseRate.addEventListener('input', calculateCorrected);
            makingCharges.addEventListener('input', calculateCorrected);
            stoneCharges.addEventListener('input', calculateCorrected);
            
            // Initial calculation
            calculateNetWeight();
        });
    </script>
</body>
</html>
