<?php
/**
 * Inventory Reports - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Get filter parameters
$category_id = $_GET['category_id'] ?? '';
$stock_status = $_GET['stock_status'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'product_name';

// Build WHERE clause
$where_conditions = ["p.is_active = 1"];
$params = [];

if ($category_id) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_id;
}

if ($stock_status) {
    switch ($stock_status) {
        case 'low_stock':
            $where_conditions[] = "i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0";
            break;
        case 'out_of_stock':
            $where_conditions[] = "i.quantity_in_stock = 0";
            break;
        case 'overstock':
            $where_conditions[] = "i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0";
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// Validate sort_by parameter
$allowed_sorts = ['product_name', 'category_name', 'quantity_in_stock', 'stock_value', 'last_updated'];
if (!in_array($sort_by, $allowed_sorts)) {
    $sort_by = 'product_name';
}

try {
    // Get inventory data
    $inventory = $db->fetchAll("
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE $where_clause
        ORDER BY $sort_by ASC
    ", $params);

    // Get summary statistics
    $summary = $db->fetch("
        SELECT 
            COUNT(*) as total_products,
            COALESCE(SUM(i.quantity_in_stock), 0) as total_quantity,
            COALESCE(SUM(i.quantity_in_stock * p.selling_price), 0) as total_value,
            COUNT(CASE WHEN i.quantity_in_stock = 0 THEN 1 END) as out_of_stock_count,
            COUNT(CASE WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 1 END) as low_stock_count
        FROM products p
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE $where_clause
    ", $params);

    // Get categories for filter
    $categories = $db->fetchAll("
        SELECT id, category_name 
        FROM categories 
        WHERE is_active = 1 
        ORDER BY category_name
    ");

} catch (Exception $e) {
    $error = "Error loading inventory data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Reports - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Inventory Reports</h2>
                    <p class="text-muted mb-0">Monitor stock levels and inventory value</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Category</label>
                            <select class="form-select" name="category_id">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Stock Status</label>
                            <select class="form-select" name="stock_status">
                                <option value="">All Status</option>
                                <option value="low_stock" <?php echo $stock_status == 'low_stock' ? 'selected' : ''; ?>>Low Stock</option>
                                <option value="out_of_stock" <?php echo $stock_status == 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                                <option value="overstock" <?php echo $stock_status == 'overstock' ? 'selected' : ''; ?>>Overstock</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Sort By</label>
                            <select class="form-select" name="sort_by">
                                <option value="product_name" <?php echo $sort_by == 'product_name' ? 'selected' : ''; ?>>Product Name</option>
                                <option value="category_name" <?php echo $sort_by == 'category_name' ? 'selected' : ''; ?>>Category</option>
                                <option value="quantity_in_stock" <?php echo $sort_by == 'quantity_in_stock' ? 'selected' : ''; ?>>Stock Quantity</option>
                                <option value="stock_value" <?php echo $sort_by == 'stock_value' ? 'selected' : ''; ?>>Stock Value</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>Apply Filters
                                </button>
                                <a href="inventory.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $summary['total_products']; ?></h3>
                            <small class="text-muted">Total Products</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo number_format($summary['total_quantity']); ?></h3>
                            <small class="text-muted">Total Quantity</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">₹<?php echo number_format($summary['total_value'], 2); ?></h3>
                            <small class="text-muted">Total Value</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-danger"><?php echo $summary['out_of_stock_count']; ?></h3>
                            <small class="text-muted">Out of Stock</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo $summary['low_stock_count']; ?></h3>
                            <small class="text-muted">Low Stock</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary">₹<?php echo $summary['total_products'] > 0 ? number_format($summary['total_value'] / $summary['total_products'], 2) : '0.00'; ?></h3>
                            <small class="text-muted">Avg. Value</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Data -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Inventory Details</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>SKU</th>
                                    <th>Stock Qty</th>
                                    <th>Min. Level</th>
                                    <th>Unit Price</th>
                                    <th>Stock Value</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($inventory)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            No inventory found for the selected criteria
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($inventory as $item): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                                <?php if ($item['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($item['description'], 0, 50)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($item['category_name'] ?? 'Uncategorized'); ?></td>
                                            <td><code><?php echo htmlspecialchars($item['sku']); ?></code></td>
                                            <td><strong><?php echo number_format($item['quantity_in_stock']); ?></strong></td>
                                            <td><?php echo number_format($item['minimum_stock_level']); ?></td>
                                            <td>₹<?php echo number_format($item['selling_price'], 2); ?></td>
                                            <td><strong>₹<?php echo number_format($item['stock_value'], 2); ?></strong></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                switch ($item['stock_status_text']) {
                                                    case 'Out of Stock':
                                                        $statusClass = 'bg-danger';
                                                        break;
                                                    case 'Low Stock':
                                                        $statusClass = 'bg-warning';
                                                        break;
                                                    case 'Overstock':
                                                        $statusClass = 'bg-info';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-success';
                                                }
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>">
                                                    <?php echo $item['stock_status_text']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($item['last_updated']): ?>
                                                    <small><?php echo date('d/m/Y H:i', strtotime($item['last_updated'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/app.js"></script>
    
    <script>
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open('export-inventory-report.php?' + params.toString(), '_blank');
        }
    </script>
</body>
</html>
