<?php
/**
 * Create Missing Backups Table
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>Creating Backups Table</h2>";
    
    // Check if table already exists
    $existing = $db->fetchAll("SHOW TABLES LIKE 'backups'");
    
    if (count($existing) > 0) {
        echo "✅ Backups table already exists!<br>";
    } else {
        echo "📝 Creating backups table...<br>";
        
        $sql = "
        CREATE TABLE `backups` (
            `id` INT PRIMARY KEY AUTO_INCREMENT,
            `backup_name` VARCHAR(255) NOT NULL,
            `file_path` VARCHAR(500) NOT NULL,
            `file_size` BIGINT DEFAULT 0,
            `backup_type` ENUM('manual', 'automatic', 'scheduled') DEFAULT 'manual',
            `status` ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'completed',
            `description` TEXT,
            `created_by` INT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
            INDEX `idx_backup_date` (`created_at`),
            INDEX `idx_backup_type` (`backup_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $db->execute($sql);
        echo "✅ Backups table created successfully!<br>";
        
        // Add some sample backup records
        echo "<br>📝 Adding sample backup records...<br>";
        
        $sample_backups = [
            [
                'backup_name' => 'Initial Database Backup',
                'file_path' => 'backups/backup_' . date('Y-m-d_H-i-s') . '.sql',
                'file_size' => 1024 * 50, // 50KB
                'backup_type' => 'manual',
                'description' => 'Initial backup after system setup'
            ],
            [
                'backup_name' => 'Weekly Automatic Backup',
                'file_path' => 'backups/auto_backup_' . date('Y-m-d', strtotime('-7 days')) . '.sql',
                'file_size' => 1024 * 75, // 75KB
                'backup_type' => 'automatic',
                'description' => 'Automatic weekly backup'
            ]
        ];
        
        foreach ($sample_backups as $backup) {
            $db->execute("
                INSERT INTO backups (backup_name, file_path, file_size, backup_type, description, status) 
                VALUES (?, ?, ?, ?, ?, 'completed')
            ", [
                $backup['backup_name'],
                $backup['file_path'],
                $backup['file_size'],
                $backup['backup_type'],
                $backup['description']
            ]);
            echo "- Added backup: {$backup['backup_name']}<br>";
        }
    }
    
    // Verify the table structure
    echo "<br><h3>Table Structure</h3>";
    $columns = $db->fetchAll("DESCRIBE backups");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show current backup records
    echo "<br><h3>Current Backup Records</h3>";
    $backups = $db->fetchAll("SELECT * FROM backups ORDER BY created_at DESC");
    echo "Found " . count($backups) . " backup records.<br>";
    
    if (count($backups) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Size</th><th>Created</th></tr>";
        foreach ($backups as $backup) {
            echo "<tr>";
            echo "<td>{$backup['id']}</td>";
            echo "<td>{$backup['backup_name']}</td>";
            echo "<td>{$backup['backup_type']}</td>";
            echo "<td>" . number_format($backup['file_size'] / 1024, 1) . " KB</td>";
            echo "<td>{$backup['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<br>✅ Backups table setup completed successfully!<br>";
    echo "<br><a href='backup.php'>→ Go to Backup Page</a>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
