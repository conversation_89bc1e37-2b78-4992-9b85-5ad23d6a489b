[29-Jul-2025 15:01:33 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.products' doesn't exist SQL: SELECT COUNT(*) as count FROM products WHERE is_active = 1
[29-Jul-2025 15:01:36 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.categories' doesn't exist SQL: SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name
[29-Jul-2025 15:01:36 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('SELECT id, cate...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\products.php(61): Database->fetchAll('SELECT id, cate...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:01:43 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.products' doesn't exist SQL: SELECT COUNT(*) as count FROM products WHERE is_active = 1
[29-Jul-2025 15:01:45 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.categories' doesn't exist SQL: SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name
[29-Jul-2025 15:01:45 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('SELECT id, cate...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\products.php(61): Database->fetchAll('SELECT id, cate...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:02:33 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.products' doesn't exist SQL: SELECT COUNT(*) as count FROM products WHERE is_active = 1
[29-Jul-2025 15:20:11 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.categories' doesn't exist SQL: SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name
[29-Jul-2025 15:20:11 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('SELECT id, cate...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\products.php(61): Database->fetchAll('SELECT id, cate...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:21:53 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.categories' doesn't exist SQL: SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name
[29-Jul-2025 15:21:53 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('SELECT id, cate...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\products.php(61): Database->fetchAll('SELECT id, cate...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:21:55 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.categories' doesn't exist SQL: 
        SELECT c.*, 
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.sort_order, c.category_name
    
[29-Jul-2025 15:21:55 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\categories.php(58): Database->fetchAll('\n        SELECT...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:24:29 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\settings.php:168
Stack trace:
#0 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\settings.php on line 168
[29-Jul-2025 15:26:43 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\settings.php:168
Stack trace:
#0 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\settings.php on line 168
[29-Jul-2025 15:26:47 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\users.php on line 201
[29-Jul-2025 15:26:52 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.backups' doesn't exist SQL: 
    SELECT b.*, u.full_name as created_by_name
    FROM backups b
    LEFT JOIN users u ON b.created_by = u.id
    ORDER BY b.created_at DESC
    LIMIT 20

[29-Jul-2025 15:26:52 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php:81
Stack trace:
#0 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php(90): Database->query('\n    SELECT b.*...', Array)
#1 C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\backup.php(96): Database->fetchAll('\n    SELECT b.*...')
#2 {main}
  thrown in C:\Users\<USER>\Documents\augment-projects\WholsaleJewel\v2\config\database.php on line 81
[29-Jul-2025 15:29:21 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'payment_method' at row 1 SQL: INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_amount, tax_amount, grand_total, payment_status, payment_method, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0)
[29-Jul-2025 15:33:05 Asia/Kolkata] Metal rates update - Rates data: Array
(
    [Gold_24K] => Array
        (
            [rate_per_gram] => 10112
            [metal_type] => Gold
            [purity] => 24K
        )

    [Gold_22K] => Array
        (
            [rate_per_gram] => 5800.00
            [metal_type] => Gold
            [purity] => 22K
        )

    [Gold_18K] => Array
        (
            [rate_per_gram] => 4650.00
            [metal_type] => Gold
            [purity] => 18K
        )

    [Silver_999] => Array
        (
            [rate_per_gram] => 85.00
            [metal_type] => Silver
            [purity] => 999
        )

    [Silver_925] => Array
        (
            [rate_per_gram] => 78.00
            [metal_type] => Silver
            [purity] => 925
        )

    [Platinum_950] => Array
        (
            [rate_per_gram] => 3200.00
            [metal_type] => Platinum
            [purity] => 950
        )

)

[29-Jul-2025 15:33:05 Asia/Kolkata] Metal rates update - Date: 2025-07-29
[29-Jul-2025 15:33:15 Asia/Kolkata] Metal rates update - Rates data: Array
(
    [Gold_24K] => Array
        (
            [rate_per_gram] => 10112.00
            [metal_type] => Gold
            [purity] => 24K
        )

    [Gold_22K] => Array
        (
            [rate_per_gram] => 9115
            [metal_type] => Gold
            [purity] => 22K
        )

    [Gold_18K] => Array
        (
            [rate_per_gram] => 4650.00
            [metal_type] => Gold
            [purity] => 18K
        )

    [Silver_999] => Array
        (
            [rate_per_gram] => 85.00
            [metal_type] => Silver
            [purity] => 999
        )

    [Silver_925] => Array
        (
            [rate_per_gram] => 78.00
            [metal_type] => Silver
            [purity] => 925
        )

    [Platinum_950] => Array
        (
            [rate_per_gram] => 3200.00
            [metal_type] => Platinum
            [purity] => 950
        )

)

[29-Jul-2025 15:33:15 Asia/Kolkata] Metal rates update - Date: 2025-07-29
[29-Jul-2025 15:33:20 Asia/Kolkata] Metal rates update - Rates data: Array
(
    [Gold_24K] => Array
        (
            [rate_per_gram] => 10112.00
            [metal_type] => Gold
            [purity] => 24K
        )

    [Gold_22K] => Array
        (
            [rate_per_gram] => 9115.00
            [metal_type] => Gold
            [purity] => 22K
        )

    [Gold_18K] => Array
        (
            [rate_per_gram] => 7500
            [metal_type] => Gold
            [purity] => 18K
        )

    [Silver_999] => Array
        (
            [rate_per_gram] => 85.00
            [metal_type] => Silver
            [purity] => 999
        )

    [Silver_925] => Array
        (
            [rate_per_gram] => 78.00
            [metal_type] => Silver
            [purity] => 925
        )

    [Platinum_950] => Array
        (
            [rate_per_gram] => 3200.00
            [metal_type] => Platinum
            [purity] => 950
        )

)

[29-Jul-2025 15:33:20 Asia/Kolkata] Metal rates update - Date: 2025-07-29
[29-Jul-2025 15:39:40 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`jewellery_wholesale_v2`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL) SQL: INSERT INTO products (product_name, product_code, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, description, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
[29-Jul-2025 16:36:33 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`jewellery_wholesale_v2`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL) SQL: INSERT INTO products (product_name, product_code, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, description, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
[29-Jul-2025 16:38:17 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`jewellery_wholesale_v2`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL) SQL: INSERT INTO products (product_name, product_code, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, description, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
[29-Jul-2025 16:39:11 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`jewellery_wholesale_v2`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL) SQL: INSERT INTO products (supplier_id, category_id, product_name, product_code, description, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
[29-Jul-2025 16:40:16 Asia/Kolkata] PHP Notice:  ob_end_flush(): Failed to delete and flush buffer. No buffer to delete or flush in C:\proj\v2\test-all-pages.php on line 11
[29-Jul-2025 16:41:26 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'discount_type' at row 1 SQL: INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[29-Jul-2025 16:42:08 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\proj\v2\settings.php:168
Stack trace:
#0 {main}
  thrown in C:\proj\v2\settings.php on line 168
[29-Jul-2025 17:09:26 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 17:09:26 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 17:45:55 Asia/Kolkata] PHP Warning:  Undefined variable $inventory in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:45:55 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:46:04 Asia/Kolkata] PHP Warning:  Undefined variable $inventory in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:46:04 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:46:08 Asia/Kolkata] PHP Warning:  Trying to access array offset on false in C:\proj\v2\inventory.php on line 741
[29-Jul-2025 17:46:08 Asia/Kolkata] PHP Deprecated:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\proj\v2\inventory.php on line 741
[29-Jul-2025 17:46:08 Asia/Kolkata] PHP Warning:  Trying to access array offset on false in C:\proj\v2\inventory.php on line 741
[29-Jul-2025 17:46:08 Asia/Kolkata] PHP Deprecated:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\proj\v2\inventory.php on line 741
[29-Jul-2025 17:46:15 Asia/Kolkata] PHP Warning:  Undefined variable $inventory in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:46:15 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\inventory.php on line 371
[29-Jul-2025 17:49:54 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\proj\v2\settings.php:186
Stack trace:
#0 {main}
  thrown in C:\proj\v2\settings.php on line 186
[29-Jul-2025 17:58:11 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.system_logs' doesn't exist SQL: 
        SELECT u.*,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE created_by = u.id AND is_cancelled = 0) as total_sales_amount,
               (SELECT COUNT(*) FROM system_logs WHERE user_id = u.id AND DATE(created_at) = CURDATE()) as today_activities,
               (SELECT MAX(created_at) FROM system_logs WHERE user_id = u.id AND log_type = 'access' AND message LIKE '%login%') as last_login
        FROM users u
        WHERE u.is_active = 1
        ORDER BY u.created_at DESC
    
[29-Jul-2025 17:58:11 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\users.php(100): Database->fetchAll('\n        SELECT...')
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:01:18 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 18:01:18 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:01:24 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 18:01:24 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:06:31 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_failed_login' in 'field list' SQL: 
                    SELECT *,
                           (login_attempts >= 5 AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)) as is_locked
                    FROM users
                    WHERE username = ? AND is_active = 1
                
[29-Jul-2025 18:06:57 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_failed_login' in 'field list' SQL: 
                    SELECT *,
                           (login_attempts >= 5 AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)) as is_locked
                    FROM users
                    WHERE username = ? AND is_active = 1
                
[29-Jul-2025 18:07:09 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_failed_login' in 'field list' SQL: 
                    SELECT *,
                           (login_attempts >= 5 AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)) as is_locked
                    FROM users
                    WHERE username = ? AND is_active = 1
                
[29-Jul-2025 18:09:56 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\proj\v2\settings.php:186
Stack trace:
#0 {main}
  thrown in C:\proj\v2\settings.php on line 186
[29-Jul-2025 18:10:34 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 18:10:34 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:12:51 Asia/Kolkata] PHP Fatal error:  Uncaught TypeError: Unsupported operand types: int + PDOStatement in C:\proj\v2\cleanup-demo-notifications.php:27
Stack trace:
#0 {main}
  thrown in C:\proj\v2\cleanup-demo-notifications.php on line 27
[29-Jul-2025 18:13:23 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 18:13:23 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:16:52 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.payments' doesn't exist SQL: 
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0)
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE is_active = 1
        ORDER BY c.customer_name
    
[29-Jul-2025 18:16:52 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\customers.php(149): Database->fetchAll('\n        SELECT...', Array)
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:21:52 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined method Database::getAttribute() in C:\proj\v2\settings.php:186
Stack trace:
#0 {main}
  thrown in C:\proj\v2\settings.php on line 186
[29-Jul-2025 18:21:54 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.system_logs' doesn't exist SQL: 
        SELECT u.*,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE created_by = u.id AND is_cancelled = 0) as total_sales_amount,
               (SELECT COUNT(*) FROM system_logs WHERE user_id = u.id AND DATE(created_at) = CURDATE()) as today_activities,
               (SELECT MAX(created_at) FROM system_logs WHERE user_id = u.id AND log_type = 'access' AND message LIKE '%login%') as last_login
        FROM users u
        WHERE u.is_active = 1
        ORDER BY u.created_at DESC
    
[29-Jul-2025 18:21:54 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n        SELECT...', Array)
#1 C:\proj\v2\users.php(100): Database->fetchAll('\n        SELECT...')
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 18:22:57 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 233
[29-Jul-2025 18:24:03 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 233
[29-Jul-2025 18:24:12 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 233
[29-Jul-2025 18:24:20 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 233
[29-Jul-2025 18:24:45 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 233
[29-Jul-2025 19:02:14 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
            SELECT p.*, c.category_name, i.*,
                   (i.quantity_in_stock * p.selling_price) as stock_value,
                   CASE 
                       WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                       WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                       WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                       ELSE 'Normal'
                   END as stock_status_text
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN inventory i ON p.id = i.product_id
            WHERE p.is_active = 1
            ORDER BY product_name ASC
            LIMIT 5
        
[29-Jul-2025 19:06:32 Asia/Kolkata] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.backups' doesn't exist SQL: 
    SELECT b.*, u.full_name as created_by_name
    FROM backups b
    LEFT JOIN users u ON b.created_by = u.id
    ORDER BY b.created_at DESC
    LIMIT 20

[29-Jul-2025 19:06:32 Asia/Kolkata] PHP Fatal error:  Uncaught Exception: Database query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'jewellery_wholesale_v2.backups' doesn't exist in C:\proj\v2\config\database.php:81
Stack trace:
#0 C:\proj\v2\config\database.php(90): Database->query('\n    SELECT b.*...', Array)
#1 C:\proj\v2\backup.php(96): Database->fetchAll('\n    SELECT b.*...')
#2 {main}
  thrown in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 19:06:36 Asia/Kolkata] PHP Warning:  Undefined array key "phone" in C:\proj\v2\users.php on line 237
[29-Jul-2025 19:30:30 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined function generateTCPDF() in C:\proj\v2\generate-pdf.php:74
Stack trace:
#0 {main}
  thrown in C:\proj\v2\generate-pdf.php on line 74
[29-Jul-2025 19:30:46 Asia/Kolkata] PHP Fatal error:  Uncaught Error: Call to undefined function buildTCPDFHTML() in C:\proj\v2\generate-pdf.php:388
Stack trace:
#0 C:\proj\v2\generate-pdf.php(74): generateTCPDF(Array, Array, 'Bill_BILL-20250...')
#1 {main}
  thrown in C:\proj\v2\generate-pdf.php on line 388
[29-Jul-2025 19:46:39 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'customer_type' at row 1 SQL: 
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                
[29-Jul-2025 19:46:39 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'customer_type' at row 1 SQL: 
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                
[29-Jul-2025 19:46:39 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'customer_type' at row 1 SQL: 
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                
[29-Jul-2025 19:46:39 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'customer_type' at row 1 SQL: 
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                
[29-Jul-2025 19:46:39 Asia/Kolkata] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'customer_type' at row 1 SQL: 
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                
[29-Jul-2025 19:55:55 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '001' for key 'products.product_code' SQL: INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, hsn_code, tax_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[29-Jul-2025 20:02:53 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '001' for key 'products.product_code' SQL: INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, hsn_code, tax_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[29-Jul-2025 20:03:16 Asia/Kolkata] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '001' for key 'products.product_code' SQL: INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, hsn_code, tax_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[29-Jul-2025 20:04:41 Asia/Kolkata] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'unique_product_code'; check that column/key exists SQL: ALTER TABLE products DROP INDEX unique_product_code
[29-Jul-2025 20:04:41 Asia/Kolkata] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'product_code'; check that column/key exists SQL: ALTER TABLE products DROP INDEX product_code
[29-Jul-2025 20:04:41 Asia/Kolkata] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'products_product_code_unique'; check that column/key exists SQL: ALTER TABLE products DROP INDEX products_product_code_unique
[29-Jul-2025 20:06:08 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'selling_price' in 'field list' SQL: SELECT product_name, product_code, metal_type, purity, gross_weight, selling_price FROM products WHERE gross_weight > 0 LIMIT 5
[29-Jul-2025 20:58:22 Asia/Kolkata] Query failed: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away SQL: SELECT id FROM products WHERE product_code = ?
[29-Jul-2025 20:58:26 Asia/Kolkata] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
