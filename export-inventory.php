<?php
/**
 * Export Inventory Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Get inventory data with all related information
    $inventory = $db->fetchAll("
        SELECT p.id, p.product_name, p.product_code, p.description,
               c.category_name, p.metal_type, p.purity,
               p.base_weight, p.stone_weight, p.net_weight,
               p.stone_cost, p.making_charges, p.hsn_code,
               i.quantity_in_stock, i.minimum_stock_level, i.maximum_stock_level,
               i.reorder_point, i.cost_price, i.selling_price,
               i.location, i.rack_number,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > i.maximum_stock_level AND i.maximum_stock_level > 0 THEN 'Overstocked'
                   ELSE 'Normal'
               END as stock_status,
               (i.quantity_in_stock * i.cost_price) as stock_value
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY p.product_name
    ");

    if ($format === 'csv') {
        // CSV Export
        $filename = 'inventory_report_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Product ID', 'Product Name', 'Product Code', 'Description', 'Category',
            'Metal Type', 'Purity', 'Base Weight (gm)', 'Stone Weight (gm)', 'Net Weight (gm)',
            'Stone Cost (₹)', 'Making Charges (₹)', 'HSN Code',
            'Current Stock', 'Min Level', 'Max Level', 'Reorder Point',
            'Cost Price (₹)', 'Selling Price (₹)', 'Stock Value (₹)',
            'Location', 'Rack Number', 'Stock Status'
        ];

        fputcsv($output, $headers);

        foreach ($inventory as $item) {
            $row = [
                $item['id'],
                $item['product_name'],
                $item['product_code'],
                $item['description'],
                $item['category_name'] ?? 'Uncategorized',
                $item['metal_type'],
                $item['purity'],
                $item['base_weight'],
                $item['stone_weight'],
                $item['net_weight'],
                $item['stone_cost'],
                $item['making_charges'],
                $item['hsn_code'],
                $item['quantity_in_stock'] ?? 0,
                $item['minimum_stock_level'] ?? 0,
                $item['maximum_stock_level'] ?? 0,
                $item['reorder_point'] ?? 0,
                $item['cost_price'] ?? 0,
                $item['selling_price'] ?? 0,
                $item['stock_value'] ?? 0,
                $item['location'],
                $item['rack_number'],
                $item['stock_status']
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (simplified HTML to PDF)
        $filename = 'inventory_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // For now, we'll create an HTML version that can be printed as PDF
        // In a production environment, you'd use a library like TCPDF or mPDF
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Inventory Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .low-stock { background-color: #fff3cd; }
                .out-of-stock { background-color: #f8d7da; }
                .overstocked { background-color: #d1ecf1; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Inventory Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Category</th>
                        <th>Current Stock</th>
                        <th>Min/Max</th>
                        <th>Cost Price</th>
                        <th>Stock Value</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($inventory as $item) {
            $statusClass = '';
            switch ($item['stock_status']) {
                case 'Low Stock': $statusClass = 'low-stock'; break;
                case 'Out of Stock': $statusClass = 'out-of-stock'; break;
                case 'Overstocked': $statusClass = 'overstocked'; break;
            }
            
            echo "<tr class='$statusClass'>
                    <td>
                        <strong>" . htmlspecialchars($item['product_name']) . "</strong><br>
                        <small>" . htmlspecialchars($item['product_code']) . "</small>
                    </td>
                    <td>" . htmlspecialchars($item['category_name'] ?? 'Uncategorized') . "</td>
                    <td>" . ($item['quantity_in_stock'] ?? 0) . "</td>
                    <td>" . ($item['minimum_stock_level'] ?? 0) . "/" . ($item['maximum_stock_level'] ?? 0) . "</td>
                    <td>₹" . number_format($item['cost_price'] ?? 0, 2) . "</td>
                    <td>₹" . number_format($item['stock_value'] ?? 0, 2) . "</td>
                    <td>" . $item['stock_status'] . "</td>
                  </tr>";
        }
        
        echo "</tbody>
            </table>
            
            <script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export (using HTML table with Excel MIME type)
        $filename = 'inventory_report_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Inventory Report</title>
        </head>
        <body>
            <table border='1'>
                <tr>
                    <th>Product ID</th>
                    <th>Product Name</th>
                    <th>Product Code</th>
                    <th>Category</th>
                    <th>Metal Type</th>
                    <th>Purity</th>
                    <th>Net Weight (gm)</th>
                    <th>Current Stock</th>
                    <th>Min Level</th>
                    <th>Max Level</th>
                    <th>Cost Price</th>
                    <th>Selling Price</th>
                    <th>Stock Value</th>
                    <th>Location</th>
                    <th>Status</th>
                </tr>";

        foreach ($inventory as $item) {
            echo "<tr>
                    <td>" . $item['id'] . "</td>
                    <td>" . htmlspecialchars($item['product_name']) . "</td>
                    <td>" . htmlspecialchars($item['product_code']) . "</td>
                    <td>" . htmlspecialchars($item['category_name'] ?? 'Uncategorized') . "</td>
                    <td>" . htmlspecialchars($item['metal_type']) . "</td>
                    <td>" . htmlspecialchars($item['purity']) . "</td>
                    <td>" . $item['net_weight'] . "</td>
                    <td>" . ($item['quantity_in_stock'] ?? 0) . "</td>
                    <td>" . ($item['minimum_stock_level'] ?? 0) . "</td>
                    <td>" . ($item['maximum_stock_level'] ?? 0) . "</td>
                    <td>" . ($item['cost_price'] ?? 0) . "</td>
                    <td>" . ($item['selling_price'] ?? 0) . "</td>
                    <td>" . ($item['stock_value'] ?? 0) . "</td>
                    <td>" . htmlspecialchars($item['location']) . "</td>
                    <td>" . $item['stock_status'] . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting inventory: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='inventory.php'>← Back to Inventory</a></p>";
    echo "</div>";
}
?>
