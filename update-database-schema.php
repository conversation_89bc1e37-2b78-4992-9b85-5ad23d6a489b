<?php
/**
 * Update Database Schema for Indian Jewellery Wholesale Business
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🔧 Updating Database Schema for Indian Jewellery Business</h2>";
    
    // Track changes
    $changes_made = [];
    $errors = [];
    
    // 1. Update Products Table
    echo "<h3>📦 Updating Products Table</h3>";
    
    $product_columns = [
        'gross_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Total weight including stones"',
        'stone_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Weight of stones/gems only"', 
        'net_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Metal weight only (gross - stone)"',
        'tunch_percentage' => 'DECIMAL(5,2) DEFAULT 0 COMMENT "Purity percentage for tunch calculation"',
        'making_charges_per_gram' => 'DECIMAL(8,2) DEFAULT 0 COMMENT "Making charges per gram"',
        'wastage_percentage' => 'DECIMAL(5,2) DEFAULT 0 COMMENT "Wastage percentage"',
        'hallmark_charges' => 'DECIMAL(8,2) DEFAULT 0 COMMENT "Hallmark certification charges"',
        'va_percentage' => 'DECIMAL(5,2) DEFAULT 0 COMMENT "Value Added percentage"',
        'scheme_applicable' => 'BOOLEAN DEFAULT FALSE COMMENT "Whether schemes apply to this product"'
    ];
    
    foreach ($product_columns as $column => $definition) {
        try {
            // Check if column exists
            $exists = $db->fetchAll("SHOW COLUMNS FROM products LIKE '$column'");
            if (count($exists) == 0) {
                $db->execute("ALTER TABLE products ADD COLUMN $column $definition");
                $changes_made[] = "Added column '$column' to products table";
                echo "✅ Added column: $column<br>";
            } else {
                echo "⚠️ Column '$column' already exists<br>";
            }
        } catch (Exception $e) {
            $errors[] = "Error adding column '$column' to products: " . $e->getMessage();
            echo "❌ Error adding column '$column': " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. Update Inventory Table
    echo "<h3>📊 Updating Inventory Table</h3>";
    
    $inventory_columns = [
        'tunch_rate' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Calculated tunch rate"',
        'base_rate' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Base metal rate per gram"',
        'stone_cost_per_piece' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Stone cost per piece"',
        'making_cost' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Total making cost"',
        'total_metal_value' => 'DECIMAL(12,2) DEFAULT 0 COMMENT "Total metal value calculated"',
        'wastage_amount' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Wastage amount in rupees"'
    ];
    
    foreach ($inventory_columns as $column => $definition) {
        try {
            $exists = $db->fetchAll("SHOW COLUMNS FROM inventory LIKE '$column'");
            if (count($exists) == 0) {
                $db->execute("ALTER TABLE inventory ADD COLUMN $column $definition");
                $changes_made[] = "Added column '$column' to inventory table";
                echo "✅ Added column: $column<br>";
            } else {
                echo "⚠️ Column '$column' already exists<br>";
            }
        } catch (Exception $e) {
            $errors[] = "Error adding column '$column' to inventory: " . $e->getMessage();
            echo "❌ Error adding column '$column': " . $e->getMessage() . "<br>";
        }
    }
    
    // 3. Update Sale Items Table
    echo "<h3>💰 Updating Sale Items Table</h3>";
    
    $sale_item_columns = [
        'gross_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Gross weight at time of sale"',
        'stone_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Stone weight at time of sale"',
        'net_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Net metal weight"',
        'tunch_weight' => 'DECIMAL(8,3) DEFAULT 0 COMMENT "Calculated tunch weight"',
        'tunch_rate' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Tunch rate used"',
        'metal_value' => 'DECIMAL(12,2) DEFAULT 0 COMMENT "Metal value (tunch_weight × tunch_rate)"',
        'stone_charges' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Stone charges"',
        'making_charges' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Making charges"',
        'va_amount' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Value Added amount"',
        'wastage_amount' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Wastage amount"',
        'scheme_discount' => 'DECIMAL(10,2) DEFAULT 0 COMMENT "Scheme discount applied"'
    ];
    
    foreach ($sale_item_columns as $column => $definition) {
        try {
            $exists = $db->fetchAll("SHOW COLUMNS FROM sale_items LIKE '$column'");
            if (count($exists) == 0) {
                $db->execute("ALTER TABLE sale_items ADD COLUMN $column $definition");
                $changes_made[] = "Added column '$column' to sale_items table";
                echo "✅ Added column: $column<br>";
            } else {
                echo "⚠️ Column '$column' already exists<br>";
            }
        } catch (Exception $e) {
            $errors[] = "Error adding column '$column' to sale_items: " . $e->getMessage();
            echo "❌ Error adding column '$column': " . $e->getMessage() . "<br>";
        }
    }
    
    // 4. Create Daily Rates Table
    echo "<h3>📈 Creating Daily Rates Table</h3>";
    
    try {
        $exists = $db->fetchAll("SHOW TABLES LIKE 'daily_rates'");
        if (count($exists) == 0) {
            $daily_rates_sql = "
            CREATE TABLE daily_rates (
                id INT PRIMARY KEY AUTO_INCREMENT,
                rate_date DATE NOT NULL,
                metal_type VARCHAR(20) NOT NULL COMMENT 'Gold, Silver, etc.',
                purity VARCHAR(10) NOT NULL COMMENT '22K, 18K, 925, etc.',
                base_rate DECIMAL(10,2) NOT NULL COMMENT 'Base rate per gram',
                tunch_rate DECIMAL(10,2) NOT NULL COMMENT 'Calculated tunch rate',
                market_rate DECIMAL(10,2) DEFAULT 0 COMMENT 'Market reference rate',
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_rate (rate_date, metal_type, purity),
                INDEX idx_rate_date (rate_date),
                INDEX idx_metal_purity (metal_type, purity),
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->execute($daily_rates_sql);
            $changes_made[] = "Created daily_rates table";
            echo "✅ Created daily_rates table<br>";
        } else {
            echo "⚠️ daily_rates table already exists<br>";
        }
    } catch (Exception $e) {
        $errors[] = "Error creating daily_rates table: " . $e->getMessage();
        echo "❌ Error creating daily_rates table: " . $e->getMessage() . "<br>";
    }
    
    // 5. Create Schemes Table
    echo "<h3>🎁 Creating Schemes Table</h3>";
    
    try {
        $exists = $db->fetchAll("SHOW TABLES LIKE 'schemes'");
        if (count($exists) == 0) {
            $schemes_sql = "
            CREATE TABLE schemes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                scheme_name VARCHAR(100) NOT NULL,
                scheme_type ENUM('discount', 'making_free', 'stone_free', 'cashback') NOT NULL,
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                discount_amount DECIMAL(10,2) DEFAULT 0,
                min_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'Minimum purchase amount',
                max_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'Maximum discount amount',
                applicable_categories TEXT COMMENT 'JSON array of applicable category IDs',
                valid_from DATE NOT NULL,
                valid_to DATE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_validity (valid_from, valid_to),
                INDEX idx_scheme_type (scheme_type),
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->execute($schemes_sql);
            $changes_made[] = "Created schemes table";
            echo "✅ Created schemes table<br>";
        } else {
            echo "⚠️ schemes table already exists<br>";
        }
    } catch (Exception $e) {
        $errors[] = "Error creating schemes table: " . $e->getMessage();
        echo "❌ Error creating schemes table: " . $e->getMessage() . "<br>";
    }
    
    // 6. Create Tunch Calculations Table (for audit trail)
    echo "<h3>🧮 Creating Tunch Calculations Table</h3>";
    
    try {
        $exists = $db->fetchAll("SHOW TABLES LIKE 'tunch_calculations'");
        if (count($exists) == 0) {
            $tunch_sql = "
            CREATE TABLE tunch_calculations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                sale_item_id INT NOT NULL,
                gross_weight DECIMAL(8,3) NOT NULL,
                stone_weight DECIMAL(8,3) NOT NULL,
                net_weight DECIMAL(8,3) NOT NULL,
                tunch_percentage DECIMAL(5,2) NOT NULL,
                tunch_weight DECIMAL(8,3) NOT NULL,
                base_rate DECIMAL(10,2) NOT NULL,
                tunch_rate DECIMAL(10,2) NOT NULL,
                metal_value DECIMAL(12,2) NOT NULL,
                making_charges DECIMAL(10,2) DEFAULT 0,
                stone_charges DECIMAL(10,2) DEFAULT 0,
                wastage_amount DECIMAL(10,2) DEFAULT 0,
                final_amount DECIMAL(12,2) NOT NULL,
                calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sale_item_id) REFERENCES sale_items(id) ON DELETE CASCADE,
                INDEX idx_sale_item (sale_item_id),
                INDEX idx_calculation_date (calculation_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->execute($tunch_sql);
            $changes_made[] = "Created tunch_calculations table";
            echo "✅ Created tunch_calculations table<br>";
        } else {
            echo "⚠️ tunch_calculations table already exists<br>";
        }
    } catch (Exception $e) {
        $errors[] = "Error creating tunch_calculations table: " . $e->getMessage();
        echo "❌ Error creating tunch_calculations table: " . $e->getMessage() . "<br>";
    }
    
    // Summary
    echo "<h3>📋 Update Summary</h3>";
    
    if (count($changes_made) > 0) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Changes Made Successfully:</h4>";
        echo "<ul>";
        foreach ($changes_made as $change) {
            echo "<li>$change</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (count($errors) > 0) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Errors Encountered:</h4>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Next Steps
    echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎯 Next Steps</h3>";
    echo "<ol>";
    echo "<li><strong>Add Sample Data:</strong> Insert sample daily rates and schemes</li>";
    echo "<li><strong>Update Existing Products:</strong> Add tunch percentages and weights</li>";
    echo "<li><strong>Implement Tunch Calculator:</strong> Create calculation engine</li>";
    echo "<li><strong>Update Billing System:</strong> Integrate new calculations</li>";
    echo "<li><strong>Test Calculations:</strong> Verify with real jewellery data</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='add-sample-jewellery-data.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Add Sample Data</a>";
    echo "<a href='implement-tunch-calculator.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Implement Calculator</a>";
    echo "<a href='test-database-changes.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Changes</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Error</h4>";
    echo "<p>Error updating database schema: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
