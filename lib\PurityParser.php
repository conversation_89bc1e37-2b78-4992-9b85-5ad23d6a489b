<?php
/**
 * Purity Parser - Advanced Purity/Tunch Handling System
 * Based on the TypeScript reference implementation
 */

class PurityParser {
    
    // Standard purity reference table
    private static $PURITY_REFERENCE = [
        'Gold' => [
            '999' => ['percentage' => 99.9, 'tunch' => 999, 'label' => '24K Gold (999)'],
            '995' => ['percentage' => 99.5, 'tunch' => 995, 'label' => '24K Gold (995)'],
            '916' => ['percentage' => 91.6, 'tunch' => 916, 'label' => '22K Gold (916)'],
            '875' => ['percentage' => 87.5, 'tunch' => 875, 'label' => '21K Gold (875)'],
            '833' => ['percentage' => 83.3, 'tunch' => 833, 'label' => '20K Gold (833)'],
            '750' => ['percentage' => 75.0, 'tunch' => 750, 'label' => '18K Gold (750)'],
            '585' => ['percentage' => 58.5, 'tunch' => 585, 'label' => '14K Gold (585)'],
            '417' => ['percentage' => 41.7, 'tunch' => 417, 'label' => '10K Gold (417)'],
            '22K' => ['percentage' => 91.6, 'tunch' => 916, 'label' => '22K Gold'],
            '18K' => ['percentage' => 75.0, 'tunch' => 750, 'label' => '18K Gold'],
            '14K' => ['percentage' => 58.5, 'tunch' => 585, 'label' => '14K Gold'],
            '10K' => ['percentage' => 41.7, 'tunch' => 417, 'label' => '10K Gold']
        ],
        'Silver' => [
            '999' => ['percentage' => 99.9, 'tunch' => 999, 'label' => 'Fine Silver (999)'],
            '958' => ['percentage' => 95.8, 'tunch' => 958, 'label' => 'Britannia Silver (958)'],
            '925' => ['percentage' => 92.5, 'tunch' => 925, 'label' => 'Sterling Silver (925)'],
            '900' => ['percentage' => 90.0, 'tunch' => 900, 'label' => 'Coin Silver (900)'],
            '835' => ['percentage' => 83.5, 'tunch' => 835, 'label' => 'European Silver (835)'],
            '800' => ['percentage' => 80.0, 'tunch' => 800, 'label' => 'German Silver (800)']
        ],
        'Platinum' => [
            '999' => ['percentage' => 99.9, 'tunch' => 999, 'label' => 'Pure Platinum (999)'],
            '950' => ['percentage' => 95.0, 'tunch' => 950, 'label' => 'Platinum (950)'],
            '900' => ['percentage' => 90.0, 'tunch' => 900, 'label' => 'Platinum (900)'],
            '850' => ['percentage' => 85.0, 'tunch' => 850, 'label' => 'Platinum (850)']
        ]
    ];
    
    /**
     * Parse purity input and return detailed purity information
     * 
     * @param mixed $purityInput - Can be number or string
     * @param string $metalType - Optional metal type for reference lookup
     * @return array PurityInfo with percentage, tunch, label, format
     */
    public static function parsePurity($purityInput, $metalType = null) {
        $purityStr = trim((string)$purityInput);
        
        // Check standard reference table for exact string matches
        if ($metalType && is_string($purityInput) && isset(self::$PURITY_REFERENCE[$metalType])) {
            $metalPurities = self::$PURITY_REFERENCE[$metalType];
            
            if (isset($metalPurities[$purityStr])) {
                $standardPurity = $metalPurities[$purityStr];
                return [
                    'percentage' => $standardPurity['percentage'],
                    'tunch' => $standardPurity['tunch'],
                    'label' => $standardPurity['label'],
                    'format' => 'standard'
                ];
            }
        }
        
        // Parse any numeric input as direct percentage
        $numericPurity = floatval($purityStr);
        
        if (!is_numeric($purityStr) || $numericPurity < 0) {
            throw new InvalidArgumentException("Invalid purity value: $purityInput");
        }
        
        // ANY number = direct percentage (no conversions, no restrictions)
        $percentage = $numericPurity;
        $tunch = round($numericPurity * 10);
        $label = "{$percentage}% (Custom)";
        
        return [
            'percentage' => $percentage,
            'tunch' => $tunch,
            'label' => $label,
            'format' => 'custom'
        ];
    }
    
    /**
     * Get purity percentage from any input format
     */
    public static function getPurityPercentage($purityInput, $metalType = null) {
        return self::parsePurity($purityInput, $metalType)['percentage'];
    }
    
    /**
     * Get tunch value from any input format
     */
    public static function getTunchValue($purityInput, $metalType = null) {
        return self::parsePurity($purityInput, $metalType)['tunch'];
    }
    
    /**
     * Get all available purities for a metal type
     */
    public static function getAvailablePurities($metalType) {
        return isset(self::$PURITY_REFERENCE[$metalType]) ? self::$PURITY_REFERENCE[$metalType] : [];
    }
    
    /**
     * Get all supported metal types
     */
    public static function getSupportedMetalTypes() {
        return array_keys(self::$PURITY_REFERENCE);
    }
    
    /**
     * Validate purity input
     */
    public static function validatePurity($purityInput, $metalType = null) {
        try {
            self::parsePurity($purityInput, $metalType);
            return ['valid' => true, 'errors' => []];
        } catch (Exception $e) {
            return ['valid' => false, 'errors' => [$e->getMessage()]];
        }
    }
    
    /**
     * Format purity for display
     */
    public static function formatPurity($purityInput, $metalType = null) {
        try {
            $purityInfo = self::parsePurity($purityInput, $metalType);
            return $purityInfo['label'];
        } catch (Exception $e) {
            return "Invalid Purity";
        }
    }
    
    /**
     * Get purity suggestions based on metal type
     */
    public static function getPuritySuggestions($metalType) {
        $suggestions = [];
        $purities = self::getAvailablePurities($metalType);
        
        foreach ($purities as $code => $info) {
            $suggestions[] = [
                'code' => $code,
                'percentage' => $info['percentage'],
                'tunch' => $info['tunch'],
                'label' => $info['label'],
                'display' => "{$code} ({$info['percentage']}%)"
            ];
        }
        
        return $suggestions;
    }
    
    /**
     * Convert between different purity formats
     */
    public static function convertPurity($purityInput, $fromFormat, $toFormat, $metalType = null) {
        $purityInfo = self::parsePurity($purityInput, $metalType);
        
        switch ($toFormat) {
            case 'percentage':
                return $purityInfo['percentage'];
            case 'tunch':
                return $purityInfo['tunch'];
            case 'decimal':
                return $purityInfo['percentage'] / 100;
            case 'label':
                return $purityInfo['label'];
            default:
                return $purityInput;
        }
    }
    
    /**
     * Get purity range validation for metal type
     */
    public static function getPurityRange($metalType) {
        $ranges = [
            'Gold' => ['min' => 10, 'max' => 100, 'typical' => [41.7, 58.5, 75.0, 91.6, 99.9]],
            'Silver' => ['min' => 50, 'max' => 100, 'typical' => [80.0, 83.5, 90.0, 92.5, 95.8, 99.9]],
            'Platinum' => ['min' => 80, 'max' => 100, 'typical' => [85.0, 90.0, 95.0, 99.9]]
        ];
        
        return isset($ranges[$metalType]) ? $ranges[$metalType] : ['min' => 0, 'max' => 100, 'typical' => []];
    }
    
    /**
     * Check if purity is within typical range for metal
     */
    public static function isTypicalPurity($purityInput, $metalType) {
        $purityInfo = self::parsePurity($purityInput, $metalType);
        $range = self::getPurityRange($metalType);
        
        $percentage = $purityInfo['percentage'];
        
        // Check if it's a standard purity
        if ($purityInfo['format'] === 'standard') {
            return true;
        }
        
        // Check if it's within typical range
        return $percentage >= $range['min'] && $percentage <= $range['max'];
    }
    
    /**
     * Get purity recommendations based on usage
     */
    public static function getPurityRecommendations($metalType, $usage = 'jewelry') {
        $recommendations = [
            'Gold' => [
                'jewelry' => ['916', '750', '585'],
                'investment' => ['999', '995'],
                'industrial' => ['750', '585', '417']
            ],
            'Silver' => [
                'jewelry' => ['925', '900'],
                'investment' => ['999', '958'],
                'industrial' => ['925', '835', '800']
            ],
            'Platinum' => [
                'jewelry' => ['950', '900'],
                'investment' => ['999'],
                'industrial' => ['950', '900', '850']
            ]
        ];
        
        $metalRecs = isset($recommendations[$metalType]) ? $recommendations[$metalType] : [];
        $usageRecs = isset($metalRecs[$usage]) ? $metalRecs[$usage] : [];
        
        $result = [];
        foreach ($usageRecs as $purityCode) {
            if (isset(self::$PURITY_REFERENCE[$metalType][$purityCode])) {
                $result[] = self::$PURITY_REFERENCE[$metalType][$purityCode];
            }
        }
        
        return $result;
    }
}
?>
