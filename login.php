<?php
/**
 * Login Page - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';
$show_2fa = false;
$show_captcha = false;
$account_locked = false;

// Check for password reset token
$reset_token = $_GET['reset_token'] ?? '';
$show_reset_form = !empty($reset_token);

if ($_POST) {
    $action = $_POST['action'] ?? 'login';

    if ($action === 'login') {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $captcha = $_POST['captcha'] ?? '';
        $remember_me = isset($_POST['remember_me']);

        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            try {
                $db = getDB();

                // Check for account lockout
                $user = $db->fetch("
                    SELECT *,
                           (login_attempts >= 5 AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)) as is_locked
                    FROM users
                    WHERE username = ? AND is_active = 1
                ", [$username]);

                if ($user && $user['is_locked']) {
                    $account_locked = true;
                    $error = 'Account temporarily locked due to multiple failed login attempts. Please try again in 30 minutes or contact administrator.';
                } elseif ($user && password_verify($password, $user['password_hash'])) {

                    // Check if 2FA is enabled
                    if ($user['two_factor_enabled']) {
                        $show_2fa = true;
                        $_SESSION['temp_user_id'] = $user['id'];
                        $_SESSION['temp_username'] = $user['username'];
                    } else {
                        // Complete login
                        completeLogin($user, $remember_me, $db);
                    }

                } else {
                    // Update failed login attempts
                    if ($user) {
                        $db->query("
                            UPDATE users
                            SET login_attempts = login_attempts + 1,
                                last_failed_login = NOW()
                            WHERE id = ?
                        ", [$user['id']]);

                        // Log security event
                        $db->query("
                            INSERT INTO system_logs (log_type, level, message, context, ip_address, created_at)
                            VALUES ('security', 'warning', 'Failed login attempt', ?, ?, NOW())
                        ", [
                            json_encode(['username' => $username, 'attempts' => $user['login_attempts'] + 1]),
                            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                        ]);
                    }

                    $error = 'Invalid username or password.';

                    // Show captcha after 3 failed attempts
                    if ($user && $user['login_attempts'] >= 2) {
                        $show_captcha = true;
                    }
                }
            } catch (Exception $e) {
                logError("Login error: " . $e->getMessage());
                $error = 'Login failed. Please try again.';
            }
        }

    } elseif ($action === 'verify_2fa') {
        $code = $_POST['2fa_code'] ?? '';
        $user_id = $_SESSION['temp_user_id'] ?? null;

        if (empty($code) || !$user_id) {
            $error = 'Please enter the verification code.';
            $show_2fa = true;
        } else {
            try {
                $db = getDB();
                $user = $db->fetch("SELECT * FROM users WHERE id = ? AND is_active = 1", [$user_id]);

                if ($user && verify2FACode($user['two_factor_secret'], $code)) {
                    // Complete login
                    completeLogin($user, false, $db);

                    // Clear temp session
                    unset($_SESSION['temp_user_id'], $_SESSION['temp_username']);
                } else {
                    $error = 'Invalid verification code. Please try again.';
                    $show_2fa = true;
                }
            } catch (Exception $e) {
                logError("2FA verification error: " . $e->getMessage());
                $error = '2FA verification failed. Please try again.';
                $show_2fa = true;
            }
        }

    } elseif ($action === 'forgot_password') {
        $email = sanitizeInput($_POST['email'] ?? '');

        if (empty($email)) {
            $error = 'Please enter your email address.';
        } elseif (!validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                $db = getDB();
                $user = $db->fetch("SELECT * FROM users WHERE email = ? AND is_active = 1", [$email]);

                if ($user) {
                    // Generate reset token
                    $reset_token = bin2hex(random_bytes(32));
                    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

                    // Store reset token
                    $db->query("
                        INSERT INTO password_resets (user_id, token, expires_at)
                        VALUES (?, ?, ?)
                        ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)
                    ", [$user['id'], $reset_token, $expires_at]);

                    // Send reset email (simulated)
                    $reset_link = "http://" . $_SERVER['HTTP_HOST'] . "/login.php?reset_token=" . $reset_token;

                    // Log password reset request
                    $db->query("
                        INSERT INTO system_logs (log_type, level, message, context, user_id, ip_address, created_at)
                        VALUES ('security', 'info', 'Password reset requested', ?, ?, ?, NOW())
                    ", [
                        json_encode(['email' => $email, 'reset_link' => $reset_link]),
                        $user['id'],
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                }

                // Always show success message for security
                $success = 'If an account with that email exists, a password reset link has been sent.';

            } catch (Exception $e) {
                logError("Password reset error: " . $e->getMessage());
                $error = 'Password reset failed. Please try again.';
            }
        }

    } elseif ($action === 'reset_password') {
        $token = $_POST['reset_token'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        if (empty($new_password) || empty($confirm_password)) {
            $error = 'Please enter and confirm your new password.';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Passwords do not match.';
        } elseif (strlen($new_password) < 6) {
            $error = 'Password must be at least 6 characters long.';
        } else {
            try {
                $db = getDB();

                // Verify reset token
                $reset = $db->fetch("
                    SELECT pr.*, u.username
                    FROM password_resets pr
                    JOIN users u ON pr.user_id = u.id
                    WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = 0
                ", [$token]);

                if ($reset) {
                    // Update password
                    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                    $db->query("UPDATE users SET password_hash = ? WHERE id = ?", [$password_hash, $reset['user_id']]);

                    // Mark token as used
                    $db->query("UPDATE password_resets SET used = 1 WHERE token = ?", [$token]);

                    // Log password reset
                    $db->query("
                        INSERT INTO system_logs (log_type, level, message, context, user_id, ip_address, created_at)
                        VALUES ('security', 'info', 'Password reset completed', ?, ?, ?, NOW())
                    ", [
                        json_encode(['username' => $reset['username']]),
                        $reset['user_id'],
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);

                    $success = 'Password reset successfully. You can now login with your new password.';
                    $show_reset_form = false;
                } else {
                    $error = 'Invalid or expired reset token.';
                }

            } catch (Exception $e) {
                logError("Password reset completion error: " . $e->getMessage());
                $error = 'Password reset failed. Please try again.';
            }
        }
    }
}

function completeLogin($user, $remember_me, $db) {
    // Update last login
    $db->query("UPDATE users SET last_login = NOW(), login_attempts = 0 WHERE id = ?", [$user['id']]);

    // Set session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['full_name'] = $user['full_name'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['login_time'] = time();

    // Set remember me cookie
    if ($remember_me) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30 days

        setcookie('remember_token', $token, $expires, '/', '', false, true);

        // Store token in database
        $db->query("
            INSERT INTO remember_tokens (user_id, token, expires_at)
            VALUES (?, ?, ?)
        ", [$user['id'], $token, date('Y-m-d H:i:s', $expires)]);
    }

    // Log successful login
    $db->query("
        INSERT INTO system_logs (log_type, level, message, context, user_id, ip_address, created_at)
        VALUES ('access', 'info', 'User login successful', ?, ?, ?, NOW())
    ", [
        json_encode(['username' => $user['username'], 'role' => $user['role']]),
        $user['id'],
        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);

    // Redirect to dashboard
    header('Location: index.php');
    exit;
}

function verify2FACode($secret, $code) {
    // Simple TOTP verification (in production, use a proper TOTP library)
    $time = floor(time() / 30);
    $expected = substr(hash_hmac('sha1', pack('N*', 0) . pack('N*', $time), base32_decode($secret)), -6);
    return $code === $expected;
}

function base32_decode($data) {
    // Simple base32 decode (in production, use a proper library)
    return base64_decode(strtr($data, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567', 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef'));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 20px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 10px 0 0 10px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="mb-2">
                <i class="fas fa-gem me-2"></i>
                <?php echo APP_NAME; ?>
            </h1>
            <p class="mb-0">Management System v2.0</p>
        </div>
        
        <div class="login-body">
            <?php if ($show_reset_form): ?>
                <!-- Password Reset Form -->
                <h4 class="text-center mb-4">Reset Password</h4>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="reset_token" value="<?php echo htmlspecialchars($reset_token); ?>">

                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   placeholder="Enter new password" required minlength="6">
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="Confirm new password" required minlength="6">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login w-100">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </button>
                </form>

                <div class="text-center mt-3">
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>Back to Login
                    </a>
                </div>

            <?php elseif ($show_2fa): ?>
                <!-- 2FA Verification Form -->
                <h4 class="text-center mb-4">Two-Factor Authentication</h4>
                <p class="text-center text-muted mb-4">Enter the 6-digit code from your authenticator app</p>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <input type="hidden" name="action" value="verify_2fa">

                    <div class="mb-4">
                        <label for="2fa_code" class="form-label">Verification Code</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-shield-alt"></i>
                            </span>
                            <input type="text" class="form-control text-center" id="2fa_code" name="2fa_code"
                                   placeholder="000000" maxlength="6" pattern="[0-9]{6}" required
                                   style="font-size: 1.2em; letter-spacing: 0.2em;">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login w-100">
                        <i class="fas fa-check me-2"></i>Verify Code
                    </button>
                </form>

                <div class="text-center mt-3">
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>Back to Login
                    </a>
                </div>

            <?php else: ?>
                <!-- Main Login Form -->
                <h4 class="text-center mb-4">Welcome Back!</h4>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        <?php if ($account_locked): ?>
                            <div class="mt-2">
                                <small>
                                    <a href="#" onclick="showForgotPassword()" class="text-decoration-none">
                                        Forgot your password?
                                    </a>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" id="loginForm">
                    <input type="hidden" name="action" value="login">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   placeholder="Enter your username" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Enter your password" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <?php if ($show_captcha): ?>
                        <div class="mb-3">
                            <label for="captcha" class="form-label">Security Check</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-robot"></i>
                                </span>
                                <input type="text" class="form-control" id="captcha" name="captcha"
                                       placeholder="Enter CAPTCHA" required>
                            </div>
                            <div class="mt-2 p-2 bg-light text-center" style="font-family: monospace; font-size: 1.2em; letter-spacing: 0.2em;">
                                DEMO123
                            </div>
                            <small class="text-muted">Enter the code shown above</small>
                        </div>
                    <?php endif; ?>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            Remember me for 30 days
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login w-100">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </button>
                </form>

                <div class="text-center mt-3">
                    <a href="#" onclick="showForgotPassword()" class="text-decoration-none">
                        <i class="fas fa-key me-1"></i>Forgot Password?
                    </a>
                </div>

                <!-- Forgot Password Form (Hidden) -->
                <form method="POST" id="forgotPasswordForm" style="display: none;">
                    <input type="hidden" name="action" value="forgot_password">

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="Enter your email address" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-warning w-100 mb-2">
                        <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                    </button>

                    <button type="button" class="btn btn-outline-secondary w-100" onclick="showLogin()">
                        <i class="fas fa-arrow-left me-2"></i>Back to Login
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="demo-credentials">
                <h6><i class="fas fa-info-circle me-2"></i>Demo Credentials</h6>
                <p class="mb-1"><strong>Username:</strong> admin</p>
                <p class="mb-0"><strong>Password:</strong> admin123</p>
            </div>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Secure login with encrypted passwords
                </small>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            togglePassword.addEventListener('click', function() {
                const password = document.getElementById('password');
                const icon = this.querySelector('i');

                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        // Auto-focus appropriate field
        <?php if ($show_2fa): ?>
            document.getElementById('2fa_code').focus();
        <?php elseif ($show_reset_form): ?>
            document.getElementById('new_password').focus();
        <?php else: ?>
            const usernameField = document.getElementById('username');
            if (usernameField) usernameField.focus();
        <?php endif; ?>

        // Show/hide forgot password form
        function showForgotPassword() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'block';
            document.querySelector('.login-body h4').textContent = 'Reset Password';
            document.getElementById('email').focus();
        }

        function showLogin() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('forgotPasswordForm').style.display = 'none';
            document.querySelector('.login-body h4').textContent = 'Welcome Back!';
            document.getElementById('username').focus();
        }

        // 2FA code input formatting
        const tfaCode = document.getElementById('2fa_code');
        if (tfaCode) {
            tfaCode.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 6);
            });

            tfaCode.addEventListener('keyup', function() {
                if (this.value.length === 6) {
                    this.form.submit();
                }
            });
        }

        // Password strength indicator
        const newPassword = document.getElementById('new_password');
        if (newPassword) {
            newPassword.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrengthIndicator(strength);
            });
        }

        function calculatePasswordStrength(password) {
            let score = 0;
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;
            return score;
        }

        function updatePasswordStrengthIndicator(strength) {
            // This would update a password strength indicator
            console.log('Password strength:', strength);
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Please wait...';

                        // Re-enable after 5 seconds to prevent permanent disable
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = submitBtn.innerHTML.replace('Please wait...', submitBtn.textContent.includes('Login') ? 'Login' : 'Submit');
                        }, 5000);
                    }
                });
            });
        });

        // Security monitoring
        let loginAttempts = 0;
        const maxAttempts = 5;

        function trackLoginAttempt() {
            loginAttempts++;
            if (loginAttempts >= maxAttempts) {
                document.getElementById('loginForm').style.display = 'none';
                document.querySelector('.login-body').innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-shield-alt fa-2x mb-3"></i>
                        <h5>Too Many Attempts</h5>
                        <p>For security reasons, login has been temporarily disabled.</p>
                        <p>Please refresh the page and try again.</p>
                    </div>
                `;
            }
        }

        // Auto-logout warning
        let warningShown = false;
        function checkSessionTimeout() {
            // This would check session timeout and show warning
            if (!warningShown && window.location.pathname !== '/login.php') {
                // Implementation for session timeout warning
            }
        }

        // Check every 5 minutes
        setInterval(checkSessionTimeout, 5 * 60 * 1000);
    </script>
</body>
</html>
