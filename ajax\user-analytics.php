<?php
/**
 * User Analytics AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Get user statistics
    $totalUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'];
    $activeUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['count'];
    $adminUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND role = 'admin'")['count'];
    $managerUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND role = 'manager'")['count'];
    $staffUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND role = 'staff'")['count'];
    
    // Get user activity data (simplified without system_logs)
    $userActivity = $db->fetchAll("
        SELECT u.full_name, u.role,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as total_activities,
               0 as today_activities,
               u.last_login as last_activity
        FROM users u
        WHERE u.is_active = 1
        ORDER BY total_activities DESC
        LIMIT 10
    ");
    
    // Get top performers by sales
    $topPerformers = $db->fetchAll("
        SELECT u.full_name, u.role,
               COUNT(s.id) as sales_count,
               COALESCE(SUM(s.grand_total), 0) as total_revenue
        FROM users u
        LEFT JOIN sales s ON u.id = s.created_by AND s.is_cancelled = 0
        WHERE u.is_active = 1
        GROUP BY u.id, u.full_name, u.role
        HAVING sales_count > 0
        ORDER BY total_revenue DESC
        LIMIT 5
    ");
    
    // Get role distribution
    $roleDistribution = $db->fetchAll("
        SELECT role, COUNT(*) as count
        FROM users 
        WHERE is_active = 1
        GROUP BY role
        ORDER BY count DESC
    ");
    
    // Calculate percentages for role distribution
    foreach ($roleDistribution as &$role) {
        $role['percentage'] = $totalUsers > 0 ? round(($role['count'] / $totalUsers) * 100, 1) : 0;
    }
    
    // Get login activity for the last 7 days (simplified without system_logs)
    $loginActivity = [];
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $loginActivity[] = [
            'login_date' => $date,
            'unique_logins' => rand(1, 5), // Demo data
            'total_logins' => rand(1, 8)   // Demo data
        ];
    }
    
    // Get user registration trend (last 30 days)
    $registrationTrend = $db->fetchAll("
        SELECT DATE(created_at) as reg_date,
               COUNT(*) as new_users
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY reg_date DESC
    ");
    
    // Prepare statistics
    $statistics = [
        [
            'label' => 'Total Users',
            'value' => $totalUsers
        ],
        [
            'label' => 'Active Users (30 days)',
            'value' => $activeUsers
        ],
        [
            'label' => 'Administrators',
            'value' => $adminUsers
        ],
        [
            'label' => 'Managers',
            'value' => $managerUsers
        ],
        [
            'label' => 'Staff Members',
            'value' => $staffUsers
        ],
        [
            'label' => 'Activity Rate',
            'value' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) . '%' : '0%'
        ]
    ];
    
    // Format top performers
    $formattedTopPerformers = [];
    foreach ($topPerformers as $performer) {
        $formattedTopPerformers[] = [
            'name' => $performer['full_name'],
            'role' => ucfirst($performer['role']),
            'sales' => $performer['sales_count'],
            'revenue' => number_format($performer['total_revenue'], 2)
        ];
    }
    
    // Format role distribution
    $formattedRoleDistribution = [];
    foreach ($roleDistribution as $role) {
        $formattedRoleDistribution[] = [
            'role' => ucfirst($role['role']),
            'count' => $role['count'],
            'percentage' => $role['percentage']
        ];
    }
    
    // Format activity data for chart
    $activityChartData = [];
    foreach ($userActivity as $activity) {
        $activityChartData[] = [
            'user' => $activity['full_name'],
            'total' => $activity['total_activities'],
            'today' => $activity['today_activities'],
            'role' => $activity['role']
        ];
    }
    
    // Get security insights
    $securityInsights = [];
    
    // Check for users without recent activity (simplified)
    $inactiveUsers = $db->fetch("
        SELECT COUNT(*) as count
        FROM users u
        WHERE u.is_active = 1
        AND (u.last_login IS NULL OR u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY))
    ")['count'];
    
    if ($inactiveUsers > 0) {
        $securityInsights[] = [
            'type' => 'warning',
            'title' => 'Inactive Users',
            'description' => "$inactiveUsers users haven't been active in the last 30 days"
        ];
    }
    
    // Check for admin count
    if ($adminUsers < 2) {
        $securityInsights[] = [
            'type' => 'warning',
            'title' => 'Admin Backup',
            'description' => 'Consider having at least 2 admin users for redundancy'
        ];
    }
    
    // Check for password reset needs (simulated)
    $passwordResetNeeded = $db->fetch("
        SELECT COUNT(*) as count 
        FROM users 
        WHERE is_active = 1 
        AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
        AND password_hash IS NOT NULL
    ")['count'];
    
    if ($passwordResetNeeded > 0) {
        $securityInsights[] = [
            'type' => 'info',
            'title' => 'Password Policy',
            'description' => "$passwordResetNeeded users may need password updates (90+ days old accounts)"
        ];
    }
    
    // Return analytics data
    echo json_encode([
        'success' => true,
        'statistics' => $statistics,
        'topPerformers' => $formattedTopPerformers,
        'roleDistribution' => $formattedRoleDistribution,
        'activity' => $activityChartData,
        'loginActivity' => $loginActivity,
        'registrationTrend' => $registrationTrend,
        'securityInsights' => $securityInsights,
        'summary' => [
            'totalUsers' => $totalUsers,
            'activeUsers' => $activeUsers,
            'activityRate' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) : 0,
            'topPerformerRevenue' => !empty($topPerformers) ? number_format($topPerformers[0]['total_revenue'], 2) : '0.00',
            'lastUpdated' => date('d/m/Y H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
