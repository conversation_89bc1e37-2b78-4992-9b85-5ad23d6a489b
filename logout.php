<?php
/**
 * Logout Page - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'] ?? null;
    $username = $_SESSION['username'] ?? 'Unknown';

    if ($user_id) {
        // Log logout event
        $db->query("
            INSERT INTO system_logs (log_type, level, message, context, user_id, ip_address, created_at)
            VALUES ('access', 'info', 'User logout', ?, ?, ?, NOW())
        ", [
            json_encode(['username' => $username, 'session_duration' => time() - ($_SESSION['login_time'] ?? time())]),
            $user_id,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);

        // Deactivate current session
        if (session_id()) {
            $db->query("
                UPDATE login_sessions
                SET is_active = 0
                WHERE session_id = ? AND user_id = ?
            ", [session_id(), $user_id]);
        }

        // Remove remember me token if exists
        if (isset($_COOKIE['remember_token'])) {
            $db->query("
                DELETE FROM remember_tokens
                WHERE token = ? AND user_id = ?
            ", [$_COOKIE['remember_token'], $user_id]);

            // Clear remember me cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }

} catch (Exception $e) {
    // Log error but continue with logout
    error_log("Logout error: " . $e->getMessage());
}

// Clear all session data
$_SESSION = array();

// Destroy session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy session
session_destroy();

// Clear any other authentication cookies
setcookie('auth_token', '', time() - 3600, '/', '', false, true);
setcookie('user_preferences', '', time() - 3600, '/', '', false, true);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logged Out - <?php echo APP_NAME; ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }

        .logout-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 20px;
            text-align: center;
        }

        .logout-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
        }

        .logout-body {
            padding: 2rem;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
            display: inline-block;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .security-tips {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            text-align: left;
        }

        .logout-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-header">
            <i class="fas fa-check-circle logout-icon"></i>
            <h2 class="mb-0">Successfully Logged Out</h2>
        </div>

        <div class="logout-body">
            <h5 class="mb-3">Thank you for using <?php echo APP_NAME; ?></h5>
            <p class="text-muted mb-4">Your session has been securely terminated.</p>

            <div class="d-grid gap-2">
                <a href="login.php" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>Login Again
                </a>

                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-2"></i>Go to Homepage
                </a>
            </div>

            <div class="security-tips">
                <h6><i class="fas fa-shield-alt me-2"></i>Security Tips</h6>
                <ul class="mb-0 small">
                    <li>Always log out when using shared computers</li>
                    <li>Close your browser after logging out</li>
                    <li>Never share your login credentials</li>
                    <li>Report any suspicious activity immediately</li>
                </ul>
            </div>

            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    Logged out at <?php echo date('d/m/Y H:i:s'); ?>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Clear any cached data
        if ('caches' in window) {
            caches.keys().then(function(names) {
                names.forEach(function(name) {
                    caches.delete(name);
                });
            });
        }

        // Clear local storage (be careful with this in production)
        try {
            localStorage.removeItem('user_preferences');
            localStorage.removeItem('temp_data');
            sessionStorage.clear();
        } catch (e) {
            console.log('Storage clearing failed:', e);
        }

        // Prevent back button after logout
        window.history.pushState(null, null, window.location.href);
        window.onpopstate = function() {
            window.history.pushState(null, null, window.location.href);
        };

        // Auto-redirect to login after 30 seconds
        let countdown = 30;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'mt-3 text-muted small';
        countdownElement.innerHTML = `<i class="fas fa-clock me-1"></i>Redirecting to login in <span id="countdown">${countdown}</span> seconds`;
        document.querySelector('.logout-body').appendChild(countdownElement);

        const countdownTimer = setInterval(function() {
            countdown--;
            document.getElementById('countdown').textContent = countdown;

            if (countdown <= 0) {
                clearInterval(countdownTimer);
                window.location.href = 'login.php';
            }
        }, 1000);

        // Allow user to cancel auto-redirect
        document.addEventListener('click', function() {
            clearInterval(countdownTimer);
            countdownElement.innerHTML = '<i class="fas fa-info-circle me-1"></i>Auto-redirect cancelled';
        });

        document.addEventListener('keydown', function() {
            clearInterval(countdownTimer);
            countdownElement.innerHTML = '<i class="fas fa-info-circle me-1"></i>Auto-redirect cancelled';
        });
    </script>
</body>
</html>
