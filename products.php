<?php
/**
 * Products Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$product_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'delete' && isset($_POST['product_id'])) {
            // Soft delete product
            $db->query("UPDATE products SET is_active = 0 WHERE id = ?", [$_POST['product_id']]);
            $success = "Product deleted successfully!";
            $action = 'list';
        } elseif ($action === 'bulk_delete' && isset($_POST['selected_products'])) {
            // Bulk delete products
            $productIds = $_POST['selected_products'];
            $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
            $db->query("UPDATE products SET is_active = 0 WHERE id IN ($placeholders)", $productIds);
            $success = count($productIds) . " products deleted successfully!";
            $action = 'list';
        } elseif ($action === 'add' || $action === 'edit') {
            $data = [
                'product_name' => sanitizeInput($_POST['product_name']),
                'product_code' => sanitizeInput($_POST['product_code']),
                'description' => sanitizeInput($_POST['description']),
                'category_id' => $_POST['category_id'] ?: null,
                'supplier_id' => $_POST['supplier_id'] ?: null,
                'metal_type' => sanitizeInput($_POST['metal_type']),
                'purity' => sanitizeInput($_POST['purity']),
                'base_weight' => floatval($_POST['base_weight']),
                'stone_weight' => floatval($_POST['stone_weight']),
                'net_weight' => floatval($_POST['net_weight']),
                'stone_cost' => floatval($_POST['stone_cost']),
                'making_charges' => floatval($_POST['making_charges']),
                'hsn_code' => sanitizeInput($_POST['hsn_code']),
                'tax_rate' => floatval($_POST['tax_rate'])
            ];
            
            if ($action === 'add') {
                $sql = "INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, hsn_code, tax_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $product_id = $db->lastInsertId();

                // Create inventory record
                $db->query("INSERT INTO inventory (product_id, quantity_in_stock, minimum_stock_level) VALUES (?, 0, 5)", [$product_id]);

                $success = "Product added successfully!";
            } else {
                $sql = "UPDATE products SET product_name=?, product_code=?, description=?, category_id=?, supplier_id=?, metal_type=?, purity=?, base_weight=?, stone_weight=?, net_weight=?, stone_cost=?, making_charges=?, hsn_code=?, tax_rate=? WHERE id=?";
                $params = array_values($data);
                $params[] = $product_id;
                $db->query($sql, $params);

                $success = "Product updated successfully!";
            }
            
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get categories and suppliers for dropdown
$categories = $db->fetchAll("SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name");
$suppliers = $db->fetchAll("SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name");

// Get products list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    
    $where_conditions = ["p.is_active = 1"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(p.product_name LIKE ? OR p.product_code LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($category_filter) {
        $where_conditions[] = "p.category_id = ?";
        $params[] = $category_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT p.*, c.category_name, s.supplier_name, i.quantity_in_stock, i.minimum_stock_level
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE $where_clause
        ORDER BY p.created_at DESC
    ";
    
    $products = $db->fetchAll($sql, $params);
}

// Get single product for edit
if ($action === 'edit' && $product_id) {
    $product = $db->fetch("SELECT * FROM products WHERE id = ?", [$product_id]);
    if (!$product) {
        $error = "Product not found!";
        $action = 'list';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Products List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Products</h2>
                    <p class="text-muted mb-0">Manage your jewellery inventory</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportProducts()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Product
                    </a>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="card mb-3" id="bulkActions" style="display: none;">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="selectedCount">0 products selected</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i>Delete Selected
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>Clear Selection
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by name or code...">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" <?php echo $category_filter == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="products.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Supplier</th>
                                    <th>Metal/Purity</th>
                                    <th>Weight (gm)</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($products)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            <i class="fas fa-gem fa-3x mb-3 opacity-25"></i>
                                            <p>No products found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="product-checkbox" value="<?php echo $product['id']; ?>" onchange="updateBulkActions()">
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($product['supplier_name'] ?? 'No Supplier'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($product['metal_type']): ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <?php echo htmlspecialchars($product['metal_type'] . ' ' . $product['purity']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($product['net_weight'] > 0): ?>
                                                    <?php echo formatWeight($product['net_weight']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $stock = $product['quantity_in_stock'] ?? 0;
                                                $min_stock = $product['minimum_stock_level'] ?? 0;
                                                $stock_class = $stock <= $min_stock && $min_stock > 0 ? 'bg-danger' : 'bg-success';
                                                ?>
                                                <span class="badge <?php echo $stock_class; ?>">
                                                    <?php echo $stock; ?> units
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=edit&id=<?php echo $product['id']; ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-info" title="View Details"
                                                            onclick="viewProduct(<?php echo $product['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="Delete"
                                                            onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['product_name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Product Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Product' : 'Edit Product'; ?></h2>
                    <p class="text-muted mb-0">Enter product details below</p>
                </div>
                <a href="products.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Products
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Product Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="product_name" class="form-label">Product Name *</label>
                                        <input type="text" class="form-control" id="product_name" name="product_name" 
                                               value="<?php echo htmlspecialchars($product['product_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="product_code" class="form-label">Product Code *</label>
                                        <input type="text" class="form-control" id="product_code" name="product_code" 
                                               value="<?php echo htmlspecialchars($product['product_code'] ?? ''); ?>" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="category_id" class="form-label">Category</label>
                                        <select class="form-select" id="category_id" name="category_id">
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $cat): ?>
                                                <option value="<?php echo $cat['id']; ?>"
                                                        <?php echo ($product['category_id'] ?? '') == $cat['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($cat['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="supplier_id" class="form-label">Supplier</label>
                                        <select class="form-select" id="supplier_id" name="supplier_id">
                                            <option value="">Select Supplier</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <option value="<?php echo $supplier['id']; ?>"
                                                        <?php echo ($product['supplier_id'] ?? '') == $supplier['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($supplier['supplier_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="metal_type" class="form-label">Metal Type</label>
                                        <select class="form-select" id="metal_type" name="metal_type">
                                            <option value="">Select Metal</option>
                                            <option value="Gold" <?php echo ($product['metal_type'] ?? '') === 'Gold' ? 'selected' : ''; ?>>Gold</option>
                                            <option value="Silver" <?php echo ($product['metal_type'] ?? '') === 'Silver' ? 'selected' : ''; ?>>Silver</option>
                                            <option value="Platinum" <?php echo ($product['metal_type'] ?? '') === 'Platinum' ? 'selected' : ''; ?>>Platinum</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="purity" class="form-label">Purity</label>
                                        <select class="form-select" id="purity" name="purity">
                                            <option value="">Select Purity</option>
                                            <option value="24K" <?php echo ($product['purity'] ?? '') === '24K' ? 'selected' : ''; ?>>24K</option>
                                            <option value="22K" <?php echo ($product['purity'] ?? '') === '22K' ? 'selected' : ''; ?>>22K</option>
                                            <option value="18K" <?php echo ($product['purity'] ?? '') === '18K' ? 'selected' : ''; ?>>18K</option>
                                            <option value="999" <?php echo ($product['purity'] ?? '') === '999' ? 'selected' : ''; ?>>999</option>
                                            <option value="925" <?php echo ($product['purity'] ?? '') === '925' ? 'selected' : ''; ?>>925</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="base_weight" class="form-label">Base Weight (gm)</label>
                                        <input type="number" class="form-control" id="base_weight" name="base_weight" 
                                               step="0.001" value="<?php echo $product['base_weight'] ?? '0'; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="stone_weight" class="form-label">Stone Weight (gm)</label>
                                        <input type="number" class="form-control" id="stone_weight" name="stone_weight" 
                                               step="0.001" value="<?php echo $product['stone_weight'] ?? '0'; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="net_weight" class="form-label">Net Weight (gm)</label>
                                        <input type="number" class="form-control" id="net_weight" name="net_weight" 
                                               step="0.001" value="<?php echo $product['net_weight'] ?? '0'; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="stone_cost" class="form-label">Stone Cost (₹)</label>
                                        <input type="number" class="form-control" id="stone_cost" name="stone_cost" 
                                               step="0.01" value="<?php echo $product['stone_cost'] ?? '0'; ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="making_charges" class="form-label">Making Charges (₹)</label>
                                        <input type="number" class="form-control" id="making_charges" name="making_charges" 
                                               step="0.01" value="<?php echo $product['making_charges'] ?? '0'; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="hsn_code" class="form-label">HSN Code</label>
                                        <input type="text" class="form-control" id="hsn_code" name="hsn_code" 
                                               value="<?php echo htmlspecialchars($product['hsn_code'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                               step="0.01" value="<?php echo $product['tax_rate'] ?? '3.00'; ?>">
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="products.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action === 'add' ? 'Add Product' : 'Update Product'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Tips</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Product Code</h6>
                                <p class="mb-0 small">Use a unique code for each product. Example: GC001, SB002, etc.</p>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-balance-scale me-2"></i>Weight Calculation</h6>
                                <p class="mb-0 small">Net Weight = Base Weight + Stone Weight</p>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-tags me-2"></i>HSN Code</h6>
                                <p class="mb-0 small">Use 71131900 for gold jewellery, 71131100 for silver jewellery.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Auto-calculate net weight
        document.addEventListener('DOMContentLoaded', function() {
            const baseWeight = document.getElementById('base_weight');
            const stoneWeight = document.getElementById('stone_weight');
            const netWeight = document.getElementById('net_weight');

            function calculateNetWeight() {
                const base = parseFloat(baseWeight.value) || 0;
                const stone = parseFloat(stoneWeight.value) || 0;
                netWeight.value = (base + stone).toFixed(3);
            }

            baseWeight?.addEventListener('input', calculateNetWeight);
            stoneWeight?.addEventListener('input', calculateNetWeight);
        });

        // Bulk actions functionality
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.product-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');

            if (checkboxes.length > 0) {
                bulkActions.style.display = 'block';
                selectedCount.textContent = checkboxes.length + ' products selected';
            } else {
                bulkActions.style.display = 'none';
            }
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const selectAll = document.getElementById('selectAll');

            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAll.checked = false;

            updateBulkActions();
        }

        function bulkDelete() {
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');

            if (checkboxes.length === 0) {
                alert('Please select products to delete');
                return;
            }

            if (confirm(`Are you sure you want to delete ${checkboxes.length} selected products?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=bulk_delete';

                checkboxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'selected_products[]';
                    input.value = checkbox.value;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteProduct(id, name) {
            if (confirm(`Are you sure you want to delete "${name}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=delete';

                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'product_id';
                input.value = id;
                form.appendChild(input);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewProduct(id) {
            // Open product details in a modal or new page
            window.open(`product-details.php?id=${id}`, '_blank', 'width=800,height=600');
        }

        function exportProducts() {
            window.open('export-products.php', '_blank');
        }
    </script>
</body>
</html>
