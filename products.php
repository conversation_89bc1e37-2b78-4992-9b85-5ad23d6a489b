<?php
/**
 * Products Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';
require_once 'lib/ProductCodeGenerator.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$product_id = $_GET['id'] ?? null;

// Handle success message from redirect
if (isset($_GET['success'])) {
    $success = $_GET['success'];
}

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'delete' && isset($_POST['product_id'])) {
            // Soft delete product
            $db->query("UPDATE products SET is_active = 0 WHERE id = ?", [$_POST['product_id']]);
            $success = "Product deleted successfully!";
            $action = 'list';
        } elseif ($action === 'bulk_delete' && isset($_POST['selected_products'])) {
            // Bulk delete products
            $productIds = $_POST['selected_products'];
            $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
            $db->query("UPDATE products SET is_active = 0 WHERE id IN ($placeholders)", $productIds);
            $success = count($productIds) . " products deleted successfully!";
            $action = 'list';
        } elseif ($action === 'add' || $action === 'edit') {
            // Auto-generate product code for new products
            $product_code = '';
            if ($action === 'add') {
                $codeGenerator = new ProductCodeGenerator($db);
                $product_code = $codeGenerator->generateProductCode(
                    $_POST['product_name'],
                    $_POST['category_id'],
                    $_POST['metal_type']
                );
            } else {
                $product_code = sanitizeInput($_POST['product_code']);
            }

            $data = [
                'product_name' => sanitizeInput($_POST['product_name']),
                'product_code' => $product_code,
                'description' => sanitizeInput($_POST['description']),
                'category_id' => $_POST['category_id'] ?: null,
                'supplier_id' => $_POST['supplier_id'] ?: null,
                'metal_type' => sanitizeInput($_POST['metal_type']),
                'tunch_percentage' => floatval($_POST['tunch_percentage'] ?? 0),
                'gross_weight' => floatval($_POST['gross_weight'] ?? 0),
                'stone_weight' => floatval($_POST['stone_weight'] ?? 0),
                'net_weight' => floatval($_POST['gross_weight'] ?? 0) - floatval($_POST['stone_weight'] ?? 0),
                'making_charges_per_gram' => floatval($_POST['making_charges_per_gram'] ?? 0),
                'wastage_percentage' => floatval($_POST['wastage_percentage'] ?? 0),
                'hallmark_charges' => floatval($_POST['hallmark_charges'] ?? 0),
                'va_percentage' => floatval($_POST['va_percentage'] ?? 0),
                'base_weight' => floatval($_POST['base_weight']),
                'stone_cost' => floatval($_POST['stone_cost']),
                'making_charges' => floatval($_POST['making_charges']),
                'hsn_code' => sanitizeInput($_POST['hsn_code']),
                'tax_rate' => floatval($_POST['tax_rate']),
                'scheme_applicable' => isset($_POST['scheme_applicable']) ? 1 : 0
            ];

            if ($action === 'add') {
                $sql = "INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, tunch_percentage, gross_weight, stone_weight, net_weight, making_charges_per_gram, wastage_percentage, hallmark_charges, va_percentage, base_weight, stone_cost, making_charges, hsn_code, tax_rate, scheme_applicable) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $product_id = $db->lastInsertId();

                // Create inventory record
                $db->query("INSERT INTO inventory (product_id, quantity_in_stock, minimum_stock_level) VALUES (?, 0, 5)", [$product_id]);

                $success = "🎉 Product added successfully! Product Code: <strong>" . $product_code . "</strong> | Product ID: <strong>" . $product_id . "</strong>";

                // Redirect to prevent form resubmission
                header("Location: products.php?success=" . urlencode($success));
                exit;
            } else {
                $sql = "UPDATE products SET product_name=?, product_code=?, description=?, category_id=?, supplier_id=?, metal_type=?, tunch_percentage=?, gross_weight=?, stone_weight=?, net_weight=?, making_charges_per_gram=?, wastage_percentage=?, hallmark_charges=?, va_percentage=?, base_weight=?, stone_cost=?, making_charges=?, hsn_code=?, tax_rate=?, scheme_applicable=? WHERE id=?";
                $params = array_values($data);
                $params[] = $product_id;
                $db->query($sql, $params);

                $success = "✅ Product updated successfully!";

                // Redirect to prevent form resubmission
                header("Location: products.php?success=" . urlencode($success));
                exit;
            }
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get categories and suppliers for dropdown
$categories = $db->fetchAll("SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name");
$suppliers = $db->fetchAll("SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name");

// Get products list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    
    $where_conditions = ["p.is_active = 1"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(p.product_name LIKE ? OR p.product_code LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($category_filter) {
        $where_conditions[] = "p.category_id = ?";
        $params[] = $category_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT p.*, c.category_name, s.supplier_name, i.quantity_in_stock, i.minimum_stock_level
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE $where_clause
        ORDER BY p.created_at DESC
    ";
    
    $products = $db->fetchAll($sql, $params);
}

// Get single product for edit
if ($action === 'edit' && $product_id) {
    $product = $db->fetch("SELECT * FROM products WHERE id = ?", [$product_id]);
    if (!$product) {
        $error = "Product not found!";
        $action = 'list';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Products List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Products</h2>
                    <p class="text-muted mb-0">Manage your jewellery inventory</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportProducts()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Product
                    </a>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="card mb-3" id="bulkActions" style="display: none;">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="selectedCount">0 products selected</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i>Delete Selected
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>Clear Selection
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by name or code...">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" <?php echo $category_filter == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="products.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Supplier</th>
                                    <th>Metal/Purity</th>
                                    <th>Weight (gm)</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($products)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            <i class="fas fa-gem fa-3x mb-3 opacity-25"></i>
                                            <p>No products found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="product-checkbox" value="<?php echo $product['id']; ?>" onchange="updateBulkActions()">
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($product['supplier_name'] ?? 'No Supplier'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($product['metal_type']): ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <?php echo htmlspecialchars($product['metal_type'] . ' ' . $product['purity']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($product['net_weight'] > 0): ?>
                                                    <?php echo formatWeight($product['net_weight']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $stock = $product['quantity_in_stock'] ?? 0;
                                                $min_stock = $product['minimum_stock_level'] ?? 0;
                                                $stock_class = $stock <= $min_stock && $min_stock > 0 ? 'bg-danger' : 'bg-success';
                                                ?>
                                                <span class="badge <?php echo $stock_class; ?>">
                                                    <?php echo $stock; ?> units
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=edit&id=<?php echo $product['id']; ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-info" title="View Details"
                                                            onclick="viewProduct(<?php echo $product['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="Delete"
                                                            onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['product_name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Product Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Product' : 'Edit Product'; ?></h2>
                    <p class="text-muted mb-0">Enter product details below</p>
                </div>
                <a href="products.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Products
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Product Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="product_name" class="form-label">Product Name *</label>
                                        <input type="text" class="form-control" id="product_name" name="product_name" 
                                               value="<?php echo htmlspecialchars($product['product_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="product_code" class="form-label">Product Code</label>
                                        <?php if ($action === 'add'): ?>
                                            <input type="text" class="form-control" id="product_code" name="product_code"
                                                   value="Auto-generated" readonly
                                                   style="background-color: #e9ecef; color: #6c757d;">
                                            <small class="text-muted">Code will be generated automatically based on product name and metal type</small>
                                        <?php else: ?>
                                            <input type="text" class="form-control" id="product_code" name="product_code"
                                                   value="<?php echo htmlspecialchars($product['product_code'] ?? ''); ?>" readonly
                                                   style="background-color: #e9ecef; color: #6c757d;">
                                            <small class="text-muted">Product code cannot be changed after creation</small>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="category_id" class="form-label">Category</label>
                                        <select class="form-select" id="category_id" name="category_id">
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $cat): ?>
                                                <option value="<?php echo $cat['id']; ?>"
                                                        <?php echo ($product['category_id'] ?? '') == $cat['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($cat['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="supplier_id" class="form-label">Supplier</label>
                                        <select class="form-select" id="supplier_id" name="supplier_id">
                                            <option value="">Select Supplier</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <option value="<?php echo $supplier['id']; ?>"
                                                        <?php echo ($product['supplier_id'] ?? '') == $supplier['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($supplier['supplier_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="metal_type" class="form-label">Metal Type</label>
                                        <select class="form-select" id="metal_type" name="metal_type">
                                            <option value="">Select Metal</option>
                                            <option value="Gold" <?php echo ($product['metal_type'] ?? '') === 'Gold' ? 'selected' : ''; ?>>Gold</option>
                                            <option value="Silver" <?php echo ($product['metal_type'] ?? '') === 'Silver' ? 'selected' : ''; ?>>Silver</option>
                                            <option value="Platinum" <?php echo ($product['metal_type'] ?? '') === 'Platinum' ? 'selected' : ''; ?>>Platinum</option>
                                        </select>
                                    </div>
                                </div>



                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="gross_weight" class="form-label">Gross Weight (gm) *</label>
                                        <input type="number" class="form-control" id="gross_weight" name="gross_weight"
                                               step="0.001" value="<?php echo $product['gross_weight'] ?? '0'; ?>" required>
                                        <small class="text-muted">Total weight including stones</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="stone_weight" class="form-label">Stone Weight (gm)</label>
                                        <input type="number" class="form-control" id="stone_weight" name="stone_weight"
                                               step="0.001" value="<?php echo $product['stone_weight'] ?? '0'; ?>">
                                        <small class="text-muted">Weight of stones/gems only</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="net_weight" class="form-label">Net Weight (gm)</label>
                                        <input type="number" class="form-control" id="net_weight" name="net_weight"
                                               step="0.001" value="<?php echo $product['net_weight'] ?? '0'; ?>" readonly
                                               style="background-color: #e9ecef;">
                                        <small class="text-muted">Auto-calculated (Gross - Stone)</small>
                                    </div>
                                </div>

                                <!-- Tunch/Purity Field -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="tunch_percentage" class="form-label">Tunch/Purity (%) *</label>
                                        <input type="number" class="form-control" id="tunch_percentage" name="tunch_percentage"
                                               step="0.01" value="<?php echo $product['tunch_percentage'] ?? '91.6'; ?>" required>
                                        <small class="text-muted">Enter purity percentage (e.g., 91.6 for 22K gold, 92.5 for 925 silver, 75.0 for 18K gold)</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-info mb-0" style="padding: 10px;">
                                            <small><strong>Common Purities:</strong><br>
                                            22K Gold: 91.6% | 18K Gold: 75.0%<br>
                                            925 Silver: 92.5% | 999 Silver: 99.9%</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Indian Jewellery Specific Fields -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="making_charges_per_gram" class="form-label">Making Charges (₹/gm)</label>
                                        <input type="number" class="form-control" id="making_charges_per_gram" name="making_charges_per_gram"
                                               step="0.01" value="<?php echo $product['making_charges_per_gram'] ?? '0'; ?>">
                                        <small class="text-muted">Making charges per gram</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="wastage_percentage" class="form-label">Wastage (%)</label>
                                        <input type="number" class="form-control" id="wastage_percentage" name="wastage_percentage"
                                               step="0.1" value="<?php echo $product['wastage_percentage'] ?? '2.5'; ?>">
                                        <small class="text-muted">Wastage percentage</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="hallmark_charges" class="form-label">Hallmark Charges (₹)</label>
                                        <input type="number" class="form-control" id="hallmark_charges" name="hallmark_charges"
                                               step="0.01" value="<?php echo $product['hallmark_charges'] ?? '0'; ?>">
                                        <small class="text-muted">Hallmark certification charges</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="va_percentage" class="form-label">VA Percentage (%)</label>
                                        <input type="number" class="form-control" id="va_percentage" name="va_percentage"
                                               step="0.01" value="<?php echo $product['va_percentage'] ?? '0'; ?>">
                                        <small class="text-muted">Value Added percentage</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="base_weight" class="form-label">Base Weight (gm)</label>
                                        <input type="number" class="form-control" id="base_weight" name="base_weight"
                                               step="0.001" value="<?php echo $product['base_weight'] ?? '0'; ?>">
                                        <small class="text-muted">Legacy field (optional)</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="stone_cost" class="form-label">Stone Cost (₹)</label>
                                        <input type="number" class="form-control" id="stone_cost" name="stone_cost" 
                                               step="0.01" value="<?php echo $product['stone_cost'] ?? '0'; ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="making_charges" class="form-label">Making Charges (₹)</label>
                                        <input type="number" class="form-control" id="making_charges" name="making_charges" 
                                               step="0.01" value="<?php echo $product['making_charges'] ?? '0'; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="hsn_code" class="form-label">HSN Code</label>
                                        <input type="text" class="form-control" id="hsn_code" name="hsn_code"
                                               value="<?php echo htmlspecialchars($product['hsn_code'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate"
                                               step="0.01" value="<?php echo $product['tax_rate'] ?? '3.00'; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="scheme_applicable" name="scheme_applicable"
                                                   <?php echo ($product['scheme_applicable'] ?? 0) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="scheme_applicable">
                                                <strong>Scheme Applicable</strong>
                                            </label>
                                            <small class="text-muted d-block">Check if promotional schemes can be applied to this product</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="products.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <span id="submitText">
                                            <i class="fas fa-save me-2"></i>
                                            <?php echo $action === 'add' ? 'Add Product' : 'Update Product'; ?>
                                        </span>
                                        <span id="loadingText" style="display: none;">
                                            <i class="fas fa-spinner fa-spin me-2"></i>
                                            <?php echo $action === 'add' ? 'Creating Product...' : 'Updating Product...'; ?>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Tips</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-magic me-2"></i>Auto Product Code</h6>
                                <p class="mb-0 small">Product codes are generated automatically based on product name and metal type. Examples: GR001 (Gold Ring), SB002 (Silver Bracelet)</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-balance-scale me-2"></i>Weight Calculation</h6>
                                <p class="mb-0 small">Net Weight = Gross Weight - Stone Weight (auto-calculated)</p>
                            </div>

                            <div class="alert alert-primary">
                                <h6><i class="fas fa-gem me-2"></i>Tunch/Purity Percentage</h6>
                                <p class="mb-0 small">Enter the purity percentage directly: 22K Gold=91.6%, 18K Gold=75%, 925 Silver=92.5%, 999 Silver=99.9%</p>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-tags me-2"></i>HSN Code</h6>
                                <p class="mb-0 small">Use 71131900 for gold jewellery, 71131100 for silver jewellery.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Auto-calculate net weight and handle form submission
        document.addEventListener('DOMContentLoaded', function() {
            const grossWeight = document.getElementById('gross_weight');
            const stoneWeight = document.getElementById('stone_weight');
            const netWeight = document.getElementById('net_weight');
            const form = document.querySelector('form');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingText = document.getElementById('loadingText');

            function calculateNetWeight() {
                const gross = parseFloat(grossWeight.value) || 0;
                const stone = parseFloat(stoneWeight.value) || 0;
                const net = gross - stone;
                netWeight.value = net.toFixed(3);
            }

            // Handle form submission
            function handleFormSubmit(e) {
                // Show loading state
                if (submitBtn && submitText && loadingText) {
                    submitBtn.disabled = true;
                    submitText.style.display = 'none';
                    loadingText.style.display = 'inline';
                }

                // Let the form submit normally
                return true;
            }

            // Event listeners
            grossWeight?.addEventListener('input', calculateNetWeight);
            stoneWeight?.addEventListener('input', calculateNetWeight);
            form?.addEventListener('submit', handleFormSubmit);

            // Initial calculation
            calculateNetWeight();

            // Auto-hide success/error messages after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        alert.classList.add('fade');
                    }
                }, 5000);
            });
        });

        // Bulk actions functionality
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.product-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');

            if (checkboxes.length > 0) {
                bulkActions.style.display = 'block';
                selectedCount.textContent = checkboxes.length + ' products selected';
            } else {
                bulkActions.style.display = 'none';
            }
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const selectAll = document.getElementById('selectAll');

            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAll.checked = false;

            updateBulkActions();
        }

        function bulkDelete() {
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');

            if (checkboxes.length === 0) {
                alert('Please select products to delete');
                return;
            }

            if (confirm(`Are you sure you want to delete ${checkboxes.length} selected products?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=bulk_delete';

                checkboxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'selected_products[]';
                    input.value = checkbox.value;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteProduct(id, name) {
            if (confirm(`Are you sure you want to delete "${name}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=delete';

                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'product_id';
                input.value = id;
                form.appendChild(input);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewProduct(id) {
            // Open product details in a modal or new page
            window.open(`product-details.php?id=${id}`, '_blank', 'width=800,height=600');
        }

        function exportProducts() {
            window.open('export-products.php', '_blank');
        }
    </script>
</body>
</html>
