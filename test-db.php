<?php
/**
 * Database Connection Test
 */

require_once 'config/database.php';

try {
    echo "<h2>🔧 Database Connection Test</h2>";
    
    $db = getDB();
    echo "<p>✅ Database connection successful!</p>";
    
    // Test basic queries
    $tables = $db->fetchAll("SHOW TABLES");
    echo "<p>📊 Found " . count($tables) . " tables:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "<li>$tableName</li>";
    }
    echo "</ul>";
    
    // Test data
    $userCount = $db->fetch("SELECT COUNT(*) as count FROM users")['count'];
    $categoryCount = $db->fetch("SELECT COUNT(*) as count FROM categories")['count'];
    $settingsCount = $db->fetch("SELECT COUNT(*) as count FROM settings")['count'];
    
    echo "<h3>📝 Data Summary:</h3>";
    echo "<ul>";
    echo "<li>👤 Users: $userCount</li>";
    echo "<li>📂 Categories: $categoryCount</li>";
    echo "<li>⚙️ Settings: $settingsCount</li>";
    echo "</ul>";
    
    echo "<p>🎉 <strong>All tests passed! The system is ready to use.</strong></p>";
    echo "<p><a href='index.php'>Go to Dashboard</a> | <a href='login.php'>Login Page</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
