<?php
/**
 * Add Sample Data for Testing Reports
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>Adding Sample Data</h2>";
    
    // Check if data already exists
    $existing_categories = $db->fetch("SELECT COUNT(*) as count FROM categories");
    $existing_products = $db->fetch("SELECT COUNT(*) as count FROM products");
    
    if ($existing_categories['count'] > 0 && $existing_products['count'] > 0) {
        echo "✅ Sample data already exists!<br>";
        echo "Categories: {$existing_categories['count']}<br>";
        echo "Products: {$existing_products['count']}<br>";
    } else {
        echo "📝 Adding sample data...<br><br>";
        
        // Add sample categories
        echo "Adding categories...<br>";
        $categories = [
            ['Gold Rings', 'Traditional and modern gold rings'],
            ['Gold Necklaces', 'Beautiful gold necklace sets'],
            ['Gold Earrings', 'Elegant gold earring designs'],
            ['Silver Jewelry', 'Pure silver jewelry items']
        ];
        
        foreach ($categories as $cat) {
            $db->execute("INSERT INTO categories (category_name, description, is_active) VALUES (?, ?, 1)", $cat);
            echo "- Added category: {$cat[0]}<br>";
        }
        
        // Add sample suppliers
        echo "<br>Adding suppliers...<br>";
        $suppliers = [
            ['ABC Gold Suppliers', 'Mumbai', '9876543210'],
            ['XYZ Silver Works', 'Delhi', '9876543211']
        ];
        
        foreach ($suppliers as $sup) {
            $db->execute("INSERT INTO suppliers (supplier_name, city, phone, is_active) VALUES (?, ?, ?, 1)", $sup);
            echo "- Added supplier: {$sup[0]}<br>";
        }
        
        // Add sample products
        echo "<br>Adding products...<br>";
        $products = [
            [1, 1, 'Gold Ring - Classic', 'GR001', 'Classic gold ring design', 'Gold', '22K', 5.5],
            [1, 1, 'Gold Ring - Modern', 'GR002', 'Modern gold ring design', 'Gold', '18K', 4.2],
            [2, 1, 'Gold Necklace Set', 'GN001', 'Traditional necklace set', 'Gold', '22K', 25.8],
            [3, 1, 'Gold Earrings', 'GE001', 'Beautiful gold earrings', 'Gold', '18K', 8.5],
            [4, 2, 'Silver Bracelet', 'SB001', 'Pure silver bracelet', 'Silver', '925', 15.2]
        ];
        
        foreach ($products as $prod) {
            $db->execute("
                INSERT INTO products (category_id, supplier_id, product_name, product_code, description, metal_type, purity, net_weight, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            ", $prod);
            echo "- Added product: {$prod[2]}<br>";
        }
        
        // Add inventory data
        echo "<br>Adding inventory data...<br>";
        $inventory_data = [
            [1, 10, 15000, 18000, 20000, 5],  // Gold Ring Classic
            [2, 8, 12000, 15000, 17000, 3],   // Gold Ring Modern  
            [3, 5, 45000, 55000, 60000, 2],   // Gold Necklace Set
            [4, 12, 18000, 22000, 25000, 4],  // Gold Earrings
            [5, 20, 3000, 4000, 4500, 10]     // Silver Bracelet
        ];
        
        foreach ($inventory_data as $inv) {
            $db->execute("
                INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, mrp, minimum_stock_level) 
                VALUES (?, ?, ?, ?, ?, ?)
            ", $inv);
            echo "- Added inventory for product ID: {$inv[0]}<br>";
        }
        
        // Add sample customers
        echo "<br>Adding customers...<br>";
        $customers = [
            ['Rajesh Kumar', 'Kumar Jewellers', '9876543210', '<EMAIL>'],
            ['Priya Sharma', 'Sharma Gold House', '9876543211', '<EMAIL>'],
            ['Amit Patel', 'Patel Jewellery', '9876543212', '<EMAIL>']
        ];
        
        foreach ($customers as $cust) {
            $db->execute("
                INSERT INTO customers (customer_name, business_name, phone, email, is_active) 
                VALUES (?, ?, ?, ?, 1)
            ", $cust);
            echo "- Added customer: {$cust[0]}<br>";
        }
        
        // Add sample sales
        echo "<br>Adding sample sales...<br>";
        $sales_data = [
            ['BILL001', 1, '2025-01-15', 1, 18000, 18000, 0],
            ['BILL002', 2, '2025-01-16', 2, 30000, 25000, 5000],
            ['BILL003', 1, '2025-01-17', 1, 22000, 22000, 0]
        ];
        
        foreach ($sales_data as $sale) {
            $db->execute("
                INSERT INTO sales (bill_number, customer_id, sale_date, total_items, grand_total, paid_amount, balance_amount, payment_status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ", array_merge($sale, [$sale[6] > 0 ? 'partial' : 'paid']));
            echo "- Added sale: {$sale[0]}<br>";
        }
        
        echo "<br>✅ Sample data added successfully!<br>";
    }
    
    // Show final counts
    echo "<br><h3>Current Data Summary</h3>";
    $tables = ['categories', 'suppliers', 'products', 'inventory', 'customers', 'sales'];
    foreach ($tables as $table) {
        $count = $db->fetch("SELECT COUNT(*) as count FROM $table");
        echo "- {$table}: {$count['count']} records<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
