/**
 * Indian Jewellery Wholesale Management System v2.0
 * Modern CSS Framework with Sidebar Layout
 */

:root {
    /* Layout Variables */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 60px;
    
    /* Color Palette */
    --primary: #667eea;
    --primary-dark: #5a67d8;
    --secondary: #764ba2;
    --success: #48bb78;
    --warning: #ed8936;
    --danger: #f56565;
    --info: #4299e1;
    --light: #f7fafc;
    --dark: #2d3748;
    --white: #ffffff;
    
    /* Sidebar Colors */
    --sidebar-bg: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    --sidebar-text: rgba(255, 255, 255, 0.9);
    --sidebar-text-hover: #ffffff;
    --sidebar-active: rgba(255, 255, 255, 0.15);
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    background-color: var(--light);
    color: var(--dark);
    overflow-x: hidden;
}

/* Layout Structure */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    z-index: 1000;
    transition: all var(--transition-base);
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: var(--header-height);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--white);
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: opacity var(--transition-base);
}

.sidebar-brand i {
    font-size: var(--font-size-xl);
    margin-right: var(--spacing-3);
}

.sidebar.collapsed .sidebar-brand .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius);
    transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: var(--spacing-4) 0;
}

.nav-section {
    margin-bottom: var(--spacing-6);
}

.nav-section-title {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(255, 255, 255, 0.6);
    padding: 0 var(--spacing-4) var(--spacing-2);
    transition: opacity var(--transition-base);
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin: var(--spacing-1) var(--spacing-3);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--sidebar-text);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    color: var(--sidebar-text-hover);
    background-color: var(--sidebar-hover);
    transform: translateX(2px);
}

.nav-link.active {
    color: var(--white);
    background-color: var(--sidebar-active);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background-color: var(--white);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-base);
    flex-shrink: 0;
}

.nav-text {
    transition: opacity var(--transition-base);
    white-space: nowrap;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Submenu Styles */
.nav-item.has-submenu .nav-link {
    justify-content: space-between;
}

.submenu-arrow {
    transition: transform var(--transition-fast);
    font-size: var(--font-size-sm);
}

.nav-item.has-submenu.open .submenu-arrow {
    transform: rotate(180deg);
}

.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-base);
    background-color: rgba(0, 0, 0, 0.1);
    margin: var(--spacing-1) 0;
    border-radius: var(--radius);
}

.nav-item.has-submenu.open .submenu {
    max-height: 300px;
}

.submenu .nav-link {
    padding-left: calc(var(--spacing-4) + 20px + var(--spacing-3));
    font-size: var(--font-size-sm);
    font-weight: 400;
}

.sidebar.collapsed .submenu {
    display: none;
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-base);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Header */
.top-header {
    background: var(--white);
    border-bottom: 1px solid #e2e8f0;
    padding: 0 var(--spacing-6);
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--dark);
    cursor: pointer;
    padding: var(--spacing-2);
    margin-right: var(--spacing-3);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--dark);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--spacing-6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .main-content.sidebar-collapsed {
        margin-left: 0;
    }
    
    .mobile-sidebar-toggle {
        display: block !important;
    }
}

@media (max-width: 768px) {
    .content-area {
        padding: var(--spacing-4);
    }
    
    .top-header {
        padding: 0 var(--spacing-4);
    }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-between { justify-content: space-between !important; }
.justify-content-center { justify-content: center !important; }
.align-items-center { align-items: center !important; }

.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }

.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }

.text-primary { color: var(--primary) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-info { color: var(--info) !important; }
.text-muted { color: #6b7280 !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-light { background-color: var(--light) !important; }
.bg-white { background-color: var(--white) !important; }

/* Component Styles */

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: none;
    margin-bottom: var(--spacing-6);
    transition: all var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.card-header {
    background: var(--white);
    border-bottom: 1px solid #e2e8f0;
    padding: var(--spacing-5) var(--spacing-6);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    font-weight: 600;
    color: var(--dark);
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-5);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    border-radius: var(--radius);
    border: 1px solid transparent;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background: var(--secondary);
    color: var(--white);
    border-color: var(--secondary);
}

.btn-success {
    background: var(--success);
    color: var(--white);
    border-color: var(--success);
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
    border-color: var(--warning);
}

.btn-danger {
    background: var(--danger);
    color: var(--white);
    border-color: var(--danger);
}

.btn-info {
    background: var(--info);
    color: var(--white);
    border-color: var(--info);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: var(--white);
}

.btn-outline-secondary {
    color: var(--secondary);
    border-color: #6c757d;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: var(--white);
}

.btn-outline-dark {
    color: var(--dark);
    border-color: var(--dark);
    background: transparent;
}

.btn-outline-dark:hover {
    background: var(--dark);
    color: var(--white);
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-lg);
}

/* Form Controls */
.form-control, .form-select {
    display: block;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark);
    background: var(--white);
    border: 1px solid #d1d5db;
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    display: inline-block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--dark);
}

.form-text {
    margin-top: var(--spacing-1);
    font-size: var(--font-size-sm);
    color: #6b7280;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 0;
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th,
.table td {
    padding: var(--spacing-4);
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
}

.table th {
    background: #f9fafb;
    font-weight: 600;
    color: var(--dark);
    text-transform: uppercase;
    font-size: var(--font-size-xs);
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background: #f9fafb;
}

.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-lg);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    color: var(--white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-xl);
}

.badge.bg-primary { background: var(--primary) !important; }
.badge.bg-secondary { background: var(--secondary) !important; }
.badge.bg-success { background: var(--success) !important; }
.badge.bg-warning { background: var(--warning) !important; }
.badge.bg-danger { background: var(--danger) !important; }
.badge.bg-info { background: var(--info) !important; }

/* Alerts */
.alert {
    position: relative;
    padding: var(--spacing-4) var(--spacing-5);
    margin-bottom: var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
}

.alert-primary {
    color: #1e40af;
    background: #dbeafe;
    border-color: #93c5fd;
}

.alert-success {
    color: #065f46;
    background: #d1fae5;
    border-color: #6ee7b7;
}

.alert-warning {
    color: #92400e;
    background: #fef3c7;
    border-color: #fcd34d;
}

.alert-danger {
    color: #991b1b;
    background: #fee2e2;
    border-color: #fca5a5;
}

.alert-info {
    color: #1e40af;
    background: #dbeafe;
    border-color: #93c5fd;
}

/* Dropdowns */
.dropdown-menu {
    background: var(--white);
    border: 1px solid #e5e7eb;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2) 0;
    min-width: 200px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--dark);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: #f3f4f6;
    color: var(--dark);
}

.dropdown-header {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dropdown-divider {
    height: 0;
    margin: var(--spacing-2) 0;
    overflow: hidden;
    border-top: 1px solid #e5e7eb;
}

/* Notification Dropdown */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown .dropdown-item {
    padding: var(--spacing-4);
    border-bottom: 1px solid #f3f4f6;
    white-space: normal;
    line-height: 1.4;
}

.notification-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary);
    transition: all var(--transition-base);
    cursor: pointer;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.dashboard-card.border-left-primary { border-left-color: var(--primary); }
.dashboard-card.border-left-success { border-left-color: var(--success); }
.dashboard-card.border-left-warning { border-left-color: var(--warning); }
.dashboard-card.border-left-danger { border-left-color: var(--danger); }
.dashboard-card.border-left-info { border-left-color: var(--info); }

.dashboard-card .card-title {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #6b7280;
    margin-bottom: var(--spacing-2);
}

.dashboard-card .card-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 0;
}

.dashboard-card .card-icon {
    font-size: var(--font-size-3xl);
    opacity: 0.3;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}
