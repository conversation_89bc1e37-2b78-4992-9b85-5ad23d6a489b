<?php
/**
 * Basic Seed Data Creation - Step by Step
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h1>🔧 Creating Basic Seed Data Step by Step</h1>";
    
    // Step 1: Create one supplier
    echo "<h2>🚚 Creating First Supplier...</h2>";
    
    $stmt = $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        'Emerald Jewel Industry',
        '<PERSON><PERSON>', 
        '**********', 
        '<EMAIL>', 
        '123 Jewel Street, Mumbai', 
        'Mumbai', 
        'Maharashtra', 
        '400001', 
        '27**********1Z5', 
        '**********', 
        'HDFC Bank', 
        '***********', 
        'HDFC0001234', 
        200000.00, 
        30,
        1
    ]);
    
    $supplierId = $db->lastInsertId();
    echo "<p>✅ Created supplier with ID: $supplierId</p>";
    
    // Step 2: Create one product
    echo "<h2>💎 Creating First Product...</h2>";
    
    $stmt = $db->query("INSERT INTO products (supplier_id, category_id, product_name, product_code, description, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $supplierId,
        1, // Chains category
        'Coimbatore Chain',
        'CC001',
        'Traditional Coimbatore style gold chain',
        'Gold',
        '22K',
        10.500,
        0.000,
        10.500,
        0,
        950,
        5800,
        '********',
        1
    ]);
    
    $productId = $db->lastInsertId();
    echo "<p>✅ Created product with ID: $productId</p>";
    
    // Step 3: Create inventory record
    echo "<h2>📦 Creating Inventory Record...</h2>";
    
    $stock = 25;
    $metal_value = 10.500 * 5800; // net_weight * gold_rate
    $total_cost = $metal_value + 0 + 950; // metal + stone + making
    $selling_price = $total_cost * 1.25; // 25% markup
    
    $stmt = $db->query("INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, minimum_stock_level, reorder_point, location, rack_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [
        $productId,
        $stock,
        $total_cost,
        $selling_price,
        5,
        10,
        'A-01',
        'R-001'
    ]);
    
    echo "<p>✅ Created inventory record - Stock: $stock, Cost: ₹" . number_format($total_cost, 2) . "</p>";
    
    // Step 4: Create one customer
    echo "<h2>👥 Creating First Customer...</h2>";
    
    $stmt = $db->query("INSERT INTO customers (customer_name, business_name, customer_type, phone, email, address, city, state, pincode, gst_number, pan_number, aadhar_number, credit_limit, credit_days, discount_percentage, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        'VG Jewellery Store',
        'VG Retail Chain',
        'business',
        '9876543220',
        '<EMAIL>',
        '123 Main Street, Chennai',
        'Chennai',
        'Tamil Nadu',
        '600001',
        '33**********7Z1',
        '**********',
        '',
        500000.00,
        45,
        5.0,
        1
    ]);
    
    $customerId = $db->lastInsertId();
    echo "<p>✅ Created customer with ID: $customerId</p>";
    
    // Step 5: Create a simple sale
    echo "<h2>🛒 Creating Sample Sale...</h2>";
    
    $billNumber = 'BILL-' . date('Ymd') . '-0001';
    $subtotal = 61850.00;
    $discountAmount = 1237.00; // 2%
    $taxAmount = 1855.50; // 3%
    $grandTotal = $subtotal - $discountAmount + $taxAmount;
    
    $stmt = $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $billNumber,
        $customerId,
        date('Y-m-d'),
        date('H:i:s'),
        $subtotal,
        'percentage',
        2.00,
        $discountAmount,
        $taxAmount,
        $grandTotal,
        'cash',
        'paid',
        1,
        0
    ]);
    
    $saleId = $db->lastInsertId();
    echo "<p>✅ Created sale with ID: $saleId</p>";
    
    // Step 6: Create sale item
    $stmt = $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $saleId,
        $productId,
        1,
        10.500,
        10.500,
        0,
        950,
        5800,
        61850,
        61850,
        3.00,
        1855.50,
        63705.50
    ]);
    
    echo "<p>✅ Created sale item</p>";
    
    // Step 7: Update inventory and create stock movement
    $stmt = $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - 1 WHERE product_id = ?", [$productId]);
    
    $stmt = $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)", [
        $productId,
        'out',
        1,
        'sale',
        $saleId,
        'Sale transaction',
        1
    ]);
    
    echo "<p>✅ Updated inventory and created stock movement</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; color: #155724; margin: 30px 0;'>";
    echo "<h2>🎉 Basic Seed Data Created Successfully!</h2>";
    echo "<p>Created:</p>";
    echo "<ul>";
    echo "<li>✅ 1 Supplier (Emerald Jewel Industry)</li>";
    echo "<li>✅ 1 Product (Coimbatore Chain) with inventory</li>";
    echo "<li>✅ 1 Customer (VG Jewellery Store)</li>";
    echo "<li>✅ 1 Sample sale transaction</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Test Your System Now:</h3>";
    echo "<p><a href='index.php' target='_blank'>📊 Dashboard</a> | ";
    echo "<a href='products.php' target='_blank'>💎 Products</a> | ";
    echo "<a href='suppliers.php' target='_blank'>🚚 Suppliers</a> | ";
    echo "<a href='customers.php' target='_blank'>👥 Customers</a> | ";
    echo "<a href='sales.php' target='_blank'>📊 Sales</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Creating Seed Data</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
