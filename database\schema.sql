-- Indian Jewellery Wholesale Management System v2.0
-- Complete Database Schema

SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS `audit_logs`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `stock_movements`;
DROP TABLE IF EXISTS `sale_items`;
DROP TABLE IF EXISTS `sales`;
DROP TABLE IF EXISTS `inventory`;
DROP TABLE IF EXISTS `products`;
DROP TABLE IF EXISTS `categories`;
DROP TABLE IF EXISTS `customers`;
DROP TABLE IF EXISTS `suppliers`;
DROP TABLE IF EXISTS `metal_rates`;
DROP TABLE IF EXISTS `settings`;
DROP TABLE IF EXISTS `users`;

-- Users table for authentication
CREATE TABLE `users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `username` VARCHAR(50) UNIQUE NOT NULL,
    `email` VARCHAR(100) UNIQUE NOT NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `full_name` VARCHAR(100) NOT NULL,
    `role` ENUM('admin', 'manager', 'staff') DEFAULT 'staff',
    `is_active` BOOLEAN DEFAULT TRUE,
    `last_login` TIMESTAMP NULL,
    `login_attempts` INT DEFAULT 0,
    `locked_until` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Settings table for system configuration
CREATE TABLE `settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `setting_key` VARCHAR(100) UNIQUE NOT NULL,
    `setting_value` TEXT,
    `setting_type` ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    `category` VARCHAR(50) DEFAULT 'general',
    `description` TEXT,
    `is_public` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Metal rates table for daily rate management
CREATE TABLE `metal_rates` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `metal_type` VARCHAR(50) NOT NULL,
    `purity` VARCHAR(20) NOT NULL,
    `rate_per_gram` DECIMAL(10,2) NOT NULL,
    `rate_date` DATE NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_by` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_metal_date` (`metal_type`, `purity`, `rate_date`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
);

-- Suppliers table
CREATE TABLE `suppliers` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `supplier_name` VARCHAR(100) NOT NULL,
    `contact_person` VARCHAR(100),
    `phone` VARCHAR(15),
    `email` VARCHAR(100),
    `address` TEXT,
    `city` VARCHAR(50),
    `state` VARCHAR(50),
    `pincode` VARCHAR(10),
    `gst_number` VARCHAR(15),
    `pan_number` VARCHAR(10),
    `bank_name` VARCHAR(100),
    `account_number` VARCHAR(20),
    `ifsc_code` VARCHAR(11),
    `credit_limit` DECIMAL(12,2) DEFAULT 0,
    `credit_days` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table for product classification
CREATE TABLE `categories` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `category_name` VARCHAR(50) NOT NULL,
    `description` TEXT,
    `parent_id` INT NULL,
    `sort_order` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL
);

-- Products table
CREATE TABLE `products` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `supplier_id` INT,
    `category_id` INT,
    `product_name` VARCHAR(100) NOT NULL,
    `product_code` VARCHAR(50) UNIQUE,
    `description` TEXT,
    `metal_type` VARCHAR(50),
    `purity` VARCHAR(20),
    `base_weight` DECIMAL(8,3) DEFAULT 0,
    `stone_weight` DECIMAL(8,3) DEFAULT 0,
    `net_weight` DECIMAL(8,3) DEFAULT 0,
    `stone_cost` DECIMAL(10,2) DEFAULT 0,
    `making_charges` DECIMAL(10,2) DEFAULT 0,
    `gold_rate` DECIMAL(10,2) DEFAULT 0,
    `hsn_code` VARCHAR(20),
    `tax_rate` DECIMAL(5,2) DEFAULT 3.00,
    `image_path` VARCHAR(255),
    `barcode` VARCHAR(50),
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL,
    INDEX `idx_product_code` (`product_code`),
    INDEX `idx_metal_type` (`metal_type`, `purity`)
);

-- Inventory table
CREATE TABLE `inventory` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `product_id` INT NOT NULL,
    `quantity_in_stock` INT DEFAULT 0,
    `reserved_quantity` INT DEFAULT 0,
    `cost_price` DECIMAL(10,2) DEFAULT 0,
    `selling_price` DECIMAL(10,2) DEFAULT 0,
    `mrp` DECIMAL(10,2) DEFAULT 0,
    `minimum_stock_level` INT DEFAULT 0,
    `maximum_stock_level` INT DEFAULT 0,
    `reorder_point` INT DEFAULT 0,
    `location` VARCHAR(100),
    `rack_number` VARCHAR(50),
    `last_stock_update` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_product_inventory` (`product_id`)
);

-- Customers table
CREATE TABLE `customers` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `customer_name` VARCHAR(100) NOT NULL,
    `business_name` VARCHAR(100),
    `customer_type` ENUM('individual', 'business') DEFAULT 'business',
    `phone` VARCHAR(15),
    `email` VARCHAR(100),
    `address` TEXT,
    `city` VARCHAR(50),
    `state` VARCHAR(50),
    `pincode` VARCHAR(10),
    `gst_number` VARCHAR(15),
    `pan_number` VARCHAR(10),
    `aadhar_number` VARCHAR(12),
    `credit_limit` DECIMAL(12,2) DEFAULT 0,
    `credit_days` INT DEFAULT 0,
    `outstanding_balance` DECIMAL(12,2) DEFAULT 0,
    `loyalty_points` INT DEFAULT 0,
    `discount_percentage` DECIMAL(5,2) DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_customer_phone` (`phone`),
    INDEX `idx_customer_gst` (`gst_number`)
);

-- Sales table
CREATE TABLE `sales` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `bill_number` VARCHAR(50) UNIQUE NOT NULL,
    `customer_id` INT,
    `sale_date` DATE NOT NULL,
    `sale_time` TIME NOT NULL,
    `total_items` INT DEFAULT 0,
    `total_quantity` INT DEFAULT 0,
    `total_weight` DECIMAL(10,3) DEFAULT 0,
    `subtotal` DECIMAL(12,2) DEFAULT 0,
    `total_stone_cost` DECIMAL(12,2) DEFAULT 0,
    `total_making_charges` DECIMAL(12,2) DEFAULT 0,
    `total_gold_value` DECIMAL(12,2) DEFAULT 0,
    `discount_type` ENUM('percentage', 'amount') DEFAULT 'amount',
    `discount_value` DECIMAL(10,2) DEFAULT 0,
    `discount_amount` DECIMAL(12,2) DEFAULT 0,
    `tax_amount` DECIMAL(12,2) DEFAULT 0,
    `round_off` DECIMAL(5,2) DEFAULT 0,
    `grand_total` DECIMAL(12,2) NOT NULL,
    `paid_amount` DECIMAL(12,2) DEFAULT 0,
    `balance_amount` DECIMAL(12,2) DEFAULT 0,
    `payment_status` ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    `payment_method` ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'mixed') DEFAULT 'cash',
    `payment_reference` VARCHAR(100),
    `due_date` DATE,
    `sales_person` VARCHAR(100),
    `notes` TEXT,
    `is_cancelled` BOOLEAN DEFAULT FALSE,
    `cancelled_reason` TEXT,
    `cancelled_at` TIMESTAMP NULL,
    `created_by` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_bill_number` (`bill_number`),
    INDEX `idx_sale_date` (`sale_date`),
    INDEX `idx_payment_status` (`payment_status`)
);

-- Sale items table
CREATE TABLE `sale_items` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `sale_id` INT NOT NULL,
    `product_id` INT NOT NULL,
    `quantity` INT NOT NULL DEFAULT 1,
    `unit_weight` DECIMAL(8,3) DEFAULT 0,
    `total_weight` DECIMAL(8,3) DEFAULT 0,
    `stone_cost` DECIMAL(10,2) DEFAULT 0,
    `making_charges` DECIMAL(10,2) DEFAULT 0,
    `gold_rate` DECIMAL(10,2) DEFAULT 0,
    `unit_price` DECIMAL(10,2) NOT NULL,
    `total_price` DECIMAL(12,2) NOT NULL,
    `discount_percentage` DECIMAL(5,2) DEFAULT 0,
    `discount_amount` DECIMAL(10,2) DEFAULT 0,
    `tax_rate` DECIMAL(5,2) DEFAULT 0,
    `tax_amount` DECIMAL(10,2) DEFAULT 0,
    `line_total` DECIMAL(12,2) NOT NULL,
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT
);

-- Stock movements table for inventory tracking
CREATE TABLE `stock_movements` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `product_id` INT NOT NULL,
    `movement_type` ENUM('in', 'out', 'adjustment', 'transfer') NOT NULL,
    `quantity` INT NOT NULL,
    `reference_type` ENUM('purchase', 'sale', 'adjustment', 'return', 'transfer') NOT NULL,
    `reference_id` INT,
    `cost_price` DECIMAL(10,2) DEFAULT 0,
    `selling_price` DECIMAL(10,2) DEFAULT 0,
    `movement_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `notes` TEXT,
    `created_by` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_movement_date` (`movement_date`),
    INDEX `idx_reference` (`reference_type`, `reference_id`)
);

-- Notifications table
CREATE TABLE `notifications` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT,
    `title` VARCHAR(255) NOT NULL,
    `message` TEXT NOT NULL,
    `type` ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    `is_read` BOOLEAN DEFAULT FALSE,
    `action_url` VARCHAR(255),
    `expires_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_unread` (`user_id`, `is_read`)
);

-- Audit logs table for tracking changes
CREATE TABLE `audit_logs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT,
    `table_name` VARCHAR(50) NOT NULL,
    `record_id` INT NOT NULL,
    `action` ENUM('create', 'update', 'delete') NOT NULL,
    `old_values` JSON,
    `new_values` JSON,
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_table_record` (`table_name`, `record_id`),
    INDEX `idx_user_action` (`user_id`, `action`)
);

-- Insert default data
INSERT INTO `users` (`username`, `email`, `password_hash`, `full_name`, `role`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');

INSERT INTO `categories` (`category_name`, `description`, `sort_order`) VALUES
('Chains', 'Gold and silver chains', 1),
('Bangles', 'Traditional and modern bangles', 2),
('Earrings', 'Studs, hoops, and traditional earrings', 3),
('Rings', 'Wedding rings, engagement rings, and fashion rings', 4),
('Necklaces', 'Traditional necklace sets', 5),
('Pendants', 'Religious and fashion pendants', 6),
('Bracelets', 'Gold and silver bracelets', 7),
('Anklets', 'Traditional anklets', 8);

INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`) VALUES
('company_name', 'Indian Jewellery Wholesale', 'text', 'company', 'Company Name'),
('company_address', 'Zaveri Bazaar, Mumbai, Maharashtra 400002', 'text', 'company', 'Company Address'),
('company_phone', '+91 98765 43210', 'text', 'company', 'Company Phone'),
('company_email', '<EMAIL>', 'text', 'company', 'Company Email'),
('company_gst', '27ABCDE1234F1Z5', 'text', 'company', 'Company GST Number'),
('default_gold_rate', '5800', 'number', 'rates', 'Default Gold Rate per gram'),
('default_silver_rate', '85', 'number', 'rates', 'Default Silver Rate per gram'),
('default_making_charges', '500', 'number', 'rates', 'Default Making Charges per gram'),
('default_gst_rate', '3', 'number', 'tax', 'Default GST Rate (%)'),
('low_stock_threshold', '5', 'number', 'inventory', 'Low Stock Alert Threshold'),
('invoice_prefix', 'BILL-', 'text', 'billing', 'Invoice Number Prefix'),
('invoice_footer', 'Thank you for your business!', 'text', 'billing', 'Invoice Footer Text');

INSERT INTO `metal_rates` (`metal_type`, `purity`, `rate_per_gram`, `rate_date`) VALUES
('Gold', '24K', 6200.00, CURDATE()),
('Gold', '22K', 5800.00, CURDATE()),
('Gold', '18K', 4650.00, CURDATE()),
('Silver', '999', 85.00, CURDATE()),
('Silver', '925', 78.00, CURDATE()),
('Platinum', '950', 3200.00, CURDATE());

SET FOREIGN_KEY_CHECKS = 1;
