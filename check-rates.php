<?php
/**
 * Check Current Metal Rates in Database
 */

require_once 'config/database.php';

echo "<h1>📊 Current Metal Rates Check</h1>";

try {
    $db = getDB();
    
    // Get today's rates
    $today_rates = $db->fetchAll("
        SELECT metal_type, purity, rate_per_gram, rate_date, created_at, updated_at
        FROM metal_rates 
        WHERE rate_date = CURDATE() AND is_active = 1 
        ORDER BY metal_type, purity
    ");
    
    echo "<h2>📅 Today's Rates (" . date('Y-m-d') . ")</h2>";
    
    if (empty($today_rates)) {
        echo "<p style='color: red;'>❌ No rates found for today!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Metal Type</th>";
        echo "<th style='padding: 10px;'>Purity</th>";
        echo "<th style='padding: 10px;'>Rate per Gram</th>";
        echo "<th style='padding: 10px;'>Date</th>";
        echo "<th style='padding: 10px;'>Last Updated</th>";
        echo "</tr>";
        
        foreach ($today_rates as $rate) {
            echo "<tr>";
            echo "<td style='padding: 10px; font-weight: bold;'>{$rate['metal_type']}</td>";
            echo "<td style='padding: 10px;'>{$rate['purity']}</td>";
            echo "<td style='padding: 10px; color: #007bff; font-weight: bold;'>₹" . number_format($rate['rate_per_gram'], 2) . "</td>";
            echo "<td style='padding: 10px;'>{$rate['rate_date']}</td>";
            echo "<td style='padding: 10px;'>" . ($rate['updated_at'] ?: $rate['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 20px 0;'>";
        echo "<h3>✅ Rates Found!</h3>";
        echo "<p>Found " . count($today_rates) . " metal rates for today.</p>";
        echo "</div>";
    }
    
    // Get recent rate history
    echo "<h2>📈 Recent Rate History (Last 10 Updates)</h2>";
    $recent_rates = $db->fetchAll("
        SELECT metal_type, purity, rate_per_gram, rate_date, created_at, updated_at
        FROM metal_rates 
        WHERE is_active = 1 
        ORDER BY COALESCE(updated_at, created_at) DESC 
        LIMIT 10
    ");
    
    if (empty($recent_rates)) {
        echo "<p style='color: red;'>❌ No rate history found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Metal</th>";
        echo "<th style='padding: 10px;'>Purity</th>";
        echo "<th style='padding: 10px;'>Rate</th>";
        echo "<th style='padding: 10px;'>Date</th>";
        echo "<th style='padding: 10px;'>Last Modified</th>";
        echo "</tr>";
        
        foreach ($recent_rates as $rate) {
            $last_modified = $rate['updated_at'] ?: $rate['created_at'];
            echo "<tr>";
            echo "<td style='padding: 10px;'>{$rate['metal_type']}</td>";
            echo "<td style='padding: 10px;'>{$rate['purity']}</td>";
            echo "<td style='padding: 10px;'>₹" . number_format($rate['rate_per_gram'], 2) . "</td>";
            echo "<td style='padding: 10px;'>{$rate['rate_date']}</td>";
            echo "<td style='padding: 10px;'>{$last_modified}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test if updates are working
    echo "<h2>🔄 Test Rate Update</h2>";
    
    if (isset($_POST['test_update'])) {
        $test_rate = 9999.99;
        $updated = $db->query("
            UPDATE metal_rates 
            SET rate_per_gram = ?, updated_at = NOW() 
            WHERE metal_type = 'Gold' AND purity = '24K' AND rate_date = CURDATE()
        ", [$test_rate]);
        
        if ($updated) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "<p>✅ Test update successful! Gold 24K rate updated to ₹{$test_rate}</p>";
            echo "<p><a href='check-rates.php'>Refresh to see changes</a></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
            echo "<p>❌ Test update failed!</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='test_update' value='1' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🧪 Test Update Gold 24K Rate";
        echo "</button>";
        echo "</form>";
    }
    
    // Check table structure
    echo "<h2>🔍 Table Structure</h2>";
    $columns = $db->fetchAll("DESCRIBE metal_rates");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Column</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>{$column['Field']}</td>";
        echo "<td style='padding: 10px;'>{$column['Type']}</td>";
        echo "<td style='padding: 10px;'>{$column['Null']}</td>";
        echo "<td style='padding: 10px;'>{$column['Key']}</td>";
        echo "<td style='padding: 10px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460; margin: 20px 0;'>";
    echo "<h3>🔧 Diagnosis</h3>";
    echo "<p>Based on the logs, the metal rates update functionality is working correctly:</p>";
    echo "<ul>";
    echo "<li>✅ Form data is being received properly</li>";
    echo "<li>✅ Database queries are executing</li>";
    echo "<li>✅ Rates are being processed and stored</li>";
    echo "</ul>";
    echo "<p><strong>If you're not seeing updates on the metal-rates.php page, try:</strong></p>";
    echo "<ul>";
    echo "<li>Clear your browser cache</li>";
    echo "<li>Check if there are JavaScript errors in browser console</li>";
    echo "<li>Verify the success message is displaying</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Quick Links:</h3>";
    echo "<ul>";
    echo "<li><a href='metal-rates.php' target='_blank'>📊 Metal Rates Page</a></li>";
    echo "<li><a href='test-metal-rates.php' target='_blank'>🧪 Metal Rates Test</a></li>";
    echo "<li><a href='index.php' target='_blank'>🏠 Dashboard</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
