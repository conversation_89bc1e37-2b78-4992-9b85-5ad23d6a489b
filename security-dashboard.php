<?php
/**
 * Security Dashboard - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Get security metrics
try {
    // Total users and active sessions
    $totalUsers = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'];
    $activeSessions = $db->fetch("
        SELECT COUNT(*) as count 
        FROM login_sessions 
        WHERE is_active = 1 AND last_activity > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ")['count'];
    
    // Failed login attempts today
    $failedLoginsToday = $db->fetch("
        SELECT COUNT(*) as count 
        FROM system_logs 
        WHERE log_type = 'security' 
        AND level = 'warning' 
        AND message LIKE '%Failed login%' 
        AND DATE(created_at) = CURDATE()
    ")['count'];
    
    // Locked accounts
    $lockedAccounts = $db->fetch("
        SELECT COUNT(*) as count 
        FROM users 
        WHERE is_active = 1 
        AND login_attempts >= 5 
        AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    ")['count'];
    
    // 2FA enabled users
    $twoFAUsers = $db->fetch("
        SELECT COUNT(*) as count 
        FROM users 
        WHERE is_active = 1 AND two_factor_enabled = 1
    ")['count'];
    
    // Recent security events
    $recentEvents = $db->fetchAll("
        SELECT sl.*, u.username, u.full_name
        FROM system_logs sl
        LEFT JOIN users u ON sl.user_id = u.id
        WHERE sl.log_type IN ('security', 'access')
        ORDER BY sl.created_at DESC
        LIMIT 20
    ");
    
    // Login activity by hour (last 24 hours)
    $loginActivity = $db->fetchAll("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as login_count
        FROM system_logs
        WHERE log_type = 'access' 
        AND message LIKE '%login%'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    
    // Top IP addresses with failed attempts
    $suspiciousIPs = $db->fetchAll("
        SELECT 
            ip_address,
            COUNT(*) as attempt_count,
            MAX(created_at) as last_attempt
        FROM system_logs
        WHERE log_type = 'security' 
        AND level = 'warning'
        AND message LIKE '%Failed login%'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY ip_address
        HAVING attempt_count >= 3
        ORDER BY attempt_count DESC
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $error = "Error loading security data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Dashboard - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Security Dashboard</h2>
                    <p class="text-muted mb-0">Monitor authentication and security events</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-outline-success" onclick="exportSecurityReport()">
                        <i class="fas fa-download me-2"></i>Export Report
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Security Metrics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h3 class="mb-1"><?php echo $totalUsers; ?></h3>
                            <small class="text-muted">Total Users</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-desktop fa-2x text-success mb-2"></i>
                            <h3 class="mb-1"><?php echo $activeSessions; ?></h3>
                            <small class="text-muted">Active Sessions</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <h3 class="mb-1"><?php echo $failedLoginsToday; ?></h3>
                            <small class="text-muted">Failed Logins Today</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-lock fa-2x text-danger mb-2"></i>
                            <h3 class="mb-1"><?php echo $lockedAccounts; ?></h3>
                            <small class="text-muted">Locked Accounts</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                            <h3 class="mb-1"><?php echo $twoFAUsers; ?></h3>
                            <small class="text-muted">2FA Enabled</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-percentage fa-2x text-secondary mb-2"></i>
                            <h3 class="mb-1"><?php echo $totalUsers > 0 ? round(($twoFAUsers / $totalUsers) * 100) : 0; ?>%</h3>
                            <small class="text-muted">2FA Adoption</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Recent Security Events -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Security Events</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Event</th>
                                            <th>User</th>
                                            <th>IP Address</th>
                                            <th>Level</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($recentEvents)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center py-4 text-muted">
                                                    No recent security events
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($recentEvents as $event): ?>
                                                <tr>
                                                    <td>
                                                        <small><?php echo date('H:i:s', strtotime($event['created_at'])); ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($event['message']); ?></td>
                                                    <td>
                                                        <?php if ($event['username']): ?>
                                                            <strong><?php echo htmlspecialchars($event['username']); ?></strong>
                                                        <?php else: ?>
                                                            <span class="text-muted">System</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <code><?php echo htmlspecialchars($event['ip_address'] ?? 'N/A'); ?></code>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $levelClass = '';
                                                        switch ($event['level']) {
                                                            case 'error':
                                                            case 'critical':
                                                                $levelClass = 'bg-danger';
                                                                break;
                                                            case 'warning':
                                                                $levelClass = 'bg-warning';
                                                                break;
                                                            case 'info':
                                                                $levelClass = 'bg-info';
                                                                break;
                                                            default:
                                                                $levelClass = 'bg-secondary';
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $levelClass; ?>">
                                                            <?php echo ucfirst($event['level']); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Suspicious IP Addresses -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Suspicious IP Addresses</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($suspiciousIPs)): ?>
                                <p class="text-muted text-center">No suspicious activity detected</p>
                            <?php else: ?>
                                <?php foreach ($suspiciousIPs as $ip): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                        <div>
                                            <code><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                            <br>
                                            <small class="text-muted">
                                                Last: <?php echo date('d/m H:i', strtotime($ip['last_attempt'])); ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-danger">
                                            <?php echo $ip['attempt_count']; ?> attempts
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Activity Chart -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Login Activity (Last 24 Hours)</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="loginActivityChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Login Activity Chart
        const ctx = document.getElementById('loginActivityChart').getContext('2d');
        const loginData = <?php echo json_encode($loginActivity); ?>;
        
        // Prepare chart data
        const hours = Array.from({length: 24}, (_, i) => i);
        const loginCounts = hours.map(hour => {
            const data = loginData.find(d => parseInt(d.hour) === hour);
            return data ? parseInt(data.login_count) : 0;
        });
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: hours.map(h => h.toString().padStart(2, '0') + ':00'),
                datasets: [{
                    label: 'Login Attempts',
                    data: loginCounts,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
        
        function refreshDashboard() {
            location.reload();
        }
        
        function exportSecurityReport() {
            window.open('export-security-report.php', '_blank');
        }
        
        // Auto-refresh every 5 minutes
        setInterval(refreshDashboard, 5 * 60 * 1000);
    </script>
</body>
</html>
