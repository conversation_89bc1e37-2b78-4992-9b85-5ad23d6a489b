<?php
/**
 * Individual Page Testing Script
 * Tests each page individually and reports functionality
 */

require_once 'config/database.php';

echo "<html><head><title>Individual Page Tests</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
h1, h2 { color: #333; }
.page-link { display: inline-block; margin: 5px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.page-link:hover { background: #0056b3; }
</style></head><body>";

echo "<h1>🔧 Individual Page Testing Results</h1>";

try {
    $db = getDB();
    
    // Get current data counts
    $suppliers = $db->fetch("SELECT COUNT(*) as count FROM suppliers")['count'];
    $products = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
    $customers = $db->fetch("SELECT COUNT(*) as count FROM customers")['count'];
    $sales = $db->fetch("SELECT COUNT(*) as count FROM sales")['count'];
    $inventory = $db->fetch("SELECT COUNT(*) as count FROM inventory")['count'];
    
    echo "<div class='test-result info'>";
    echo "<h2>📊 Current Database Status</h2>";
    echo "<ul>";
    echo "<li><strong>Suppliers:</strong> $suppliers</li>";
    echo "<li><strong>Products:</strong> $products</li>";
    echo "<li><strong>Customers:</strong> $customers</li>";
    echo "<li><strong>Sales:</strong> $sales</li>";
    echo "<li><strong>Inventory Records:</strong> $inventory</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test each page
    $pages = [
        'index.php' => [
            'title' => '📊 Dashboard',
            'description' => 'Main dashboard with statistics and overview',
            'test_queries' => [
                "SELECT COUNT(*) as count FROM products",
                "SELECT COUNT(*) as count FROM customers", 
                "SELECT COUNT(*) as count FROM sales",
                "SELECT SUM(grand_total) as revenue FROM sales"
            ]
        ],
        'products.php' => [
            'title' => '💎 Products Management',
            'description' => 'Product listing and management',
            'test_queries' => [
                "SELECT p.*, c.category_name, s.supplier_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LEFT JOIN suppliers s ON p.supplier_id = s.id LIMIT 5"
            ]
        ],
        'inventory.php' => [
            'title' => '📦 Inventory Management',
            'description' => 'Stock levels and inventory tracking',
            'test_queries' => [
                "SELECT i.*, p.product_name FROM inventory i LEFT JOIN products p ON i.product_id = p.id LIMIT 5"
            ]
        ],
        'suppliers.php' => [
            'title' => '🚚 Suppliers Management',
            'description' => 'Supplier information and management',
            'test_queries' => [
                "SELECT * FROM suppliers WHERE is_active = 1 LIMIT 5"
            ]
        ],
        'customers.php' => [
            'title' => '👥 Customers Management',
            'description' => 'Customer database and management',
            'test_queries' => [
                "SELECT * FROM customers WHERE is_active = 1 LIMIT 5"
            ]
        ],
        'sales.php' => [
            'title' => '📊 Sales History',
            'description' => 'Sales transactions and history',
            'test_queries' => [
                "SELECT s.*, c.customer_name FROM sales s LEFT JOIN customers c ON s.customer_id = c.id LIMIT 5"
            ]
        ],
        'billing.php' => [
            'title' => '🛒 New Sale/Billing',
            'description' => 'Create new sales transactions',
            'test_queries' => [
                "SELECT * FROM products WHERE is_active = 1 LIMIT 3",
                "SELECT * FROM customers WHERE is_active = 1 LIMIT 3"
            ]
        ],
        'metal-rates.php' => [
            'title' => '💰 Metal Rates',
            'description' => 'Current metal rates management',
            'test_queries' => [
                "SELECT * FROM metal_rates WHERE rate_date = CURDATE()"
            ]
        ],
        'categories.php' => [
            'title' => '📂 Categories Management',
            'description' => 'Product categories management',
            'test_queries' => [
                "SELECT * FROM categories WHERE is_active = 1"
            ]
        ],
        'settings.php' => [
            'title' => '⚙️ System Settings',
            'description' => 'System configuration and settings',
            'test_queries' => [
                "SELECT * FROM settings LIMIT 5"
            ]
        ],
        'users.php' => [
            'title' => '👤 User Management',
            'description' => 'User accounts and permissions',
            'test_queries' => [
                "SELECT id, username, email, role, is_active FROM users"
            ]
        ]
    ];
    
    foreach ($pages as $page => $info) {
        echo "<div class='test-result'>";
        echo "<h3>{$info['title']} - $page</h3>";
        echo "<p><strong>Description:</strong> {$info['description']}</p>";
        
        // Test if file exists
        if (!file_exists($page)) {
            echo "<div style='color: #721c24;'>❌ <strong>File not found!</strong></div>";
            echo "</div>";
            continue;
        }
        
        // Test database queries for this page
        $queryResults = [];
        $allQueriesWork = true;
        
        foreach ($info['test_queries'] as $query) {
            try {
                $result = $db->fetchAll($query);
                $queryResults[] = "✅ Query successful - " . count($result) . " records";
            } catch (Exception $e) {
                $queryResults[] = "❌ Query failed: " . $e->getMessage();
                $allQueriesWork = false;
            }
        }
        
        if ($allQueriesWork) {
            echo "<div style='color: #155724;'>✅ <strong>Database queries working</strong></div>";
        } else {
            echo "<div style='color: #721c24;'>❌ <strong>Some database queries failed</strong></div>";
        }
        
        echo "<details><summary>Query Results</summary><ul>";
        foreach ($queryResults as $result) {
            echo "<li>$result</li>";
        }
        echo "</ul></details>";
        
        echo "<a href='$page' target='_blank' class='page-link'>Open {$info['title']}</a>";
        echo "</div>";
    }
    
    // Test login page separately
    echo "<div class='test-result'>";
    echo "<h3>🔑 Login Page - login.php</h3>";
    echo "<p><strong>Description:</strong> User authentication and login</p>";
    
    if (file_exists('login.php')) {
        echo "<div style='color: #155724;'>✅ <strong>Login page exists</strong></div>";
        echo "<a href='login.php' target='_blank' class='page-link'>Open Login Page</a>";
    } else {
        echo "<div style='color: #721c24;'>❌ <strong>Login page not found!</strong></div>";
    }
    echo "</div>";
    
    echo "<div class='test-result success'>";
    echo "<h2>🎉 Page Testing Complete!</h2>";
    echo "<p>All pages have been tested for basic functionality. Click the links above to test each page individually.</p>";
    echo "<p><strong>Recommendation:</strong> Test each page manually to ensure the user interface is working correctly.</p>";
    echo "</div>";
    
    echo "<div class='test-result info'>";
    echo "<h2>🚀 Quick Navigation</h2>";
    echo "<p>Use these links to quickly navigate to different sections:</p>";
    foreach ($pages as $page => $info) {
        echo "<a href='$page' target='_blank' class='page-link'>{$info['title']}</a>";
    }
    echo "<a href='login.php' target='_blank' class='page-link'>🔑 Login</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-result error'>";
    echo "<h3>❌ Testing Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
