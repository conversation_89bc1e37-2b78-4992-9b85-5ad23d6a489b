<?php
/**
 * Customer History AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $customer_id = $_GET['id'] ?? null;
    
    if (!$customer_id) {
        throw new Exception('Customer ID required');
    }
    
    $db = getDB();
    
    // Get customer basic info
    $customer = $db->fetch("
        SELECT customer_name, business_name 
        FROM customers 
        WHERE id = ? AND is_active = 1
    ", [$customer_id]);
    
    if (!$customer) {
        throw new Exception('Customer not found');
    }
    
    // Get recent sales history
    $sales = $db->fetchAll("
        SELECT s.*,
               (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count,
               s.paid_amount,
               s.balance_amount
        FROM sales s
        WHERE s.customer_id = ? AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC, s.sale_time DESC
        LIMIT 10
    ", [$customer_id]);
    
    // Get recent payments (using sales table data)
    $payments = $db->fetchAll("
        SELECT s.id, s.bill_number, s.paid_amount as amount, s.payment_method,
               s.sale_date as payment_date
        FROM sales s
        WHERE s.customer_id = ? AND s.paid_amount > 0 AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC
        LIMIT 5
    ", [$customer_id]);
    
    // Generate HTML response
    echo "<div class='row'>";
    
    // Recent Sales
    echo "<div class='col-md-8'>";
    echo "<h6>Recent Sales</h6>";
    
    if (empty($sales)) {
        echo "<p class='text-muted'>No sales found</p>";
    } else {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-hover'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>Bill Number</th>";
        echo "<th>Date</th>";
        echo "<th>Items</th>";
        echo "<th>Amount</th>";
        echo "<th>Status</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($sales as $sale) {
            $statusClass = '';
            $statusText = '';
            
            if ($sale['balance_amount'] <= 0) {
                $statusClass = 'success';
                $statusText = 'Paid';
            } elseif ($sale['paid_amount'] > 0) {
                $statusClass = 'warning';
                $statusText = 'Partial';
            } else {
                $statusClass = 'danger';
                $statusText = 'Pending';
            }
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($sale['bill_number']) . "</strong></td>";
            echo "<td>" . date('d/m/Y', strtotime($sale['sale_date'])) . "</td>";
            echo "<td>" . $sale['item_count'] . " items</td>";
            echo "<td>₹" . number_format($sale['grand_total'], 2) . "</td>";
            echo "<td><span class='badge bg-$statusClass'>$statusText</span></td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Recent Payments
    echo "<div class='col-md-4'>";
    echo "<h6>Recent Payments</h6>";
    
    if (empty($payments)) {
        echo "<p class='text-muted'>No payments found</p>";
    } else {
        echo "<div class='list-group list-group-flush'>";
        
        foreach ($payments as $payment) {
            echo "<div class='list-group-item px-0'>";
            echo "<div class='d-flex justify-content-between align-items-start'>";
            echo "<div>";
            echo "<h6 class='mb-1'>" . htmlspecialchars($payment['bill_number']) . "</h6>";
            echo "<small class='text-muted'>" . date('d/m/Y', strtotime($payment['payment_date'])) . "</small>";
            echo "</div>";
            echo "<div class='text-end'>";
            echo "<strong class='text-success'>₹" . number_format($payment['amount'], 2) . "</strong><br>";
            echo "<small class='text-muted'>" . ucfirst($payment['payment_method']) . "</small>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // Summary Statistics
    $totalSales = count($sales);
    $totalAmount = array_sum(array_column($sales, 'grand_total'));
    $totalPaid = array_sum(array_column($sales, 'paid_amount'));
    $totalOutstanding = array_sum(array_column($sales, 'balance_amount'));
    
    echo "<div class='row mt-4'>";
    echo "<div class='col-12'>";
    echo "<div class='card bg-light'>";
    echo "<div class='card-body'>";
    echo "<h6>Summary (Last 10 transactions)</h6>";
    echo "<div class='row text-center'>";
    echo "<div class='col-3'>";
    echo "<div class='h5 mb-0'>$totalSales</div>";
    echo "<small class='text-muted'>Total Sales</small>";
    echo "</div>";
    echo "<div class='col-3'>";
    echo "<div class='h5 mb-0'>₹" . number_format($totalAmount, 2) . "</div>";
    echo "<small class='text-muted'>Total Amount</small>";
    echo "</div>";
    echo "<div class='col-3'>";
    echo "<div class='h5 mb-0 text-success'>₹" . number_format($totalPaid, 2) . "</div>";
    echo "<small class='text-muted'>Paid Amount</small>";
    echo "</div>";
    echo "<div class='col-3'>";
    echo "<div class='h5 mb-0 text-danger'>₹" . number_format($totalOutstanding, 2) . "</div>";
    echo "<small class='text-muted'>Outstanding</small>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
