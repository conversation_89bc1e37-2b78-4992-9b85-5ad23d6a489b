<?php
/**
 * Category Tree AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $db = getDB();
    
    // Get all categories with hierarchy information
    $categories = $db->fetchAll("
        SELECT c.*, 
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count,
               (SELECT COUNT(*) FROM categories WHERE parent_id = c.id AND is_active = 1) as subcategory_count
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.sort_order, c.category_name
    ");
    
    // Build hierarchy
    $categoryTree = [];
    $categoryMap = [];
    
    // First pass: create map and identify main categories
    foreach ($categories as $category) {
        $categoryMap[$category['id']] = $category;
        $categoryMap[$category['id']]['children'] = [];
        
        if (!$category['parent_id']) {
            $categoryTree[] = &$categoryMap[$category['id']];
        }
    }
    
    // Second pass: build parent-child relationships
    foreach ($categories as $category) {
        if ($category['parent_id'] && isset($categoryMap[$category['parent_id']])) {
            $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$category['id']];
        }
    }
    
    // Function to render tree
    function renderCategoryTree($categories, $level = 0) {
        $html = '';
        $indent = str_repeat('  ', $level);
        
        foreach ($categories as $category) {
            $icon = $level === 0 ? 'fas fa-folder' : 'fas fa-folder-open';
            $badgeClass = $level === 0 ? 'bg-primary' : 'bg-secondary';
            
            $html .= "<div class='tree-item' style='margin-left: " . ($level * 20) . "px;'>";
            $html .= "<div class='d-flex align-items-center justify-content-between p-2 mb-1 bg-light rounded'>";
            $html .= "<div class='d-flex align-items-center'>";
            $html .= "<i class='$icon me-2 text-primary'></i>";
            $html .= "<strong>" . htmlspecialchars($category['category_name']) . "</strong>";
            
            if ($category['product_count'] > 0) {
                $html .= "<span class='badge $badgeClass ms-2'>" . $category['product_count'] . " products</span>";
            }
            
            if ($category['subcategory_count'] > 0) {
                $html .= "<span class='badge bg-info ms-1'>" . $category['subcategory_count'] . " subcategories</span>";
            }
            
            $html .= "</div>";
            
            // Action buttons
            $html .= "<div class='btn-group btn-group-sm'>";
            $html .= "<button class='btn btn-outline-primary btn-sm' onclick='editCategory(" . $category['id'] . ")' title='Edit'>";
            $html .= "<i class='fas fa-edit'></i>";
            $html .= "</button>";
            
            if ($category['product_count'] > 0) {
                $html .= "<button class='btn btn-outline-success btn-sm' onclick='viewProducts(" . $category['id'] . ")' title='View Products'>";
                $html .= "<i class='fas fa-box'></i>";
                $html .= "</button>";
            }
            
            $html .= "</div>";
            $html .= "</div>";
            
            // Description
            if ($category['description']) {
                $html .= "<div class='text-muted small mb-2' style='margin-left: " . (($level * 20) + 25) . "px;'>";
                $html .= htmlspecialchars($category['description']);
                $html .= "</div>";
            }
            
            // Render children
            if (!empty($category['children'])) {
                $html .= renderCategoryTree($category['children'], $level + 1);
            }
            
            $html .= "</div>";
        }
        
        return $html;
    }
    
    // Generate tree HTML
    $treeHtml = '';
    
    if (empty($categoryTree)) {
        $treeHtml = "<div class='text-center py-4 text-muted'>";
        $treeHtml .= "<i class='fas fa-sitemap fa-3x mb-3 opacity-25'></i>";
        $treeHtml .= "<p>No categories found</p>";
        $treeHtml .= "</div>";
    } else {
        // Add summary at the top
        $totalCategories = count($categories);
        $mainCategories = count($categoryTree);
        $totalProducts = array_sum(array_column($categories, 'product_count'));
        
        $treeHtml .= "<div class='alert alert-info mb-4'>";
        $treeHtml .= "<h6><i class='fas fa-info-circle me-2'></i>Category Summary</h6>";
        $treeHtml .= "<div class='row text-center'>";
        $treeHtml .= "<div class='col-4'>";
        $treeHtml .= "<div class='h5 mb-0'>$totalCategories</div>";
        $treeHtml .= "<small>Total Categories</small>";
        $treeHtml .= "</div>";
        $treeHtml .= "<div class='col-4'>";
        $treeHtml .= "<div class='h5 mb-0'>$mainCategories</div>";
        $treeHtml .= "<small>Main Categories</small>";
        $treeHtml .= "</div>";
        $treeHtml .= "<div class='col-4'>";
        $treeHtml .= "<div class='h5 mb-0'>$totalProducts</div>";
        $treeHtml .= "<small>Total Products</small>";
        $treeHtml .= "</div>";
        $treeHtml .= "</div>";
        $treeHtml .= "</div>";
        
        // Render the tree
        $treeHtml .= "<div class='category-tree'>";
        $treeHtml .= renderCategoryTree($categoryTree);
        $treeHtml .= "</div>";
        
        // Add expand/collapse functionality
        $treeHtml .= "
        <script>
            function editCategory(id) {
                window.parent.location.href = 'categories.php?action=edit&id=' + id;
            }
            
            function viewProducts(id) {
                window.open('products.php?category_id=' + id, '_blank');
            }
            
            // Add expand/collapse functionality
            document.querySelectorAll('.tree-item').forEach(item => {
                const hasChildren = item.querySelector('.tree-item');
                if (hasChildren) {
                    const header = item.querySelector('.d-flex');
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function(e) {
                        if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'I') {
                            const children = Array.from(item.children).slice(1);
                            children.forEach(child => {
                                child.style.display = child.style.display === 'none' ? 'block' : 'none';
                            });
                        }
                    });
                }
            });
        </script>";
    }
    
    echo $treeHtml;
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
