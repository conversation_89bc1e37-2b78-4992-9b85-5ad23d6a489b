<?php
/**
 * Implement Tunch Calculator for Indian Jewellery Business
 */

require_once 'config/database.php';

// Create the Tunch Calculator Class
$tunchCalculatorCode = '<?php
/**
 * Tunch Calculator for Indian Jewellery Wholesale Business
 * Implements the standard Indian jewellery calculation formulas
 */

class TunchCalculator {
    private $db;
    
    public function __construct($database = null) {
        $this->db = $database ?: getDB();
    }
    
    /**
     * Calculate Tunch based on the standard formula
     * 
     * @param float $grossWeight Total weight including stones
     * @param float $stoneWeight Weight of stones/gems
     * @param float $tunchPercentage Purity percentage (e.g., 91.6 for 22K)
     * @param float $baseRate Base gold/silver rate per gram
     * @param float $makingCharges Making charges
     * @param float $stoneCharges Stone charges
     * @param float $wastagePercentage Wastage percentage
     * @return array Detailed calculation breakdown
     */
    public function calculateTunch($grossWeight, $stoneWeight, $tunchPercentage, $baseRate, $makingCharges = 0, $stoneCharges = 0, $wastagePercentage = 0) {
        // Step 1: Calculate Net Weight
        $netWeight = $grossWeight - $stoneWeight;
        
        // Step 2: Calculate Tunch Weight
        $tunchWeight = $netWeight * ($tunchPercentage / 100);
        
        // Step 3: Calculate Tunch Rate
        $tunchRate = $baseRate * ($tunchPercentage / 100);
        
        // Step 4: Calculate Metal Value
        $metalValue = $tunchWeight * $tunchRate;
        
        // Step 5: Calculate Wastage Amount
        $wastageAmount = $metalValue * ($wastagePercentage / 100);
        
        // Step 6: Calculate Final Value
        $finalValue = $metalValue + $makingCharges + $stoneCharges + $wastageAmount;
        
        return [
            "gross_weight" => round($grossWeight, 3),
            "stone_weight" => round($stoneWeight, 3),
            "net_weight" => round($netWeight, 3),
            "tunch_percentage" => $tunchPercentage,
            "tunch_weight" => round($tunchWeight, 3),
            "base_rate" => $baseRate,
            "tunch_rate" => round($tunchRate, 2),
            "metal_value" => round($metalValue, 2),
            "making_charges" => $makingCharges,
            "stone_charges" => $stoneCharges,
            "wastage_percentage" => $wastagePercentage,
            "wastage_amount" => round($wastageAmount, 2),
            "final_value" => round($finalValue, 2)
        ];
    }
    
    /**
     * Get current daily rate for metal and purity
     */
    public function getDailyRate($metalType, $purity, $date = null) {
        $date = $date ?: date("Y-m-d");
        
        try {
            $rate = $this->db->fetch("
                SELECT base_rate, tunch_rate 
                FROM daily_rates 
                WHERE metal_type = ? AND purity = ? AND rate_date = ? AND is_active = 1
            ", [$metalType, $purity, $date]);
            
            return $rate ?: null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Calculate product pricing based on current rates
     */
    public function calculateProductPrice($productId) {
        try {
            // Get product details
            $product = $this->db->fetch("
                SELECT p.*, i.* 
                FROM products p 
                LEFT JOIN inventory i ON p.id = i.product_id 
                WHERE p.id = ?
            ", [$productId]);
            
            if (!$product) {
                throw new Exception("Product not found");
            }
            
            // Get current daily rate
            $dailyRate = $this->getDailyRate($product["metal_type"], $product["purity"]);
            if (!$dailyRate) {
                throw new Exception("Daily rate not found for " . $product["metal_type"] . " " . $product["purity"]);
            }
            
            // Calculate using tunch formula
            return $this->calculateTunch(
                $product["gross_weight"],
                $product["stone_weight"],
                $product["tunch_percentage"],
                $dailyRate["base_rate"],
                $product["making_charges_per_gram"] * $product["net_weight"],
                $product["stone_cost"],
                $product["wastage_percentage"]
            );
            
        } catch (Exception $e) {
            throw new Exception("Error calculating product price: " . $e->getMessage());
        }
    }
    
    /**
     * Update inventory prices based on current rates
     */
    public function updateInventoryPrices($productId = null) {
        try {
            $whereClause = $productId ? "WHERE p.id = $productId" : "";
            
            $products = $this->db->fetchAll("
                SELECT p.id, p.metal_type, p.purity, p.gross_weight, p.stone_weight, 
                       p.tunch_percentage, p.making_charges_per_gram, p.wastage_percentage,
                       i.stone_cost_per_piece
                FROM products p 
                LEFT JOIN inventory i ON p.id = i.product_id 
                $whereClause
                AND p.is_active = 1
            ");
            
            $updated = 0;
            foreach ($products as $product) {
                $calculation = $this->calculateProductPrice($product["id"]);
                
                // Update inventory with calculated values
                $this->db->execute("
                    UPDATE inventory SET 
                        base_rate = ?,
                        tunch_rate = ?,
                        total_metal_value = ?,
                        making_cost = ?,
                        wastage_amount = ?,
                        selling_price = ?,
                        updated_at = NOW()
                    WHERE product_id = ?
                ", [
                    $calculation["base_rate"],
                    $calculation["tunch_rate"],
                    $calculation["metal_value"],
                    $calculation["making_charges"],
                    $calculation["wastage_amount"],
                    $calculation["final_value"],
                    $product["id"]
                ]);
                
                $updated++;
            }
            
            return $updated;
            
        } catch (Exception $e) {
            throw new Exception("Error updating inventory prices: " . $e->getMessage());
        }
    }
    
    /**
     * Save calculation to audit trail
     */
    public function saveCalculation($saleItemId, $calculation) {
        try {
            $this->db->execute("
                INSERT INTO tunch_calculations (
                    sale_item_id, gross_weight, stone_weight, net_weight,
                    tunch_percentage, tunch_weight, base_rate, tunch_rate,
                    metal_value, making_charges, stone_charges, wastage_amount,
                    final_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $saleItemId,
                $calculation["gross_weight"],
                $calculation["stone_weight"],
                $calculation["net_weight"],
                $calculation["tunch_percentage"],
                $calculation["tunch_weight"],
                $calculation["base_rate"],
                $calculation["tunch_rate"],
                $calculation["metal_value"],
                $calculation["making_charges"],
                $calculation["stone_charges"],
                $calculation["wastage_amount"],
                $calculation["final_value"]
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (Exception $e) {
            throw new Exception("Error saving calculation: " . $e->getMessage());
        }
    }
    
    /**
     * Get standard tunch percentages for different purities
     */
    public static function getStandardTunchPercentages() {
        return [
            "24K" => 99.9,
            "22K" => 91.6,
            "21K" => 87.5,
            "20K" => 83.3,
            "18K" => 75.0,
            "16K" => 66.7,
            "14K" => 58.3,
            "925" => 92.5,  // Silver
            "999" => 99.9   // Pure Silver
        ];
    }
}
?>';

// Save the Tunch Calculator class
if (!is_dir('lib')) {
    mkdir('lib', 0755, true);
}

file_put_contents('lib/TunchCalculator.php', $tunchCalculatorCode);

echo "<h2>🧮 Tunch Calculator Implementation</h2>";

echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Tunch Calculator Class Created</h3>";
echo "<p><strong>Location:</strong> lib/TunchCalculator.php</p>";
echo "<p><strong>Features:</strong></p>";
echo "<ul>";
echo "<li>✅ Standard Tunch Formula Implementation</li>";
echo "<li>✅ Daily Rate Integration</li>";
echo "<li>✅ Product Price Calculation</li>";
echo "<li>✅ Inventory Price Updates</li>";
echo "<li>✅ Calculation Audit Trail</li>";
echo "<li>✅ Standard Purity Percentages</li>";
echo "</ul>";
echo "</div>";

// Test the calculator
echo "<h3>🧪 Testing Tunch Calculator</h3>";

try {
    require_once 'lib/TunchCalculator.php';
    $calculator = new TunchCalculator();
    
    // Test calculation with sample data
    $testCalculation = $calculator->calculateTunch(
        10.500,  // Gross Weight (grams)
        1.200,   // Stone Weight (grams)
        91.6,    // Tunch Percentage (22K gold)
        6500,    // Base Rate (₹ per gram)
        500,     // Making Charges
        2000,    // Stone Charges
        2.5      // Wastage Percentage
    );
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Sample Calculation Result</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th style='padding: 8px; background: #f8f9fa;'>Parameter</th><th style='padding: 8px; background: #f8f9fa;'>Value</th></tr>";
    
    foreach ($testCalculation as $key => $value) {
        $label = ucwords(str_replace('_', ' ', $key));
        if (in_array($key, ['base_rate', 'tunch_rate', 'metal_value', 'making_charges', 'stone_charges', 'wastage_amount', 'final_value'])) {
            $value = '₹' . number_format($value, 2);
        } elseif (in_array($key, ['gross_weight', 'stone_weight', 'net_weight', 'tunch_weight'])) {
            $value = $value . ' grams';
        } elseif (in_array($key, ['tunch_percentage', 'wastage_percentage'])) {
            $value = $value . '%';
        }
        echo "<tr><td style='padding: 8px;'>$label</td><td style='padding: 8px;'><strong>$value</strong></td></tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Calculator Working Perfectly!</h4>";
    echo "<p>The Tunch Calculator is functioning correctly and ready for integration.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Calculator Test Failed</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Integration instructions
echo "<h3>🔧 Integration Instructions</h3>";

echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 How to Use the Tunch Calculator</h4>";
echo "<div style='font-family: monospace; background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
echo "// Include the calculator<br>";
echo "require_once 'lib/TunchCalculator.php';<br><br>";
echo "// Create calculator instance<br>";
echo "\$calculator = new TunchCalculator();<br><br>";
echo "// Calculate tunch for a product<br>";
echo "\$result = \$calculator->calculateTunch(<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;\$grossWeight, \$stoneWeight, \$tunchPercentage,<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;\$baseRate, \$makingCharges, \$stoneCharges, \$wastagePercentage<br>";
echo ");<br><br>";
echo "// Get current daily rate<br>";
echo "\$rate = \$calculator->getDailyRate('Gold', '22K');<br><br>";
echo "// Calculate product price<br>";
echo "\$price = \$calculator->calculateProductPrice(\$productId);";
echo "</div>";
echo "</div>";

// Next steps
echo "<h3>🎯 Next Steps</h3>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 Implementation Roadmap</h4>";
echo "<ol>";
echo "<li><strong>Add Daily Rates:</strong> Insert current gold/silver rates</li>";
echo "<li><strong>Update Products:</strong> Add tunch percentages and weights to existing products</li>";
echo "<li><strong>Integrate with Billing:</strong> Use calculator in billing system</li>";
echo "<li><strong>Update Inventory:</strong> Recalculate all inventory prices</li>";
echo "<li><strong>Test with Real Data:</strong> Verify calculations with actual jewellery items</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='add-sample-jewellery-data.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Add Sample Data</a>";
echo "<a href='test-tunch-calculator.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Calculator</a>";
echo "<a href='integrate-with-billing.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Integrate with Billing</a>";
echo "</div>";
?>
