<?php
/**
 * System Logs AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $logType = $_GET['type'] ?? 'all';
    $logDate = $_GET['date'] ?? date('Y-m-d');
    
    $db = getDB();
    
    // Create logs table if it doesn't exist
    $db->query("
        CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            log_type ENUM('error', 'access', 'system', 'security', 'info') DEFAULT 'info',
            level ENUM('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
            message TEXT NOT NULL,
            context JSON,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_log_type (log_type),
            INDEX idx_level (level),
            INDEX idx_created_at (created_at)
        )
    ");
    
    // Build query based on log type
    $whereClause = "DATE(created_at) = ?";
    $params = [$logDate];
    
    if ($logType !== 'all') {
        $whereClause .= " AND log_type = ?";
        $params[] = $logType;
    }
    
    // Get logs
    $logs = $db->fetchAll("
        SELECT l.*, u.username
        FROM system_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE $whereClause
        ORDER BY l.created_at DESC
        LIMIT 100
    ", $params);
    
    // If no logs found, create some sample logs for demonstration
    if (empty($logs)) {
        $sampleLogs = [
            ['error', 'error', 'Database connection timeout', '{"query": "SELECT * FROM products", "duration": 30}'],
            ['access', 'info', 'User login successful', '{"username": "admin", "ip": "*************"}'],
            ['system', 'info', 'Database backup completed', '{"size": "15.2MB", "duration": "2m 30s"}'],
            ['security', 'warning', 'Multiple failed login attempts', '{"ip": "*************", "attempts": 5}'],
            ['system', 'info', 'Cache cleared by user', '{"cache_type": "all", "size_cleared": "5.8MB"}'],
            ['access', 'info', 'User logout', '{"username": "admin", "session_duration": "2h 15m"}'],
            ['error', 'warning', 'Low disk space warning', '{"available": "2.1GB", "threshold": "5GB"}'],
            ['system', 'info', 'Scheduled task executed', '{"task": "inventory_sync", "status": "success"}']
        ];
        
        foreach ($sampleLogs as $log) {
            $db->query("
                INSERT INTO system_logs (log_type, level, message, context, ip_address, created_at) 
                VALUES (?, ?, ?, ?, '*************', DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 24) HOUR))
            ", $log);
        }
        
        // Re-fetch logs
        $logs = $db->fetchAll("
            SELECT l.*, u.username
            FROM system_logs l
            LEFT JOIN users u ON l.user_id = u.id
            WHERE $whereClause
            ORDER BY l.created_at DESC
            LIMIT 100
        ", $params);
    }
    
    if (empty($logs)) {
        echo "<div class='alert alert-info text-center'>";
        echo "<i class='fas fa-info-circle fa-2x mb-2'></i>";
        echo "<p>No logs found for the selected date and type.</p>";
        echo "</div>";
        return;
    }
    
    // Display logs
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-hover'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Time</th>";
    echo "<th>Type</th>";
    echo "<th>Level</th>";
    echo "<th>Message</th>";
    echo "<th>User</th>";
    echo "<th>IP</th>";
    echo "<th>Actions</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($logs as $log) {
        // Determine badge classes based on log level
        $levelClass = '';
        $typeClass = '';
        
        switch ($log['level']) {
            case 'critical':
            case 'error':
                $levelClass = 'bg-danger';
                break;
            case 'warning':
                $levelClass = 'bg-warning';
                break;
            case 'info':
                $levelClass = 'bg-info';
                break;
            case 'debug':
                $levelClass = 'bg-secondary';
                break;
            default:
                $levelClass = 'bg-primary';
        }
        
        switch ($log['log_type']) {
            case 'error':
                $typeClass = 'bg-danger';
                break;
            case 'security':
                $typeClass = 'bg-warning';
                break;
            case 'system':
                $typeClass = 'bg-info';
                break;
            case 'access':
                $typeClass = 'bg-success';
                break;
            default:
                $typeClass = 'bg-primary';
        }
        
        echo "<tr>";
        echo "<td><small>" . date('H:i:s', strtotime($log['created_at'])) . "</small></td>";
        echo "<td><span class='badge $typeClass'>" . ucfirst($log['log_type']) . "</span></td>";
        echo "<td><span class='badge $levelClass'>" . ucfirst($log['level']) . "</span></td>";
        echo "<td>";
        echo "<div>" . htmlspecialchars($log['message']) . "</div>";
        
        // Show context if available
        if ($log['context']) {
            $context = json_decode($log['context'], true);
            if ($context) {
                echo "<small class='text-muted'>";
                foreach ($context as $key => $value) {
                    echo "<strong>$key:</strong> $value ";
                }
                echo "</small>";
            }
        }
        echo "</td>";
        echo "<td>" . ($log['username'] ? htmlspecialchars($log['username']) : 'System') . "</td>";
        echo "<td><small>" . htmlspecialchars($log['ip_address'] ?? '-') . "</small></td>";
        echo "<td>";
        echo "<button class='btn btn-sm btn-outline-info' onclick='viewLogDetails(" . $log['id'] . ")' title='View Details'>";
        echo "<i class='fas fa-eye'></i>";
        echo "</button>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // Summary statistics
    $totalLogs = count($logs);
    $errorCount = count(array_filter($logs, function($log) { return $log['level'] === 'error' || $log['level'] === 'critical'; }));
    $warningCount = count(array_filter($logs, function($log) { return $log['level'] === 'warning'; }));
    
    echo "<div class='row mt-3'>";
    echo "<div class='col-md-12'>";
    echo "<div class='alert alert-light'>";
    echo "<div class='row text-center'>";
    echo "<div class='col-4'>";
    echo "<div class='h5 mb-0'>$totalLogs</div>";
    echo "<small class='text-muted'>Total Logs</small>";
    echo "</div>";
    echo "<div class='col-4'>";
    echo "<div class='h5 mb-0 text-danger'>$errorCount</div>";
    echo "<small class='text-muted'>Errors</small>";
    echo "</div>";
    echo "<div class='col-4'>";
    echo "<div class='h5 mb-0 text-warning'>$warningCount</div>";
    echo "<small class='text-muted'>Warnings</small>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<script>
        function viewLogDetails(logId) {
            // This would show detailed log information in a modal
            alert('Log details for ID: ' + logId + ' (Feature to be implemented)');
        }
    </script>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error loading logs: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
