<?php
/**
 * Backup & Restore - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Handle backup creation
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'create_backup') {
    try {
        $backup_name = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backup_path = 'backups/' . $backup_name;
        
        // Create backups directory if it doesn't exist
        if (!is_dir('backups')) {
            mkdir('backups', 0755, true);
        }
        
        // Get all tables
        $tables = $db->fetchAll("SHOW TABLES");
        $backup_content = "-- Indian Jewellery Wholesale Management System Backup\n";
        $backup_content .= "-- Created: " . date('Y-m-d H:i:s') . "\n\n";
        $backup_content .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
        
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            
            // Get table structure
            $create_table = $db->fetch("SHOW CREATE TABLE `$table_name`");
            $backup_content .= "-- Table structure for `$table_name`\n";
            $backup_content .= "DROP TABLE IF EXISTS `$table_name`;\n";
            $backup_content .= $create_table['Create Table'] . ";\n\n";
            
            // Get table data
            $rows = $db->fetchAll("SELECT * FROM `$table_name`");
            if (!empty($rows)) {
                $backup_content .= "-- Data for table `$table_name`\n";
                $backup_content .= "INSERT INTO `$table_name` VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $row_values = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $row_values[] = 'NULL';
                        } else {
                            $row_values[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $row_values) . ')';
                }
                
                $backup_content .= implode(",\n", $values) . ";\n\n";
            }
        }
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        // Save backup file
        file_put_contents($backup_path, $backup_content);
        
        // Record backup in database
        $db->query("INSERT INTO backups (backup_name, file_path, file_size, created_by) VALUES (?, ?, ?, 1)", [
            $backup_name,
            $backup_path,
            filesize($backup_path)
        ]);
        
        $success = "Backup created successfully: $backup_name";
        
    } catch (Exception $e) {
        $error = "Error creating backup: " . $e->getMessage();
    }
}

// Handle backup download
if (isset($_GET['download']) && $_GET['download']) {
    $backup_id = intval($_GET['download']);
    $backup = $db->fetch("SELECT * FROM backups WHERE id = ?", [$backup_id]);
    
    if ($backup && file_exists($backup['file_path'])) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $backup['backup_name'] . '"');
        header('Content-Length: ' . filesize($backup['file_path']));
        readfile($backup['file_path']);
        exit;
    }
}

// Get backup history
$backups = $db->fetchAll("
    SELECT b.*, u.full_name as created_by_name
    FROM backups b
    LEFT JOIN users u ON b.created_by = u.id
    ORDER BY b.created_at DESC
    LIMIT 20
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup & Restore - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">Backup & Restore</h2>
                <p class="text-muted mb-0">Manage your system backups and data recovery</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Create Backup -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Create New Backup</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Important:</strong> Creating a backup will export all your data including products, customers, sales, and settings. This process may take a few minutes depending on your data size.
                        </div>
                        
                        <form method="POST" onsubmit="return confirmBackup()">
                            <input type="hidden" name="action" value="create_backup">
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download me-2"></i>Create Full Backup
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Backup History -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Backup History</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Backup Name</th>
                                        <th>Size</th>
                                        <th>Created</th>
                                        <th>Created By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($backups)): ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4 text-muted">
                                                <i class="fas fa-database fa-3x mb-3 opacity-25"></i>
                                                <p>No backups found</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($backups as $backup): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($backup['backup_name']); ?></strong>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($backup['file_path']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <?php echo formatFileSize($backup['file_size']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo formatDateTime($backup['created_at']); ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($backup['created_by_name'] ?: 'System'); ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="?download=<?php echo $backup['id']; ?>" 
                                                           class="btn btn-outline-primary" title="Download">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteBackup(<?php echo $backup['id']; ?>)" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Backup Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Backup Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
                            <ul class="mb-0 small">
                                <li>Backups include all system data</li>
                                <li>Store backups in a secure location</li>
                                <li>Test restore process regularly</li>
                                <li>Keep multiple backup copies</li>
                            </ul>
                        </div>
                        
                        <h6>What's Included:</h6>
                        <ul class="small">
                            <li>All product information</li>
                            <li>Customer database</li>
                            <li>Sales transactions</li>
                            <li>Inventory records</li>
                            <li>User accounts</li>
                            <li>System settings</li>
                            <li>Metal rates history</li>
                        </ul>
                    </div>
                </div>

                <!-- Restore Options -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Restore Database</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> Restoring will replace all current data!
                        </div>
                        
                        <form enctype="multipart/form-data" onsubmit="return confirmRestore()">
                            <div class="mb-3">
                                <label for="backup_file" class="form-label">Select Backup File</label>
                                <input type="file" class="form-control" id="backup_file" name="backup_file" 
                                       accept=".sql" required>
                                <div class="form-text">Upload a .sql backup file</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger" disabled>
                                    <i class="fas fa-upload me-2"></i>Restore Database
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Restore functionality requires additional implementation for security.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Automated Backups -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Automated Backups</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoBackup">
                            <label class="form-check-label" for="autoBackup">
                                Enable Daily Backups
                            </label>
                        </div>
                        <div class="form-text">Automatically create backups every day at midnight</div>
                        
                        <div class="mt-3">
                            <label for="backupRetention" class="form-label">Keep Backups For</label>
                            <select class="form-select" id="backupRetention">
                                <option value="7">7 days</option>
                                <option value="30" selected>30 days</option>
                                <option value="90">90 days</option>
                                <option value="365">1 year</option>
                            </select>
                        </div>
                        
                        <div class="d-grid mt-3">
                            <button class="btn btn-outline-primary" onclick="saveBackupSettings()">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function confirmBackup() {
            return confirm('Are you sure you want to create a backup? This process may take a few minutes.');
        }
        
        function confirmRestore() {
            const confirmed = confirm('WARNING: This will replace ALL current data with the backup data. This action cannot be undone. Are you sure you want to continue?');
            if (confirmed) {
                return confirm('This is your final warning. All current data will be lost. Continue with restore?');
            }
            return false;
        }
        
        function deleteBackup(id) {
            if (confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }
        
        function saveBackupSettings() {
            const autoBackup = document.getElementById('autoBackup').checked;
            const retention = document.getElementById('backupRetention').value;
            
            // Implementation would save these settings
            jewelleryApp.showNotification('Backup settings saved successfully', 'success');
        }
        
        // Show backup progress
        document.querySelector('form[onsubmit="return confirmBackup()"]').addEventListener('submit', function() {
            const button = this.querySelector('button[type="submit"]');
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Backup...';
            button.disabled = true;
        });
    </script>
</body>
</html>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
