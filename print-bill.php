<?php
/**
 * Print Bill - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$sale_id = $_GET['id'] ?? null;

if (!$sale_id) {
    die('Sale ID is required');
}

try {
    // Get sale details
    $sale = $db->fetch("
        SELECT s.*, c.customer_name, c.business_name, c.phone, c.email, c.address, c.gst_number
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.id = ?
    ", [$sale_id]);

    if (!$sale) {
        die('Sale not found');
    }

    // Get sale items
    $sale_items = $db->fetchAll("
        SELECT si.*, p.product_name, p.product_code, p.metal_type, p.purity, p.hsn_code
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$sale_id]);

} catch (Exception $e) {
    die('Error loading sale data: ' . $e->getMessage());
}

// Helper function to format currency
function formatCurrency($amount) {
    return CURRENCY_SYMBOL . number_format($amount, 2);
}

// Helper function to format date
function formatDate($date) {
    return date('d/m/Y', strtotime($date));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill - <?php echo htmlspecialchars($sale['bill_number']); ?></title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
        }
        
        .bill-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 11px;
            color: #666;
        }
        
        .bill-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .customer-info, .bill-details {
            width: 48%;
        }
        
        .section-title {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 11px;
        }
        
        .items-table td {
            font-size: 11px;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            margin-top: 20px;
            border-top: 2px solid #333;
            padding-top: 15px;
        }
        
        .totals-table {
            width: 300px;
            margin-left: auto;
        }
        
        .totals-table td {
            padding: 5px 10px;
            border: none;
        }
        
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #333;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        
        .print-buttons {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="print-buttons no-print">
        <button onclick="window.print()" class="btn btn-primary">Print Bill</button>
        <button onclick="window.close()" class="btn btn-secondary">Close</button>
    </div>

    <div class="bill-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name"><?php echo APP_NAME; ?></div>
            <div class="company-details">
                Indian Jewellery Wholesale Management System<br>
                Phone: +91-XXXXXXXXXX | Email: <EMAIL><br>
                Address: Your Business Address Here
            </div>
        </div>

        <!-- Bill Information -->
        <div class="bill-info">
            <div class="customer-info">
                <div class="section-title">Bill To:</div>
                <?php if ($sale['customer_name']): ?>
                    <strong><?php echo htmlspecialchars($sale['customer_name']); ?></strong><br>
                    <?php if ($sale['business_name']): ?>
                        <?php echo htmlspecialchars($sale['business_name']); ?><br>
                    <?php endif; ?>
                    <?php if ($sale['phone']): ?>
                        Phone: <?php echo htmlspecialchars($sale['phone']); ?><br>
                    <?php endif; ?>
                    <?php if ($sale['email']): ?>
                        Email: <?php echo htmlspecialchars($sale['email']); ?><br>
                    <?php endif; ?>
                    <?php if ($sale['address']): ?>
                        <?php echo nl2br(htmlspecialchars($sale['address'])); ?><br>
                    <?php endif; ?>
                    <?php if ($sale['gst_number']): ?>
                        GST: <?php echo htmlspecialchars($sale['gst_number']); ?>
                    <?php endif; ?>
                <?php else: ?>
                    <em>Walk-in Customer</em>
                <?php endif; ?>
            </div>
            
            <div class="bill-details">
                <div class="section-title">Bill Details:</div>
                <strong>Bill No:</strong> <?php echo htmlspecialchars($sale['bill_number']); ?><br>
                <strong>Date:</strong> <?php echo formatDate($sale['sale_date']); ?><br>
                <strong>Time:</strong> <?php echo date('H:i:s', strtotime($sale['sale_time'])); ?><br>
                <strong>Payment:</strong> <?php echo ucfirst($sale['payment_method']); ?><br>
                <strong>Status:</strong> <?php echo ucfirst(str_replace('_', ' ', $sale['payment_status'])); ?>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>S.No</th>
                    <th>Product</th>
                    <th>HSN</th>
                    <th>Qty</th>
                    <th>Weight (g)</th>
                    <th>Rate</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <?php $sr_no = 1; ?>
                <?php foreach ($sale_items as $item): ?>
                <tr>
                    <td class="text-center"><?php echo $sr_no++; ?></td>
                    <td>
                        <strong><?php echo htmlspecialchars($item['product_name']); ?></strong><br>
                        <small><?php echo htmlspecialchars($item['product_code']); ?></small>
                        <?php if ($item['metal_type']): ?>
                            <br><small><?php echo htmlspecialchars($item['metal_type'] . ' ' . $item['purity']); ?></small>
                        <?php endif; ?>
                    </td>
                    <td class="text-center"><?php echo htmlspecialchars($item['hsn_code'] ?: '-'); ?></td>
                    <td class="text-center"><?php echo $item['quantity']; ?></td>
                    <td class="text-right"><?php echo number_format($item['total_weight'], 3); ?></td>
                    <td class="text-right"><?php echo formatCurrency($item['unit_price']); ?></td>
                    <td class="text-right"><?php echo formatCurrency($item['total_price']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td>Subtotal:</td>
                    <td class="text-right"><?php echo formatCurrency($sale['subtotal']); ?></td>
                </tr>
                <?php if ($sale['discount_amount'] > 0): ?>
                <tr>
                    <td>Discount:</td>
                    <td class="text-right">-<?php echo formatCurrency($sale['discount_amount']); ?></td>
                </tr>
                <?php endif; ?>
                <?php if ($sale['tax_amount'] > 0): ?>
                <tr>
                    <td>Tax:</td>
                    <td class="text-right"><?php echo formatCurrency($sale['tax_amount']); ?></td>
                </tr>
                <?php endif; ?>
                <?php if ($sale['round_off'] != 0): ?>
                <tr>
                    <td>Round Off:</td>
                    <td class="text-right"><?php echo formatCurrency($sale['round_off']); ?></td>
                </tr>
                <?php endif; ?>
                <tr class="grand-total">
                    <td><strong>Grand Total:</strong></td>
                    <td class="text-right"><strong><?php echo formatCurrency($sale['grand_total']); ?></strong></td>
                </tr>
                <tr>
                    <td>Paid Amount:</td>
                    <td class="text-right"><?php echo formatCurrency($sale['paid_amount']); ?></td>
                </tr>
                <?php if ($sale['balance_amount'] > 0): ?>
                <tr>
                    <td><strong>Balance Due:</strong></td>
                    <td class="text-right"><strong><?php echo formatCurrency($sale['balance_amount']); ?></strong></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for your business!</p>
            <p>This is a computer generated bill.</p>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
