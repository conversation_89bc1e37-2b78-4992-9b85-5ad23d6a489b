<?php
/**
 * Sales Report Generator - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

$dateFrom = $_GET['from'] ?? date('Y-m-01'); // First day of current month
$dateTo = $_GET['to'] ?? date('Y-m-d'); // Today

try {
    // Get sales data for the date range
    $sales = $db->fetchAll("
        SELECT s.*, c.customer_name, c.business_name,
               (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count,
               COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0) as paid_amount,
               (s.grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0)) as balance_amount
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.sale_date BETWEEN ? AND ? AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC, s.sale_time DESC
    ", [$dateFrom, $dateTo]);

    // Calculate summary statistics
    $totalSales = count($sales);
    $totalRevenue = array_sum(array_column($sales, 'grand_total'));
    $totalPaid = array_sum(array_column($sales, 'paid_amount'));
    $totalOutstanding = array_sum(array_column($sales, 'balance_amount'));
    $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

    // Get payment method breakdown
    $paymentMethods = $db->fetchAll("
        SELECT payment_method, COUNT(*) as count, SUM(grand_total) as total
        FROM sales 
        WHERE sale_date BETWEEN ? AND ? AND is_cancelled = 0
        GROUP BY payment_method
        ORDER BY total DESC
    ", [$dateFrom, $dateTo]);

    // Get top customers for this period
    $topCustomers = $db->fetchAll("
        SELECT 
            COALESCE(c.customer_name, 'Walk-in Customer') as customer_name,
            COUNT(s.id) as order_count,
            SUM(s.grand_total) as total_amount
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.sale_date BETWEEN ? AND ? AND s.is_cancelled = 0
        GROUP BY s.customer_id, c.customer_name
        ORDER BY total_amount DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    // Get daily sales breakdown
    $dailySales = $db->fetchAll("
        SELECT 
            sale_date,
            COUNT(*) as order_count,
            SUM(grand_total) as total_amount,
            AVG(grand_total) as avg_amount
        FROM sales 
        WHERE sale_date BETWEEN ? AND ? AND is_cancelled = 0
        GROUP BY sale_date
        ORDER BY sale_date DESC
    ", [$dateFrom, $dateTo]);

} catch (Exception $e) {
    die('Error generating report: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Report - <?php echo date('d/m/Y', strtotime($dateFrom)); ?> to <?php echo date('d/m/Y', strtotime($dateTo)); ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
        .report-card { border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }
        .print-hide { display: block; }
        @media print {
            .print-hide { display: none !important; }
            body { background: white; }
            .report-card { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4 print-hide">
            <div>
                <h2 class="mb-1">Sales Report</h2>
                <p class="text-muted mb-0">
                    <?php echo date('d/m/Y', strtotime($dateFrom)); ?> to <?php echo date('d/m/Y', strtotime($dateTo)); ?>
                </p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>Print Report
                </button>
                <button class="btn btn-outline-secondary" onclick="window.close()">
                    <i class="fas fa-times me-2"></i>Close
                </button>
            </div>
        </div>

        <!-- Print Header -->
        <div class="d-none d-print-block text-center mb-4">
            <h1>Sales Report</h1>
            <h3><?php echo date('d/m/Y', strtotime($dateFrom)); ?> to <?php echo date('d/m/Y', strtotime($dateTo)); ?></h3>
            <p>Generated on: <?php echo date('d/m/Y H:i:s'); ?></p>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h3><?php echo $totalSales; ?></h3>
                        <p class="mb-0">Total Sales</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                        <h3><?php echo formatCurrency($totalRevenue); ?></h3>
                        <p class="mb-0">Total Revenue</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h3><?php echo formatCurrency($averageOrderValue); ?></h3>
                        <p class="mb-0">Avg Order Value</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h3><?php echo formatCurrency($totalOutstanding); ?></h3>
                        <p class="mb-0">Outstanding</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Daily Sales Breakdown -->
            <div class="col-md-6 mb-4">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Daily Sales Breakdown</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Orders</th>
                                        <th>Total Amount</th>
                                        <th>Avg Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($dailySales)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center py-3 text-muted">No sales data</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($dailySales as $day): ?>
                                            <tr>
                                                <td><?php echo formatDate($day['sale_date']); ?></td>
                                                <td><?php echo $day['order_count']; ?></td>
                                                <td><?php echo formatCurrency($day['total_amount']); ?></td>
                                                <td><?php echo formatCurrency($day['avg_amount']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Customers -->
            <div class="col-md-6 mb-4">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Top Customers</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Orders</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($topCustomers)): ?>
                                        <tr>
                                            <td colspan="3" class="text-center py-3 text-muted">No customer data</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($topCustomers as $customer): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                                                <td><?php echo $customer['order_count']; ?></td>
                                                <td><?php echo formatCurrency($customer['total_amount']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Methods</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Payment Method</th>
                                        <th>Count</th>
                                        <th>Total Amount</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($paymentMethods)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center py-3 text-muted">No payment data</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($paymentMethods as $method): ?>
                                            <?php $percentage = $totalRevenue > 0 ? ($method['total'] / $totalRevenue) * 100 : 0; ?>
                                            <tr>
                                                <td><?php echo ucfirst($method['payment_method']); ?></td>
                                                <td><?php echo $method['count']; ?></td>
                                                <td><?php echo formatCurrency($method['total']); ?></td>
                                                <td><?php echo number_format($percentage, 1); ?>%</td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="col-md-6 mb-4">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between">
                                <span>Total Sales:</span>
                                <strong><?php echo $totalSales; ?> transactions</strong>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>Total Revenue:</span>
                                <strong><?php echo formatCurrency($totalRevenue); ?></strong>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>Amount Paid:</span>
                                <strong class="text-success"><?php echo formatCurrency($totalPaid); ?></strong>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>Outstanding:</span>
                                <strong class="text-danger"><?php echo formatCurrency($totalOutstanding); ?></strong>
                            </div>
                            <div class="list-group-item d-flex justify-content-between">
                                <span>Average Order Value:</span>
                                <strong><?php echo formatCurrency($averageOrderValue); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
