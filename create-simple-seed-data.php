<?php
/**
 * Simple Seed Data Creation Script
 * Creates basic sample data for testing
 */

require_once 'config/database.php';

try {
    $db = getDB();
    $db->beginTransaction();
    
    echo "<h1>🔧 Creating Simple Seed Data</h1>";
    
    // 1. Create basic suppliers
    echo "<h2>🚚 Creating Suppliers...</h2>";
    $suppliers = [
        ['Emerald Jewel Industry', '<PERSON><PERSON>', '**********', 'raj<PERSON>@emerald.com', '123 Jewel Street, Mumbai', 'Mumbai', 'Maharashtra', '400001', '27ABCDE1234F1Z5', 'ABCDE1234F', 'HDFC Bank', '***********', 'HDFC0001234', 200000.00, 30],
        ['Nafa Gold', 'Amit Shah', '**********', '<EMAIL>', '456 Gold Plaza, Surat', 'Surat', 'Gujarat', '395001', '24**********2Z6', '**********', 'ICICI Bank', '***********', 'ICIC0002345', 150000.00, 15],
        ['Surat Jewellers', '<PERSON><PERSON>', '**********', '<EMAIL>', '789 Diamond Market, Surat', 'Surat', 'Gujarat', '395002', '24**********3Z7', '**********', 'SBI Bank', '***********', 'SBIN0003456', 300000.00, 45]
    ];
    
    foreach ($suppliers as $supplier) {
        $stmt = $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $supplier);
        echo "<p>✅ Added supplier: {$supplier[0]}</p>";
    }
    
    // 2. Create basic products
    echo "<h2>💎 Creating Products...</h2>";
    $products = [
        [1, 1, 'Coimbatore Chain', 'CC001', 'Traditional Coimbatore style gold chain', 'Gold', '22K', 10.500, 0.000, 10.500, 0, 950, 5800, '********'],
        [2, 2, 'Gold Bangles', 'BG001', 'Traditional gold bangles set', 'Gold', '22K', 12.340, 0.000, 12.340, 0, 850, 5800, '********'],
        [3, 3, 'Diamond Studs', 'ST001', 'Diamond studded gold earrings', 'Gold', '18K', 2.040, 0.500, 1.540, 1500, 600, 4650, '********']
    ];
    
    foreach ($products as $product) {
        $stmt = $db->query("INSERT INTO products (supplier_id, category_id, product_name, product_code, description, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $product);
        $productId = $db->lastInsertId();
        
        // Create inventory record
        $stock = rand(10, 50);
        $metal_value = $product[9] * $product[12]; // net_weight * gold_rate
        $total_cost = $metal_value + $product[10] + $product[11]; // metal + stone + making
        $selling_price = $total_cost * 1.25; // 25% markup
        
        $db->query("INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, minimum_stock_level, reorder_point, location, rack_number) VALUES (?, ?, ?, ?, 5, 10, 'A-01', ?)", [
            $productId, $stock, $total_cost, $selling_price, 'R-' . str_pad($productId, 3, '0', STR_PAD_LEFT)
        ]);
        
        echo "<p>✅ Added product: {$product[2]} - Stock: $stock</p>";
    }
    
    // 3. Create basic customers
    echo "<h2>👥 Creating Customers...</h2>";
    $customers = [
        ['VG Jewellery Store', 'VG Retail Chain', 'business', '9876543220', '<EMAIL>', '123 Main Street, Chennai', 'Chennai', 'Tamil Nadu', '600001', '33GHIJK7890L7Z1', 'GHIJK7890L', '', 500000.00, 45, 5.0],
        ['Krishna Jewels Retail', 'Krishna Retail Hub', 'business', '9876543221', '<EMAIL>', '456 Market Road, Bangalore', 'Bangalore', 'Karnataka', '560001', '29**********8Z2', '**********', '', 300000.00, 30, 3.0],
        ['Priya Sharma', '', 'individual', '9876543223', '<EMAIL>', '321 Residential Area, Mumbai', 'Mumbai', 'Maharashtra', '400001', '', '', '1***********', 0, 0, 0]
    ];
    
    foreach ($customers as $customer) {
        $stmt = $db->query("INSERT INTO customers (customer_name, business_name, customer_type, phone, email, address, city, state, pincode, gst_number, pan_number, aadhar_number, credit_limit, credit_days, discount_percentage, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $customer);
        echo "<p>✅ Added customer: {$customer[0]}</p>";
    }
    
    // 4. Create a sample sale
    echo "<h2>🛒 Creating Sample Sale...</h2>";
    $saleData = [
        'bill_number' => 'BILL-' . date('Ymd') . '-0001',
        'customer_id' => 1,
        'sale_date' => date('Y-m-d'),
        'sale_time' => date('H:i:s'),
        'subtotal' => 72000.00,
        'discount_amount' => 1440.00,
        'tax_amount' => 2116.80,
        'grand_total' => 72676.80
    ];
    
    $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, 'percentage', 2.00, ?, ?, ?, 'cash', 'paid', 1, 0)", [
        $saleData['bill_number'], $saleData['customer_id'], $saleData['sale_date'], $saleData['sale_time'], 
        $saleData['subtotal'], $saleData['discount_amount'], $saleData['tax_amount'], $saleData['grand_total']
    ]);
    
    $saleId = $db->lastInsertId();
    
    // Add sale item
    $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, 1, 1, 10.500, 10.500, 0, 950, 5800, 61850, 61850, 3.00, 1855.50, 63705.50)", [$saleId]);
    
    // Update inventory
    $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - 1 WHERE product_id = 1");
    
    // Record stock movement
    $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) VALUES (1, 'out', 1, 'sale', ?, 'Sale transaction', 1)", [$saleId]);
    
    echo "<p>✅ Added sale: {$saleData['bill_number']} - ₹" . number_format($saleData['grand_total'], 2) . "</p>";
    
    $db->commit();
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; color: #155724; margin: 30px 0;'>";
    echo "<h2>🎉 Simple Seed Data Created Successfully!</h2>";
    echo "<p>The system now has basic sample data:</p>";
    echo "<ul>";
    echo "<li>✅ 3 Suppliers</li>";
    echo "<li>✅ 3 Products with inventory</li>";
    echo "<li>✅ 3 Customers</li>";
    echo "<li>✅ 1 Sample sale transaction</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Test Your System Now:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $test_pages = [
        'index.php' => '📊 Dashboard',
        'products.php' => '💎 Products',
        'inventory.php' => '📦 Inventory', 
        'suppliers.php' => '🚚 Suppliers',
        'customers.php' => '👥 Customers',
        'sales.php' => '📊 Sales',
        'billing.php' => '🛒 New Sale',
        'metal-rates.php' => '💰 Metal Rates'
    ];
    
    foreach ($test_pages as $page => $title) {
        echo "<div style='background: #e9ecef; padding: 10px; border-radius: 5px; text-align: center;'>";
        echo "<a href='$page' target='_blank' style='text-decoration: none; color: #495057; font-weight: bold;'>$title</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Creating Seed Data</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
