<?php
/**
 * Export Suppliers Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Check if specific suppliers are selected
    $selectedSuppliers = $_POST['selected_suppliers'] ?? [];
    
    $whereClause = "s.is_active = 1";
    $params = [];
    
    if (!empty($selectedSuppliers)) {
        $placeholders = str_repeat('?,', count($selectedSuppliers) - 1) . '?';
        $whereClause .= " AND s.id IN ($placeholders)";
        $params = $selectedSuppliers;
    }

    // Get suppliers data with product count
    $suppliers = $db->fetchAll("
        SELECT s.*, 
               (SELECT COUNT(*) FROM products WHERE supplier_id = s.id AND is_active = 1) as product_count,
               (SELECT SUM(i.quantity_in_stock * i.cost_price) 
                FROM products p 
                JOIN inventory i ON p.id = i.product_id 
                WHERE p.supplier_id = s.id AND p.is_active = 1) as total_stock_value
        FROM suppliers s
        WHERE $whereClause
        ORDER BY s.supplier_name
    ", $params);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'suppliers_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Supplier ID', 'Supplier Name', 'Contact Person', 'Phone', 'Email',
            'Address', 'City', 'State', 'Pincode', 'GST Number', 'PAN Number',
            'Bank Name', 'Account Number', 'IFSC Code', 'Credit Limit (₹)', 'Credit Days',
            'Product Count', 'Total Stock Value (₹)', 'Notes', 'Created Date'
        ];

        fputcsv($output, $headers);

        foreach ($suppliers as $supplier) {
            $row = [
                $supplier['id'],
                $supplier['supplier_name'],
                $supplier['contact_person'],
                $supplier['phone'],
                $supplier['email'],
                $supplier['address'],
                $supplier['city'],
                $supplier['state'],
                $supplier['pincode'],
                $supplier['gst_number'],
                $supplier['pan_number'],
                $supplier['bank_name'],
                $supplier['account_number'],
                $supplier['ifsc_code'],
                $supplier['credit_limit'],
                $supplier['credit_days'],
                $supplier['product_count'],
                $supplier['total_stock_value'] ?? 0,
                $supplier['notes'],
                date('d/m/Y H:i:s', strtotime($supplier['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'suppliers_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Suppliers Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .contact-info { font-size: 11px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Suppliers Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Supplier</th>
                        <th>Contact</th>
                        <th>Location</th>
                        <th>GST Number</th>
                        <th>Products</th>
                        <th>Credit Limit</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($suppliers as $supplier) {
            echo "<tr>
                    <td>
                        <strong>" . htmlspecialchars($supplier['supplier_name']) . "</strong><br>
                        <small>" . htmlspecialchars($supplier['contact_person']) . "</small>
                    </td>
                    <td class='contact-info'>
                        " . htmlspecialchars($supplier['phone']) . "<br>
                        " . htmlspecialchars($supplier['email']) . "
                    </td>
                    <td>
                        " . htmlspecialchars($supplier['city']) . "<br>
                        " . htmlspecialchars($supplier['state']) . "
                    </td>
                    <td>" . htmlspecialchars($supplier['gst_number']) . "</td>
                    <td>" . $supplier['product_count'] . "</td>
                    <td>₹" . number_format($supplier['credit_limit'], 2) . "</td>
                  </tr>";
        }
        
        echo "</tbody>
            </table>
            
            <script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'suppliers_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Suppliers Report</title>
        </head>
        <body>
            <table border='1'>
                <tr>
                    <th>Supplier ID</th>
                    <th>Supplier Name</th>
                    <th>Contact Person</th>
                    <th>Phone</th>
                    <th>Email</th>
                    <th>City</th>
                    <th>State</th>
                    <th>GST Number</th>
                    <th>PAN Number</th>
                    <th>Credit Limit</th>
                    <th>Credit Days</th>
                    <th>Product Count</th>
                    <th>Total Stock Value</th>
                </tr>";

        foreach ($suppliers as $supplier) {
            echo "<tr>
                    <td>" . $supplier['id'] . "</td>
                    <td>" . htmlspecialchars($supplier['supplier_name']) . "</td>
                    <td>" . htmlspecialchars($supplier['contact_person']) . "</td>
                    <td>" . htmlspecialchars($supplier['phone']) . "</td>
                    <td>" . htmlspecialchars($supplier['email']) . "</td>
                    <td>" . htmlspecialchars($supplier['city']) . "</td>
                    <td>" . htmlspecialchars($supplier['state']) . "</td>
                    <td>" . htmlspecialchars($supplier['gst_number']) . "</td>
                    <td>" . htmlspecialchars($supplier['pan_number']) . "</td>
                    <td>" . $supplier['credit_limit'] . "</td>
                    <td>" . $supplier['credit_days'] . "</td>
                    <td>" . $supplier['product_count'] . "</td>
                    <td>" . ($supplier['total_stock_value'] ?? 0) . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting suppliers: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='suppliers.php'>← Back to Suppliers</a></p>";
    echo "</div>";
}
?>
