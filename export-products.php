<?php
/**
 * Export Products to CSV - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

try {
    // Get all products with related information
    $products = $db->fetchAll("
        SELECT p.*, c.category_name, s.supplier_name, i.quantity_in_stock, i.minimum_stock_level, i.cost_price, i.selling_price
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY p.product_name
    ");

    // Set headers for CSV download
    $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // Open output stream
    $output = fopen('php://output', 'w');

    // Add BOM for proper UTF-8 encoding in Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // CSV Headers
    $headers = [
        'Product ID',
        'Product Name',
        'Product Code',
        'Description',
        'Category',
        'Supplier',
        'Metal Type',
        'Purity',
        'Base Weight (gm)',
        'Stone Weight (gm)',
        'Net Weight (gm)',
        'Stone Cost (₹)',
        'Making Charges (₹)',
        'HSN Code',
        'Tax Rate (%)',
        'Current Stock',
        'Minimum Stock',
        'Cost Price (₹)',
        'Selling Price (₹)',
        'Stock Status',
        'Created Date'
    ];

    fputcsv($output, $headers);

    // Add product data
    foreach ($products as $product) {
        $stock = $product['quantity_in_stock'] ?? 0;
        $minStock = $product['minimum_stock_level'] ?? 0;
        $stockStatus = $stock <= $minStock && $minStock > 0 ? 'Low Stock' : 'In Stock';
        
        $row = [
            $product['id'],
            $product['product_name'],
            $product['product_code'],
            $product['description'],
            $product['category_name'] ?? 'Uncategorized',
            $product['supplier_name'] ?? 'No Supplier',
            $product['metal_type'],
            $product['purity'],
            $product['base_weight'],
            $product['stone_weight'],
            $product['net_weight'],
            $product['stone_cost'],
            $product['making_charges'],
            $product['hsn_code'],
            $product['tax_rate'],
            $stock,
            $minStock,
            $product['cost_price'] ?? 0,
            $product['selling_price'] ?? 0,
            $stockStatus,
            date('d/m/Y H:i:s', strtotime($product['created_at']))
        ];

        fputcsv($output, $row);
    }

    fclose($output);
    exit;

} catch (Exception $e) {
    // If there's an error, show it instead of downloading
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting products: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='products.php'>← Back to Products</a></p>";
    echo "</div>";
}
?>
