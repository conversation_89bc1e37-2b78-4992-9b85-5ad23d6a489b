<?php
/**
 * Export Metal Rates Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$dateTo = $_GET['date_to'] ?? date('Y-m-d'); // Today

try {
    // Get metal rates data for the date range
    $rates = $db->fetchAll("
        SELECT mr.*, u.username as created_by_name
        FROM metal_rates mr
        LEFT JOIN users u ON mr.created_by = u.id
        WHERE mr.rate_date BETWEEN ? AND ? AND mr.is_active = 1
        ORDER BY mr.rate_date DESC, mr.metal_type, mr.purity
    ", [$dateFrom, $dateTo]);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'metal_rates_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Date', 'Metal Type', 'Purity', 'Rate per Gram (₹)', 'Created By', 'Created At'
        ];

        fputcsv($output, $headers);

        foreach ($rates as $rate) {
            $row = [
                date('d/m/Y', strtotime($rate['rate_date'])),
                $rate['metal_type'],
                $rate['purity'],
                $rate['rate_per_gram'],
                $rate['created_by_name'] ?? 'System',
                date('d/m/Y H:i:s', strtotime($rate['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'metal_rates_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // Group rates by date
        $ratesByDate = [];
        foreach ($rates as $rate) {
            $ratesByDate[$rate['rate_date']][] = $rate;
        }
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Metal Rates Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .date-header { background-color: #e9ecef; font-weight: bold; }
                .gold { color: #ffd700; font-weight: bold; }
                .silver { color: #c0c0c0; font-weight: bold; }
                .platinum { color: #e5e4e2; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Metal Rates Report</h1>
                <p>Period: " . date('d/m/Y', strtotime($dateFrom)) . " to " . date('d/m/Y', strtotime($dateTo)) . "</p>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>";
        
        foreach ($ratesByDate as $date => $dayRates) {
            echo "<h3 class='date-header'>Rates for " . date('d/m/Y', strtotime($date)) . "</h3>";
            echo "<table>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>Metal Type</th>";
            echo "<th>Purity</th>";
            echo "<th>Rate per Gram (₹)</th>";
            echo "<th>Updated By</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($dayRates as $rate) {
                $metalClass = strtolower($rate['metal_type']);
                echo "<tr>";
                echo "<td class='$metalClass'>" . htmlspecialchars($rate['metal_type']) . "</td>";
                echo "<td>" . htmlspecialchars($rate['purity']) . "</td>";
                echo "<td>₹" . number_format($rate['rate_per_gram'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($rate['created_by_name'] ?? 'System') . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "</table>";
        }
        
        echo "<script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'metal_rates_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Metal Rates Report</title>
        </head>
        <body>
            <h1>Metal Rates Report</h1>
            <p>Period: " . date('d/m/Y', strtotime($dateFrom)) . " to " . date('d/m/Y', strtotime($dateTo)) . "</p>
            
            <table border='1'>
                <tr>
                    <th>Date</th>
                    <th>Metal Type</th>
                    <th>Purity</th>
                    <th>Rate per Gram</th>
                    <th>Created By</th>
                    <th>Created At</th>
                </tr>";

        foreach ($rates as $rate) {
            echo "<tr>
                    <td>" . date('d/m/Y', strtotime($rate['rate_date'])) . "</td>
                    <td>" . htmlspecialchars($rate['metal_type']) . "</td>
                    <td>" . htmlspecialchars($rate['purity']) . "</td>
                    <td>" . $rate['rate_per_gram'] . "</td>
                    <td>" . htmlspecialchars($rate['created_by_name'] ?? 'System') . "</td>
                    <td>" . date('d/m/Y H:i:s', strtotime($rate['created_at'])) . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting metal rates: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='metal-rates.php'>← Back to Metal Rates</a></p>";
    echo "</div>";
}
?>
