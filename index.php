<?php
/**
 * Dashboard - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';
require_once 'includes/notifications.php';

// Start session and check authentication
startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Generate system notifications
generateSystemNotifications();

// Get notifications for display
$notifications = getUnreadNotifications(null, 5);
$notificationCount = getUnreadNotificationCount();

// Get dashboard statistics
try {
    // Total products
    $stmt = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
    $total_products = $stmt->fetch()['count'] ?? 0;
    
    // Total customers
    $stmt = $db->query("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
    $total_customers = $stmt->fetch()['count'] ?? 0;
    
    // Low stock items
    $stmt = $db->query("
        SELECT COUNT(*) as count 
        FROM inventory i 
        JOIN products p ON i.product_id = p.id 
        WHERE i.quantity_in_stock <= i.minimum_stock_level 
        AND i.minimum_stock_level > 0 
        AND p.is_active = 1
    ");
    $low_stock_items = $stmt->fetch()['count'] ?? 0;
    
    // Today's sales
    $stmt = $db->query("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE DATE(sale_date) = CURDATE() 
        AND is_cancelled = 0
    ");
    $today_sales = $stmt->fetch()['total'] ?? 0;
    
    // This month's sales
    $stmt = $db->query("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE YEAR(sale_date) = YEAR(CURDATE()) 
        AND MONTH(sale_date) = MONTH(CURDATE()) 
        AND is_cancelled = 0
    ");
    $month_sales = $stmt->fetch()['total'] ?? 0;
    
    // Recent sales
    $stmt = $db->query("
        SELECT s.id, s.bill_number, s.grand_total, s.created_at, 
               c.customer_name, s.payment_status
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        WHERE s.is_cancelled = 0
        ORDER BY s.created_at DESC 
        LIMIT 5
    ");
    $recent_sales = $stmt->fetchAll();
    
    // Low stock products
    $stmt = $db->query("
        SELECT p.product_name, p.product_code, i.quantity_in_stock, 
               i.minimum_stock_level, c.category_name
        FROM products p
        JOIN inventory i ON p.id = i.product_id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE i.quantity_in_stock <= i.minimum_stock_level 
        AND i.minimum_stock_level > 0 
        AND p.is_active = 1
        ORDER BY (i.quantity_in_stock / NULLIF(i.minimum_stock_level, 0)) ASC
        LIMIT 5
    ");
    $low_stock_products = $stmt->fetchAll();
    
    // Top selling products this month
    $stmt = $db->query("
        SELECT p.product_name, p.product_code, 
               SUM(si.quantity) as total_sold,
               SUM(si.total_price) as total_revenue
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        JOIN sales s ON si.sale_id = s.id
        WHERE YEAR(s.sale_date) = YEAR(CURDATE()) 
        AND MONTH(s.sale_date) = MONTH(CURDATE())
        AND s.is_cancelled = 0
        GROUP BY p.id, p.product_name, p.product_code
        ORDER BY total_sold DESC
        LIMIT 5
    ");
    $top_products = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("Dashboard data fetch error: " . $e->getMessage());
    $total_products = $total_customers = $low_stock_items = 0;
    $today_sales = $month_sales = 0;
    $recent_sales = $low_stock_products = $top_products = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <!-- Welcome Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">Welcome back!</h2>
                <p class="text-muted mb-0">Here's what's happening with your jewellery business today.</p>
            </div>
            <div class="text-muted">
                <i class="fas fa-clock me-2"></i>
                Last updated: <?php echo formatDateTime(date('Y-m-d H:i:s')); ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card border-left-primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-title">Total Products</div>
                            <div class="card-value"><?php echo number_format($total_products); ?></div>
                        </div>
                        <div class="card-icon text-primary">
                            <i class="fas fa-gem"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card border-left-success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-title">Total Customers</div>
                            <div class="card-value"><?php echo number_format($total_customers); ?></div>
                        </div>
                        <div class="card-icon text-success">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card border-left-warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-title">Low Stock Items</div>
                            <div class="card-value"><?php echo number_format($low_stock_items); ?></div>
                        </div>
                        <div class="card-icon text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card border-left-info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="card-title">Today's Sales</div>
                            <div class="card-value"><?php echo formatCurrency($today_sales); ?></div>
                        </div>
                        <div class="card-icon text-info">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Section -->
        <?php if (!empty($notifications)): ?>
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>Notifications
                    <?php if ($notificationCount > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $notificationCount; ?></span>
                    <?php endif; ?>
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="markAllAsRead()">
                    <i class="fas fa-check-double me-1"></i>Mark All Read
                </button>
            </div>
            <div class="card-body p-0">
                <div class="notifications-container">
                    <?php echo displayNotifications($notifications); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="billing.php" class="btn btn-primary btn-lg w-100 d-flex flex-column align-items-center py-4">
                            <i class="fas fa-file-invoice fa-2x mb-3"></i>
                            <span>Create New Sale</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="products.php?action=add" class="btn btn-success btn-lg w-100 d-flex flex-column align-items-center py-4">
                            <i class="fas fa-gem fa-2x mb-3"></i>
                            <span>Add Product</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="customers.php?action=add" class="btn btn-info btn-lg w-100 d-flex flex-column align-items-center py-4">
                            <i class="fas fa-user-plus fa-2x mb-3"></i>
                            <span>Add Customer</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="inventory.php" class="btn btn-warning btn-lg w-100 d-flex flex-column align-items-center py-4">
                            <i class="fas fa-boxes fa-2x mb-3"></i>
                            <span>Manage Stock</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Recent Activity -->
        <div class="row">
            <!-- Sales Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Sales Overview</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="chartPeriod" id="chart7days" checked>
                            <label class="btn btn-outline-primary" for="chart7days">7 Days</label>
                            
                            <input type="radio" class="btn-check" name="chartPeriod" id="chart30days">
                            <label class="btn btn-outline-primary" for="chart30days">30 Days</label>
                            
                            <input type="radio" class="btn-check" name="chartPeriod" id="chart12months">
                            <label class="btn btn-outline-primary" for="chart12months">12 Months</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Sales -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Sales</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($recent_sales)): ?>
                            <div class="text-center py-4 text-muted">
                                <i class="fas fa-file-invoice fa-3x mb-3 opacity-25"></i>
                                <p>No recent sales found</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_sales as $sale): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Bill #<?php echo htmlspecialchars($sale['bill_number']); ?></h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($sale['customer_name'] ?? 'Walk-in Customer'); ?></p>
                                            <small class="text-muted"><?php echo formatDateTime($sale['created_at']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold"><?php echo formatCurrency($sale['grand_total']); ?></div>
                                            <span class="badge bg-<?php echo $sale['payment_status'] === 'paid' ? 'success' : ($sale['payment_status'] === 'partial' ? 'warning' : 'danger'); ?>">
                                                <?php echo ucfirst($sale['payment_status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="card-footer text-center">
                                <a href="sales.php" class="btn btn-outline-primary btn-sm">View All Sales</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock and Top Products -->
        <div class="row">
            <!-- Low Stock Alerts -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Low Stock Alerts</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($low_stock_products)): ?>
                            <div class="text-center py-4 text-muted">
                                <i class="fas fa-check-circle fa-3x mb-3 text-success opacity-25"></i>
                                <p>All products are well stocked!</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($low_stock_products as $product): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($product['product_code']); ?></p>
                                            <small class="text-muted"><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-warning">
                                                <?php echo $product['quantity_in_stock']; ?> / <?php echo $product['minimum_stock_level']; ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="card-footer text-center">
                                <a href="inventory.php?filter=low_stock" class="btn btn-outline-warning btn-sm">Manage Stock</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Top Products -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2 text-warning"></i>Top Selling Products</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($top_products)): ?>
                            <div class="text-center py-4 text-muted">
                                <i class="fas fa-chart-bar fa-3x mb-3 opacity-25"></i>
                                <p>No sales data available</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($top_products as $index => $product): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary me-3"><?php echo $index + 1; ?></span>
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                                <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold"><?php echo number_format($product['total_sold']); ?> sold</div>
                                            <small class="text-muted"><?php echo formatCurrency($product['total_revenue']); ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Initialize sales chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            
            // Sample data - replace with actual data from PHP
            const salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Sales (₹)',
                        data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString('en-IN');
                                }
                            }
                        }
                    }
                }
            });
        });

        // Notification functions
        function markAllAsRead() {
            fetch('ajax/mark_notifications_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({action: 'mark_all_read'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to mark notifications as read');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }
    </script>

    <style>
        .notifications-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            transition: background-color 0.2s ease;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-icon {
            width: 40px;
            text-align: center;
        }

        .notification-title {
            font-size: 0.95rem;
            font-weight: 600;
        }

        .notification-message {
            font-size: 0.85rem;
            line-height: 1.4;
        }
    </style>
</body>
</html>
