<?php
/**
 * Customer Details Modal/Popup - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();

$db = getDB();
$customer_id = $_GET['id'] ?? null;

if (!$customer_id) {
    die('Customer ID required');
}

// Get customer details with comprehensive information
try {
    $customer = $db->fetch("
        SELECT c.*, 
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT MIN(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as first_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0) 
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE c.id = ? AND c.is_active = 1
    ", [$customer_id]);

    if (!$customer) {
        die('Customer not found');
    }

    // Get recent sales
    $recentSales = $db->fetchAll("
        SELECT s.*, si.quantity, si.total_price, p.product_name
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        LEFT JOIN products p ON si.product_id = p.id
        WHERE s.customer_id = ? AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC, s.sale_time DESC
        LIMIT 10
    ", [$customer_id]);

    // Get payment history
    $paymentHistory = $db->fetchAll("
        SELECT p.*, s.bill_number
        FROM payments p
        JOIN sales s ON p.sale_id = s.id
        WHERE s.customer_id = ?
        ORDER BY p.payment_date DESC
        LIMIT 10
    ", [$customer_id]);

    // Get customer analytics
    $monthlyPurchases = $db->fetchAll("
        SELECT 
            DATE_FORMAT(sale_date, '%Y-%m') as month,
            COUNT(*) as order_count,
            SUM(grand_total) as total_amount
        FROM sales 
        WHERE customer_id = ? AND is_cancelled = 0 
        AND sale_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(sale_date, '%Y-%m')
        ORDER BY month DESC
    ", [$customer_id]);

} catch (Exception $e) {
    die('Error loading customer details: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Details - <?php echo htmlspecialchars($customer['customer_name']); ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
        .detail-card { border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .detail-label { font-weight: 600; color: #495057; }
        .detail-value { color: #212529; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }
        .payment-item { border-left: 3px solid #28a745; padding-left: 15px; margin-bottom: 15px; }
        .outstanding-item { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="mb-1"><?php echo htmlspecialchars($customer['customer_name']); ?></h3>
                <p class="text-muted mb-0">
                    <?php echo ucfirst($customer['customer_type']); ?> Customer
                    <?php if ($customer['business_name']): ?>
                        - <?php echo htmlspecialchars($customer['business_name']); ?>
                    <?php endif; ?>
                </p>
            </div>
            <button class="btn btn-outline-secondary" onclick="window.close()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h4><?php echo $customer['sales_count']; ?></h4>
                        <p class="mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                        <h4><?php echo formatCurrency($customer['total_purchases']); ?></h4>
                        <p class="mb-0">Total Purchases</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><?php echo formatCurrency($customer['outstanding_amount']); ?></h4>
                        <p class="mb-0">Outstanding</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-credit-card fa-2x mb-2"></i>
                        <h4><?php echo formatCurrency($customer['credit_limit']); ?></h4>
                        <p class="mb-0">Credit Limit</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Customer Information -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Customer Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Type:</div>
                            <div class="col-sm-8 detail-value">
                                <span class="badge bg-<?php echo $customer['customer_type'] === 'business' ? 'primary' : 'secondary'; ?>">
                                    <?php echo ucfirst($customer['customer_type']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Phone:</div>
                            <div class="col-sm-8 detail-value">
                                <a href="tel:<?php echo $customer['phone']; ?>"><?php echo htmlspecialchars($customer['phone']); ?></a>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Email:</div>
                            <div class="col-sm-8 detail-value">
                                <?php if ($customer['email']): ?>
                                    <a href="mailto:<?php echo $customer['email']; ?>"><?php echo htmlspecialchars($customer['email']); ?></a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Address:</div>
                            <div class="col-sm-8 detail-value">
                                <?php echo nl2br(htmlspecialchars($customer['address'])); ?><br>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($customer['city']); ?>, 
                                    <?php echo htmlspecialchars($customer['state']); ?> - 
                                    <?php echo htmlspecialchars($customer['pincode']); ?>
                                </small>
                            </div>
                        </div>

                        <?php if ($customer['customer_type'] === 'business'): ?>
                            <div class="row mb-3">
                                <div class="col-sm-4 detail-label">GST Number:</div>
                                <div class="col-sm-8 detail-value"><?php echo htmlspecialchars($customer['gst_number']); ?></div>
                            </div>
                        <?php endif; ?>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Credit Terms:</div>
                            <div class="col-sm-8 detail-value">
                                <?php if ($customer['credit_limit'] > 0): ?>
                                    Limit: <?php echo formatCurrency($customer['credit_limit']); ?><br>
                                    Days: <?php echo $customer['credit_days']; ?> days<br>
                                    Discount: <?php echo $customer['discount_percentage']; ?>%
                                <?php else: ?>
                                    <span class="text-muted">No credit facility</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Customer Since:</div>
                            <div class="col-sm-8 detail-value">
                                <?php echo formatDate($customer['created_at']); ?>
                                <?php if ($customer['first_purchase_date']): ?>
                                    <br><small class="text-muted">First Purchase: <?php echo formatDate($customer['first_purchase_date']); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Sales -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shopping-bag me-2"></i>Recent Sales</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($recentSales)): ?>
                            <p class="text-muted text-center py-3">No sales found</p>
                        <?php else: ?>
                            <?php foreach ($recentSales as $sale): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                                    <div>
                                        <strong><?php echo htmlspecialchars($sale['bill_number']); ?></strong><br>
                                        <small class="text-muted">
                                            <?php echo formatDate($sale['sale_date']); ?> - 
                                            <?php echo htmlspecialchars($sale['product_name'] ?? 'Multiple items'); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div><?php echo formatCurrency($sale['grand_total']); ?></div>
                                        <small class="badge bg-<?php echo $sale['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($sale['payment_status']); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <a href="customers.php?action=edit&id=<?php echo $customer['id']; ?>" class="btn btn-primary" target="_blank">
                <i class="fas fa-edit me-2"></i>Edit Customer
            </a>
            <a href="billing.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-success" target="_blank">
                <i class="fas fa-plus me-2"></i>New Sale
            </a>
            <?php if ($customer['outstanding_amount'] > 0): ?>
                <a href="customer-payment-history.php?id=<?php echo $customer['id']; ?>" class="btn btn-warning" target="_blank">
                    <i class="fas fa-credit-card me-2"></i>Payment History
                </a>
            <?php endif; ?>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Print Details
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
