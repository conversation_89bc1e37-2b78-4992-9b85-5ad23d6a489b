<?php
/**
 * Customers Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$customer_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'customer_name' => sanitizeInput($_POST['customer_name']),
                'business_name' => sanitizeInput($_POST['business_name']),
                'customer_type' => $_POST['customer_type'],
                'phone' => sanitizeInput($_POST['phone']),
                'email' => sanitizeInput($_POST['email']),
                'address' => sanitizeInput($_POST['address']),
                'city' => sanitizeInput($_POST['city']),
                'state' => sanitizeInput($_POST['state']),
                'pincode' => sanitizeInput($_POST['pincode']),
                'gst_number' => sanitizeInput($_POST['gst_number']),
                'pan_number' => sanitizeInput($_POST['pan_number']),
                'aadhar_number' => sanitizeInput($_POST['aadhar_number']),
                'credit_limit' => floatval($_POST['credit_limit']),
                'credit_days' => intval($_POST['credit_days']),
                'discount_percentage' => floatval($_POST['discount_percentage']),
                'notes' => sanitizeInput($_POST['notes'])
            ];
            
            // Validate GST number if provided
            if ($data['gst_number'] && !validateGST($data['gst_number'])) {
                throw new Exception("Invalid GST number format");
            }
            
            // Validate email if provided
            if ($data['email'] && !validateEmail($data['email'])) {
                throw new Exception("Invalid email format");
            }
            
            // Validate phone if provided
            if ($data['phone'] && !validatePhone($data['phone'])) {
                throw new Exception("Invalid phone number format");
            }
            
            if ($action === 'add') {
                $sql = "INSERT INTO customers (customer_name, business_name, customer_type, phone, email, address, city, state, pincode, gst_number, pan_number, aadhar_number, credit_limit, credit_days, discount_percentage, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "Customer added successfully!";
            } else {
                $sql = "UPDATE customers SET customer_name=?, business_name=?, customer_type=?, phone=?, email=?, address=?, city=?, state=?, pincode=?, gst_number=?, pan_number=?, aadhar_number=?, credit_limit=?, credit_days=?, discount_percentage=?, notes=? WHERE id=?";
                $params = array_values($data);
                $params[] = $customer_id;
                $db->query($sql, $params);
                $success = "Customer updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $customer_id) {
            // Check if customer has sales
            $sales_count = $db->fetch("SELECT COUNT(*) as count FROM sales WHERE customer_id = ?", [$customer_id])['count'];
            
            if ($sales_count > 0) {
                $error = "Cannot delete customer. They have $sales_count sales records.";
            } else {
                $db->query("UPDATE customers SET is_active = 0 WHERE id = ?", [$customer_id]);
                $success = "Customer deleted successfully!";
            }
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get customers list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $type_filter = $_GET['type'] ?? '';
    $city_filter = $_GET['city'] ?? '';
    
    $where_conditions = ["is_active = 1"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(customer_name LIKE ? OR business_name LIKE ? OR phone LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($type_filter) {
        $where_conditions[] = "customer_type = ?";
        $params[] = $type_filter;
    }
    
    if ($city_filter) {
        $where_conditions[] = "city = ?";
        $params[] = $city_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $customers = $db->fetchAll("
        SELECT c.*, 
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases
        FROM customers c
        WHERE $where_clause
        ORDER BY c.customer_name
    ", $params);
    
    // Get cities and types for filters
    $cities = $db->fetchAll("SELECT DISTINCT city FROM customers WHERE is_active = 1 AND city IS NOT NULL AND city != '' ORDER BY city");
}

// Get single customer for edit
if ($action === 'edit' && $customer_id) {
    $customer = $db->fetch("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$customer_id]);
    if (!$customer) {
        $error = "Customer not found!";
        $action = 'list';
    }
}

// Get customer details for view
if ($action === 'view' && $customer_id) {
    $customer = $db->fetch("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$customer_id]);
    if (!$customer) {
        $error = "Customer not found!";
        $action = 'list';
    } else {
        // Get customer's sales history
        $customer_sales = $db->fetchAll("
            SELECT s.*, 
                   (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count
            FROM sales s 
            WHERE s.customer_id = ? AND s.is_cancelled = 0 
            ORDER BY s.created_at DESC 
            LIMIT 10
        ", [$customer_id]);
        
        // Get customer statistics
        $customer_stats = $db->fetch("
            SELECT 
                COUNT(*) as total_orders,
                COALESCE(SUM(grand_total), 0) as total_spent,
                COALESCE(AVG(grand_total), 0) as avg_order_value,
                MAX(created_at) as last_order_date
            FROM sales 
            WHERE customer_id = ? AND is_cancelled = 0
        ", [$customer_id]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Customers List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Customers</h2>
                    <p class="text-muted mb-0">Manage your customer database</p>
                </div>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Customer
                </a>
            </div>

            <!-- Customer Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Customers</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count($customers); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Business Customers</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $business_count = count(array_filter($customers, function($c) { return $c['customer_type'] === 'business'; }));
                                        echo $business_count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-building fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Individual Customers</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $individual_count = count(array_filter($customers, function($c) { return $c['customer_type'] === 'individual'; }));
                                        echo $individual_count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">With Credit Limit</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $credit_customers = count(array_filter($customers, function($c) { return $c['credit_limit'] > 0; }));
                                        echo $credit_customers;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-credit-card fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by name, business, or phone...">
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="business" <?php echo $type_filter === 'business' ? 'selected' : ''; ?>>Business</option>
                                <option value="individual" <?php echo $type_filter === 'individual' ? 'selected' : ''; ?>>Individual</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="city" class="form-label">City</label>
                            <select class="form-select" id="city" name="city">
                                <option value="">All Cities</option>
                                <?php foreach ($cities as $city): ?>
                                    <option value="<?php echo htmlspecialchars($city['city']); ?>" 
                                            <?php echo $city_filter === $city['city'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($city['city']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="customers.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Contact</th>
                                    <th>Location</th>
                                    <th>Sales</th>
                                    <th>Total Purchases</th>
                                    <th>Credit Limit</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($customers)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4 text-muted">
                                            <i class="fas fa-users fa-3x mb-3 opacity-25"></i>
                                            <p>No customers found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($customers as $customer): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($customer['customer_name']); ?></h6>
                                                    <?php if ($customer['business_name']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars($customer['business_name']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $customer['customer_type'] === 'business' ? 'primary' : 'info'; ?>">
                                                    <?php echo ucfirst($customer['customer_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($customer['phone']): ?>
                                                    <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($customer['phone']); ?></div>
                                                <?php endif; ?>
                                                <?php if ($customer['email']): ?>
                                                    <div><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($customer['email']); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($customer['city']): ?>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($customer['city']); ?>
                                                        <?php if ($customer['state']): ?>
                                                            , <?php echo htmlspecialchars($customer['state']); ?>
                                                        <?php endif; ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo $customer['sales_count']; ?> orders
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo formatCurrency($customer['total_purchases']); ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($customer['credit_limit'] > 0): ?>
                                                    <?php echo formatCurrency($customer['credit_limit']); ?>
                                                    <?php if ($customer['credit_days'] > 0): ?>
                                                        <br><small class="text-muted"><?php echo $customer['credit_days']; ?> days</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No credit</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=view&id=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="?action=edit&id=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="billing.php?customer_id=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-outline-success" title="New Sale">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                    <?php if ($customer['sales_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteCustomer(<?php echo $customer['id']; ?>)" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Customer Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Customer' : 'Edit Customer'; ?></h2>
                    <p class="text-muted mb-0">Enter customer details below</p>
                </div>
                <a href="customers.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Customers
                </a>
            </div>

            <form method="POST">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_name" class="form-label">Customer Name *</label>
                                        <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                               value="<?php echo htmlspecialchars($customer['customer_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_type" class="form-label">Customer Type *</label>
                                        <select class="form-select" id="customer_type" name="customer_type" required>
                                            <option value="">Select Type</option>
                                            <option value="individual" <?php echo ($customer['customer_type'] ?? '') === 'individual' ? 'selected' : ''; ?>>Individual</option>
                                            <option value="business" <?php echo ($customer['customer_type'] ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3" id="businessNameField" style="display: none;">
                                    <label for="business_name" class="form-label">Business Name</label>
                                    <input type="text" class="form-control" id="business_name" name="business_name" 
                                           value="<?php echo htmlspecialchars($customer['business_name'] ?? ''); ?>">
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($customer['phone'] ?? ''); ?>" 
                                               pattern="[6-9][0-9]{9}" placeholder="10-digit mobile number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($customer['email'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($customer['address'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="<?php echo htmlspecialchars($customer['city'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="state" class="form-label">State</label>
                                        <input type="text" class="form-control" id="state" name="state" 
                                               value="<?php echo htmlspecialchars($customer['state'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="pincode" class="form-label">Pincode</label>
                                        <input type="text" class="form-control" id="pincode" name="pincode" 
                                               value="<?php echo htmlspecialchars($customer['pincode'] ?? ''); ?>" 
                                               pattern="[0-9]{6}" maxlength="6">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Business Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gst_number" class="form-label">GST Number</label>
                                        <input type="text" class="form-control" id="gst_number" name="gst_number" 
                                               value="<?php echo htmlspecialchars($customer['gst_number'] ?? ''); ?>" 
                                               pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}" 
                                               maxlength="15" placeholder="22**********1Z5">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="pan_number" class="form-label">PAN Number</label>
                                        <input type="text" class="form-control" id="pan_number" name="pan_number" 
                                               value="<?php echo htmlspecialchars($customer['pan_number'] ?? ''); ?>" 
                                               pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}" maxlength="10" placeholder="**********">
                                    </div>
                                </div>

                                <div class="mb-3" id="aadharField">
                                    <label for="aadhar_number" class="form-label">Aadhar Number</label>
                                    <input type="text" class="form-control" id="aadhar_number" name="aadhar_number" 
                                           value="<?php echo htmlspecialchars($customer['aadhar_number'] ?? ''); ?>" 
                                           pattern="[0-9]{12}" maxlength="12" placeholder="12-digit Aadhar number">
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="credit_limit" class="form-label">Credit Limit (₹)</label>
                                        <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                               value="<?php echo $customer['credit_limit'] ?? '0'; ?>" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="credit_days" class="form-label">Credit Days</label>
                                        <input type="number" class="form-control" id="credit_days" name="credit_days" 
                                               value="<?php echo $customer['credit_days'] ?? '0'; ?>" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="discount_percentage" class="form-label">Discount (%)</label>
                                        <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" 
                                               value="<?php echo $customer['discount_percentage'] ?? '0'; ?>" step="0.01" min="0" max="100">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($customer['notes'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="customers.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Add Customer' : 'Update Customer'; ?>
                            </button>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Customer Types</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-user me-2"></i>Individual</h6>
                                    <p class="mb-0 small">Personal customers buying for individual use. Aadhar number required for identification.</p>
                                </div>
                                
                                <div class="alert alert-primary">
                                    <h6><i class="fas fa-building me-2"></i>Business</h6>
                                    <p class="mb-0 small">Business customers for wholesale purchases. GST and PAN details required for tax compliance.</p>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Credit Terms</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-credit-card me-2"></i>Credit Management</h6>
                                    <ul class="mb-0 small">
                                        <li>Set credit limit for trusted customers</li>
                                        <li>Define payment terms in days</li>
                                        <li>Monitor outstanding balances</li>
                                        <li>Apply customer-specific discounts</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        <?php elseif ($action === 'view'): ?>
            <!-- View Customer Details -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo htmlspecialchars($customer['customer_name']); ?></h2>
                    <p class="text-muted mb-0">
                        <?php echo ucfirst($customer['customer_type']); ?> Customer
                        <?php if ($customer['business_name']): ?>
                            - <?php echo htmlspecialchars($customer['business_name']); ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <a href="billing.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-success me-2">
                        <i class="fas fa-plus me-2"></i>New Sale
                    </a>
                    <a href="?action=edit&id=<?php echo $customer['id']; ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>Edit Customer
                    </a>
                    <a href="customers.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Customers
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Customer Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-primary"><?php echo $customer_stats['total_orders']; ?></h3>
                                    <small class="text-muted">Total Orders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-success"><?php echo formatCurrency($customer_stats['total_spent']); ?></h3>
                                    <small class="text-muted">Total Spent</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-info"><?php echo formatCurrency($customer_stats['avg_order_value']); ?></h3>
                                    <small class="text-muted">Avg Order Value</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-warning">
                                        <?php echo $customer_stats['last_order_date'] ? formatDate($customer_stats['last_order_date']) : '-'; ?>
                                    </h3>
                                    <small class="text-muted">Last Order</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <?php if (!empty($customer_sales)): ?>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Recent Orders</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Bill Number</th>
                                                <th>Date</th>
                                                <th>Items</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($customer_sales as $sale): ?>
                                                <tr>
                                                    <td><code><?php echo htmlspecialchars($sale['bill_number']); ?></code></td>
                                                    <td><?php echo formatDate($sale['sale_date']); ?></td>
                                                    <td><?php echo $sale['item_count']; ?> items</td>
                                                    <td><strong><?php echo formatCurrency($sale['grand_total']); ?></strong></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $sale['payment_status'] === 'paid' ? 'success' : ($sale['payment_status'] === 'partial' ? 'warning' : 'danger'); ?>">
                                                            <?php echo ucfirst($sale['payment_status']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="sales.php?action=view&id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Contact Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['phone'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['email'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Address:</strong></td>
                                    <td><?php echo nl2br(htmlspecialchars($customer['address'] ?: '-')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>City:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['city'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>State:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['state'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Pincode:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['pincode'] ?: '-'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Business Details</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>GST Number:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['gst_number'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>PAN Number:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['pan_number'] ?: '-'); ?></td>
                                </tr>
                                <?php if ($customer['customer_type'] === 'individual'): ?>
                                <tr>
                                    <td><strong>Aadhar Number:</strong></td>
                                    <td><?php echo htmlspecialchars($customer['aadhar_number'] ?: '-'); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong>Credit Limit:</strong></td>
                                    <td><?php echo $customer['credit_limit'] > 0 ? formatCurrency($customer['credit_limit']) : '-'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Credit Days:</strong></td>
                                    <td><?php echo $customer['credit_days'] > 0 ? $customer['credit_days'] . ' days' : '-'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Discount:</strong></td>
                                    <td><?php echo $customer['discount_percentage'] > 0 ? $customer['discount_percentage'] . '%' : '-'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($customer['notes']): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Notes</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($customer['notes'])); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteCustomer(id) {
            if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }

        // Show/hide business name field based on customer type
        document.getElementById('customer_type')?.addEventListener('change', function() {
            const businessNameField = document.getElementById('businessNameField');
            const aadharField = document.getElementById('aadharField');
            
            if (this.value === 'business') {
                businessNameField.style.display = 'block';
                aadharField.style.display = 'none';
            } else if (this.value === 'individual') {
                businessNameField.style.display = 'none';
                aadharField.style.display = 'block';
            } else {
                businessNameField.style.display = 'none';
                aadharField.style.display = 'none';
            }
        });

        // Initialize form on page load
        document.addEventListener('DOMContentLoaded', function() {
            const customerType = document.getElementById('customer_type');
            if (customerType && customerType.value) {
                customerType.dispatchEvent(new Event('change'));
            }
        });

        // Format input fields
        document.getElementById('gst_number')?.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });

        document.getElementById('pan_number')?.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
