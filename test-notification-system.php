<?php
/**
 * Test Notification System
 * Tests automatic notification generation based on business conditions
 */

require_once 'config/database.php';
require_once 'includes/notifications.php';

echo "<html><head><title>Notification System Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
h1, h2 { color: #333; }
</style></head><body>";

echo "<h1>🔔 Notification System Test</h1>";

try {
    $db = getDB();
    
    echo "<div class='test-result info'>";
    echo "<h2>📊 Current System Status</h2>";
    
    // Check current business conditions
    $lowStockCount = $db->fetch("
        SELECT COUNT(*) as count 
        FROM products p
        JOIN inventory i ON p.id = i.product_id
        WHERE i.quantity_in_stock <= i.minimum_stock_level 
        AND i.minimum_stock_level > 0 
        AND p.is_active = 1
    ")['count'];
    
    $todaySales = $db->fetch("
        SELECT COALESCE(SUM(grand_total), 0) as total 
        FROM sales 
        WHERE DATE(sale_date) = CURDATE() AND is_cancelled = 0
    ")['total'];
    
    $pendingSales = $db->fetch("
        SELECT COUNT(*) as count, COALESCE(SUM(grand_total), 0) as total
        FROM sales 
        WHERE payment_status = 'pending' AND is_cancelled = 0
    ");
    
    $stagnantProducts = $db->fetch("
        SELECT COUNT(*) as count
        FROM products p
        LEFT JOIN sale_items si ON p.id = si.product_id
        LEFT JOIN sales s ON si.sale_id = s.id AND s.sale_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        WHERE p.is_active = 1 AND s.id IS NULL
    ")['count'];
    
    echo "<ul>";
    echo "<li><strong>Low Stock Items:</strong> $lowStockCount</li>";
    echo "<li><strong>Today's Sales:</strong> " . formatCurrency($todaySales) . "</li>";
    echo "<li><strong>Pending Sales:</strong> {$pendingSales['count']} (Total: " . formatCurrency($pendingSales['total']) . ")</li>";
    echo "<li><strong>Stagnant Products:</strong> $stagnantProducts (no sales in 30 days)</li>";
    echo "</ul>";
    echo "</div>";
    
    // Clear existing notifications for clean test
    echo "<div class='test-result info'>";
    echo "<h2>🧹 Clearing Existing Notifications</h2>";
    $db->query("DELETE FROM notifications");
    echo "<p>✅ All existing notifications cleared</p>";
    echo "</div>";
    
    // Test automatic notification generation
    echo "<div class='test-result info'>";
    echo "<h2>🤖 Testing Automatic Notification Generation</h2>";
    
    generateSystemNotifications();
    
    $newNotifications = $db->fetchAll("SELECT * FROM notifications ORDER BY created_at DESC");
    
    if (empty($newNotifications)) {
        echo "<p>ℹ️ No notifications were generated based on current business conditions</p>";
        echo "<p>This means:</p>";
        echo "<ul>";
        echo "<li>All products are adequately stocked</li>";
        echo "<li>No pending payments</li>";
        echo "<li>Sales haven't reached milestone thresholds</li>";
        echo "<li>No stagnant inventory issues</li>";
        echo "</ul>";
    } else {
        echo "<p>✅ Generated " . count($newNotifications) . " notifications based on business conditions:</p>";
        echo "<ul>";
        foreach ($newNotifications as $notification) {
            $typeIcon = [
                'info' => 'ℹ️',
                'success' => '✅',
                'warning' => '⚠️',
                'error' => '❌'
            ][$notification['type']] ?? '🔔';
            
            echo "<li>$typeIcon <strong>{$notification['title']}</strong> - {$notification['message']}</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    // Test notification display functions
    echo "<div class='test-result info'>";
    echo "<h2>🎨 Testing Notification Display Functions</h2>";
    
    $unreadCount = getUnreadNotificationCount();
    $unreadNotifications = getUnreadNotifications(null, 5);
    
    echo "<p><strong>Unread Notification Count:</strong> $unreadCount</p>";
    echo "<p><strong>Recent Notifications:</strong></p>";
    
    if (empty($unreadNotifications)) {
        echo "<p>No unread notifications to display</p>";
    } else {
        echo "<div style='border: 1px solid #ddd; border-radius: 5px; max-width: 600px;'>";
        echo displayNotifications($unreadNotifications);
        echo "</div>";
    }
    echo "</div>";
    
    // Test notification cleanup
    echo "<div class='test-result info'>";
    echo "<h2>🧽 Testing Notification Cleanup</h2>";
    
    // Create some old test notifications
    $db->query("INSERT INTO notifications (title, message, type, is_read, created_at) VALUES (?, ?, ?, ?, ?)", [
        'Old Test Notification',
        'This is an old notification for testing cleanup',
        'info',
        1,
        date('Y-m-d H:i:s', strtotime('-35 days'))
    ]);
    
    $beforeCleanup = $db->fetch("SELECT COUNT(*) as count FROM notifications")['count'];
    echo "<p>Notifications before cleanup: $beforeCleanup</p>";
    
    cleanupOldNotifications(30);
    
    $afterCleanup = $db->fetch("SELECT COUNT(*) as count FROM notifications")['count'];
    echo "<p>Notifications after cleanup: $afterCleanup</p>";
    echo "<p>✅ Cleanup function working correctly</p>";
    echo "</div>";
    
    // Create some sample notifications for testing
    echo "<div class='test-result success'>";
    echo "<h2>🎯 Creating Sample Notifications for Testing</h2>";
    
    $sampleNotifications = [
        ['System Health Check', 'All systems are running normally. Database connection: OK, Backup status: Current.', 'success'],
        ['Weekly Report Available', 'Your weekly sales and inventory report is ready for review.', 'info'],
        ['Price Update Reminder', 'Consider updating product prices based on latest metal rate changes.', 'warning']
    ];
    
    foreach ($sampleNotifications as $notif) {
        createNotification(null, $notif[0], $notif[1], $notif[2]);
        echo "<p>✅ Created: {$notif[0]}</p>";
    }
    
    echo "</div>";
    
    // Final status
    $finalCount = getUnreadNotificationCount();
    
    echo "<div class='test-result success'>";
    echo "<h2>🎉 Notification System Test Complete!</h2>";
    echo "<p><strong>✅ Automatic Generation:</strong> Working correctly</p>";
    echo "<p><strong>✅ Display Functions:</strong> Working correctly</p>";
    echo "<p><strong>✅ Cleanup Functions:</strong> Working correctly</p>";
    echo "<p><strong>✅ Database Operations:</strong> All successful</p>";
    echo "<p><strong>Current Unread Notifications:</strong> $finalCount</p>";
    echo "<br>";
    echo "<p><a href='index.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📊 View Dashboard with Notifications</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-result' style='background: #f8d7da; color: #721c24;'>";
    echo "<h2>❌ Test Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
