<?php
/**
 * IN<PERSON>AN JEWELRY WHOLESALE MANAGEMENT SYSTEM
 * Designed specifically for Indian jewelry wholesale business
 * 
 * Key Features:
 * - Tunch percentage based metal value calculation
 * - 24K gold equivalent tracking
 * - Making charges and stone charges
 * - Supplier and customer management
 * - Weight-based inventory management
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

class IndianJewelrySystem {
    private $pdo;
    private $currentUser;
    
    public function __construct() {
        $this->initDatabase();
        $this->checkAuthentication();
    }
    
    private function initDatabase() {
        try {
            $this->pdo = new PDO('sqlite:indian_jewelry.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createTables();
        } catch(PDOException $e) {
            die("Database Error: " . $e->getMessage());
        }
    }
    
    private function createTables() {
        // Users table
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT DEFAULT 'staff',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Suppliers table (Indian jewelry suppliers)
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            gst_number TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Customers table (Indian jewelry customers)
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            customer_type TEXT DEFAULT 'retail',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Inventory table (Indian jewelry inventory structure)
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sl_no INTEGER,
            supplier_id INTEGER,
            supplier_name TEXT,
            description TEXT,
            product_name TEXT NOT NULL,

            -- Weight Management (Indian jewelry standard)
            product_weight REAL DEFAULT 0,
            without_stone_weight REAL DEFAULT 0,

            -- Cost Management
            with_stone_cost REAL DEFAULT 0,
            without_stone_cost REAL DEFAULT 0,

            -- 24K Gold Calculations (Indian standard)
            procured_in_24k REAL DEFAULT 0,
            sold_value REAL DEFAULT 0,
            balance_in_stock REAL DEFAULT 0,
            with_stone_24k REAL DEFAULT 0,
            without_stone_24k REAL DEFAULT 0,
            weight_in_24k REAL DEFAULT 0,

            -- Pricing
            unit_price REAL DEFAULT 0,
            stone_price REAL DEFAULT 0,
            total_value REAL DEFAULT 0,

            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )");
        
        // Billing table (Indian jewelry billing format)
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS billing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sl_no INTEGER,
            invoice_number TEXT UNIQUE,

            -- Customer Information
            customer_name TEXT,
            location TEXT,

            -- Product Information
            product_name TEXT,
            product_type TEXT,

            -- Weight Calculations (Indian jewelry standard)
            gross_weight REAL DEFAULT 0,
            stone_weight REAL DEFAULT 0,
            net_weight REAL DEFAULT 0,

            -- Tunch Calculation (Indian jewelry purity system)
            tunch_percentage REAL DEFAULT 0,
            tunch_weight REAL DEFAULT 0,

            -- Pricing (Indian jewelry pricing structure)
            base_rate REAL DEFAULT 0,
            metal_value REAL DEFAULT 0,
            making_charges REAL DEFAULT 0,
            stone_charges REAL DEFAULT 0,

            -- Final Calculation
            total_amount REAL DEFAULT 0,

            -- Payment
            payment_status TEXT DEFAULT 'pending',

            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        $this->createSampleData();
    }
    
    private function createSampleData() {
        // Create admin user
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("INSERT INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', $passwordHash, 'System Administrator', 'admin']);
        }
        
        // Create suppliers from your data
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM suppliers");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $suppliers = [
                ['Emerald Jewel Industry', 'Manager', '9876543210', '<EMAIL>', 'Coimbatore', 'Tamil Nadu', 'GST001'],
                ['Nala Gold', 'Manager', '9876543211', '<EMAIL>', 'Mumbai', 'Maharashtra', 'GST002'],
                ['Surat', 'Manager', '9876543212', '<EMAIL>', 'Surat', 'Gujarat', 'GST003']
            ];
            
            foreach ($suppliers as $supplier) {
                $stmt = $this->pdo->prepare("INSERT INTO suppliers (name, contact_person, phone, email, city, state, gst_number) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute($supplier);
            }
        }
        
        // Create inventory from your EXACT spreadsheet data (rows 5, 6, 7)
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM inventory");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $inventory = [
                // Row 5: Emerald Jewel Industry, Coimbatore, Chain, 0, 106.420, 93, 94, 113.195, 0.000, 10.160, 9.754, 110.260
                [5, 1, 'Emerald Jewel Industry', 'Coimbatore', 'Chain', 0.000, 106.420, 93, 94, 113.195, 0.000, 0, 0.000, 10.160, 9.754, 110.260, 0, 110.260],
                // Row 6: Nala Gold, Mumbai, Bangles, 0, 246.340, 94, 96, 245.480, 0.000, 32.120, 31.478, 214.220
                [6, 2, 'Nala Gold', 'Mumbai', 'Bangles', 0.000, 246.340, 94, 96, 245.480, 0.000, 0, 0.000, 32.120, 31.478, 214.220, 0, 214.220],
                // Row 7: Surat, Studs, 110.325, 89.120, 95, 98, 193.038, 1.560, 2.010, 3.388, 195.875
                [7, 3, 'Surat', 'Surat', 'Studs', 110.325, 89.120, 95, 98, 193.038, 1.560, 110.325, 2.010, 3.388, 195.875, 0, 195.875]
            ];
            
            foreach ($inventory as $item) {
                $stmt = $this->pdo->prepare("INSERT INTO inventory (
                    sl_no, supplier_id, supplier_name, description, product_name,
                    product_weight, without_stone_weight, with_stone_cost, without_stone_cost,
                    procured_in_24k, sold_value, balance_in_stock, with_stone_24k,
                    without_stone_24k, weight_in_24k, unit_price, stone_price, total_value
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute($item);
            }
        }
        
        // Create billing data from your spreadsheet (rows 14, 15, 16)
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM billing");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $billing = [
                // Row 14: VG Jewellery, Narimakal, Chain
                [14, 'INV-2024-0001', 'VG Jewellery', 'Narimakal', 'Chain', 'Gold Chain', 10.160, 0.000, 10.160, 96, 9.754, 10140.00, 98901.56, 0, 0, 98901.56],
                // Row 15: Nishma Jewels, Chengala, Bangles
                [15, 'INV-2024-0002', 'Nishma Jewels', 'Chengala', 'Bangles', 'Gold Bangles', 82.220, 0.000, 82.220, 98, 80.376, 10140.00, 815012.64, 0, 0, 815012.64],
                // Row 16: Dhanpal Jewels, Erode, Studs
                [16, 'INV-2024-0003', 'Dhanpal Jewels', 'Erode', 'Studs', 'Gold Studs', 1.560, 0.390, 1.170, 97, 1.135, 10140.00, 11508.90, 1000, 0, 12508.90]
            ];
            
            foreach ($billing as $bill) {
                $stmt = $this->pdo->prepare("INSERT INTO billing (
                    sl_no, invoice_number, customer_name, location, product_name, product_type,
                    gross_weight, stone_weight, net_weight, tunch_percentage, tunch_weight,
                    base_rate, metal_value, making_charges, stone_charges, total_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute($bill);
            }
        }
    }
    
    private function checkAuthentication() {
        if (!isset($_SESSION['user_id']) && !$this->isLoginPage()) {
            header('Location: ?page=login');
            exit;
        }
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $this->currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
    
    private function isLoginPage() {
        return isset($_GET['page']) && $_GET['page'] === 'login';
    }
    
    public function handleLogin() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                header('Location: ?page=dashboard');
                exit;
            } else {
                return "Invalid username or password";
            }
        }
        return null;
    }
    
    public function handleLogout() {
        session_destroy();
        header('Location: ?page=login');
        exit;
    }

    // Indian Jewelry Tunch Calculation (Purity-based metal value)
    public function calculateTunchValue($data) {
        $gross_weight = floatval($data['gross_weight']);
        $stone_weight = floatval($data['stone_weight']);
        $net_weight = $gross_weight - $stone_weight;
        $tunch_percentage = floatval($data['tunch_percentage']);
        $base_rate = floatval($data['base_rate']);
        $making_charges = floatval($data['making_charges'] ?? 0);
        $stone_charges = floatval($data['stone_charges'] ?? 0);

        // Indian Jewelry Standard Calculation
        $tunch_weight = $net_weight * ($tunch_percentage / 100);
        $metal_value = $tunch_weight * $base_rate;
        $total_amount = $metal_value + $making_charges + $stone_charges;

        return [
            'net_weight' => $net_weight,
            'tunch_weight' => $tunch_weight,
            'metal_value' => $metal_value,
            'total_amount' => $total_amount
        ];
    }

    public function createBill($data) {
        try {
            $this->pdo->beginTransaction();

            // Calculate tunch value
            $calculations = $this->calculateTunchValue($data);

            // Generate invoice number
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM billing");
            $stmt->execute();
            $count = $stmt->fetchColumn() + 1;
            $invoice_number = 'INV-' . date('Y') . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);

            // Insert billing record
            $stmt = $this->pdo->prepare("INSERT INTO billing (
                invoice_number, customer_name, location, product_name, product_type,
                gross_weight, stone_weight, net_weight, tunch_percentage, tunch_weight,
                base_rate, metal_value, making_charges, stone_charges, total_amount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $invoice_number,
                $data['customer_name'],
                $data['location'],
                $data['product_name'],
                $data['product_type'] ?? 'Gold Jewelry',
                $data['gross_weight'],
                $data['stone_weight'],
                $calculations['net_weight'],
                $data['tunch_percentage'],
                $calculations['tunch_weight'],
                $data['base_rate'],
                $calculations['metal_value'],
                $data['making_charges'] ?? 0,
                $data['stone_charges'] ?? 0,
                $calculations['total_amount']
            ]);

            $this->pdo->commit();
            return ['success' => true, 'invoice_number' => $invoice_number];

        } catch (Exception $e) {
            $this->pdo->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function addInventoryItem($data) {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO inventory (
                supplier_id, supplier_name, description, product_name,
                product_weight, without_stone_weight, with_stone_cost, without_stone_cost,
                procured_in_24k, with_stone_24k, without_stone_24k, weight_in_24k,
                unit_price, stone_price, total_value, balance_in_stock
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            return $stmt->execute([
                $data['supplier_id'],
                $data['supplier_name'],
                $data['description'],
                $data['product_name'],
                $data['product_weight'],
                $data['without_stone_weight'],
                $data['with_stone_cost'],
                $data['without_stone_cost'],
                $data['procured_in_24k'],
                $data['with_stone_24k'],
                $data['without_stone_24k'],
                $data['weight_in_24k'],
                $data['unit_price'],
                $data['stone_price'],
                $data['total_value'],
                $data['balance_in_stock']
            ]);
        } catch (Exception $e) {
            return false;
        }
    }

    public function getInventory() {
        $stmt = $this->pdo->prepare("SELECT * FROM inventory ORDER BY sl_no");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getBillingHistory() {
        $stmt = $this->pdo->prepare("SELECT * FROM billing ORDER BY sl_no");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getSuppliers() {
        $stmt = $this->pdo->prepare("SELECT * FROM suppliers ORDER BY name");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDashboardStats() {
        $stats = [];

        // Total inventory value (handle null values)
        $stmt = $this->pdo->prepare("SELECT COALESCE(SUM(total_value), 0) as total_inventory_value FROM inventory");
        $stmt->execute();
        $stats['total_inventory_value'] = floatval($stmt->fetchColumn()) ?: 0;

        // Total sales (handle null values)
        $stmt = $this->pdo->prepare("SELECT COALESCE(SUM(total_amount), 0) as total_sales FROM billing");
        $stmt->execute();
        $stats['total_sales'] = floatval($stmt->fetchColumn()) ?: 0;

        // Total items
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as total_items FROM inventory");
        $stmt->execute();
        $stats['total_items'] = intval($stmt->fetchColumn()) ?: 0;

        // Total bills
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as total_bills FROM billing");
        $stmt->execute();
        $stats['total_bills'] = intval($stmt->fetchColumn()) ?: 0;

        return $stats;
    }
}

// Initialize the Indian Jewelry System
$system = new IndianJewelrySystem();

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['login'])) {
        $error = $system->handleLogin();
    } elseif (isset($_POST['create_bill'])) {
        $result = $system->createBill($_POST);
        if ($result['success']) {
            $message = "✅ Bill created successfully! Invoice: " . $result['invoice_number'];
        } else {
            $error = "❌ Error creating bill: " . $result['error'];
        }
    } elseif (isset($_POST['add_inventory'])) {
        if ($system->addInventoryItem($_POST)) {
            $message = "✅ Inventory item added successfully!";
        } else {
            $error = "❌ Error adding inventory item.";
        }
    }
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    $system->handleLogout();
}

// Get current page
$page = $_GET['page'] ?? 'dashboard';

// Get data for current page
$data = [];
switch ($page) {
    case 'dashboard':
        $data['stats'] = $system->getDashboardStats();
        $data['recent_bills'] = $system->getBillingHistory();
        $data['inventory'] = $system->getInventory();
        break;
    case 'billing':
        $data['recent_bills'] = $system->getBillingHistory();
        break;
    case 'inventory':
        $data['inventory'] = $system->getInventory();
        $data['suppliers'] = $system->getSuppliers();
        break;
    case 'reports':
        $data['stats'] = $system->getDashboardStats();
        $data['all_bills'] = $system->getBillingHistory();
        $data['all_inventory'] = $system->getInventory();
        break;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indian Jewelry Wholesale Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            color: white;
        }
        .indian-header {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .tunch-display {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #8b4513;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #daa520;
        }
        .btn-primary {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-success {
            background: linear-gradient(135deg, #32cd32 0%, #228b22 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
        }
        .login-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
        }
        .indian-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff9a56' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="indian-pattern">

<?php if ($page === 'login'): ?>
    <!-- LOGIN PAGE -->
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="login-card p-5" style="width: 400px;">
            <div class="text-center mb-4">
                <i class="fas fa-gem fa-3x text-warning mb-3"></i>
                <h2>Indian Jewelry System</h2>
                <p class="text-muted">Wholesale Management</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    Default: admin / admin123<br>
                    <strong>🇮🇳 Indian Jewelry System</strong>
                </small>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- MAIN APPLICATION -->
    <div class="container-fluid">
        <div class="row">
            <!-- SIDEBAR -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-4">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-gem fa-2x mb-2"></i>
                        <h5>Indian Jewelry</h5>
                        <small>Wholesale System</small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="?page=dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a class="nav-link <?php echo $page === 'billing' ? 'active' : ''; ?>" href="?page=billing">
                            <i class="fas fa-file-invoice-dollar me-2"></i> Billing
                        </a>
                        <a class="nav-link <?php echo $page === 'inventory' ? 'active' : ''; ?>" href="?page=inventory">
                            <i class="fas fa-boxes me-2"></i> Inventory
                        </a>
                        <a class="nav-link <?php echo $page === 'reports' ? 'active' : ''; ?>" href="?page=reports">
                            <i class="fas fa-chart-bar me-2"></i> Reports
                        </a>
                        <hr class="text-white-50">
                        <a class="nav-link" href="?action=logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- MAIN CONTENT -->
            <div class="col-md-10 main-content p-4">
                <!-- Header -->
                <div class="indian-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2><i class="fas fa-gem"></i> <?php echo ucfirst($page); ?></h2>
                            <p class="mb-0">Welcome, <?php echo $_SESSION['username']; ?> | Indian Jewelry Wholesale System</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h6><?php echo date('l, F j, Y'); ?></h6>
                            <small>Tunch-based Calculations</small>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- PAGE CONTENT -->
                <?php if ($page === 'dashboard'): ?>
                    <!-- DASHBOARD -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-boxes fa-2x mb-3"></i>
                                    <h3><?php echo $data['stats']['total_items']; ?></h3>
                                    <p class="mb-0">Inventory Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-rupee-sign fa-2x mb-3"></i>
                                    <h3>₹<?php echo number_format($data['stats']['total_inventory_value'] ?? 0, 0); ?></h3>
                                    <p class="mb-0">Inventory Value</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-file-invoice fa-2x mb-3"></i>
                                    <h3><?php echo $data['stats']['total_bills']; ?></h3>
                                    <p class="mb-0">Total Bills</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card text-center">
                                <div class="card-body">
                                    <i class="fas fa-chart-line fa-2x mb-3"></i>
                                    <h3>₹<?php echo number_format($data['stats']['total_sales'] ?? 0, 0); ?></h3>
                                    <p class="mb-0">Total Sales</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SAMPLE DATA DISPLAY -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-warehouse"></i> Your Inventory Data</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Sl.No</th>
                                                    <th>Supplier</th>
                                                    <th>Product</th>
                                                    <th>Without Stone</th>
                                                    <th>Total Value</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['inventory'] as $item): ?>
                                                <tr>
                                                    <td><?php echo $item['sl_no']; ?></td>
                                                    <td><?php echo $item['supplier_name']; ?></td>
                                                    <td><?php echo $item['product_name']; ?></td>
                                                    <td><?php echo number_format($item['without_stone_weight'] ?? 0, 3); ?>g</td>
                                                    <td>₹<?php echo number_format($item['total_value'] ?? 0, 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-file-invoice-dollar"></i> Your Billing Data</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Sl.No</th>
                                                    <th>Customer</th>
                                                    <th>Product</th>
                                                    <th>Tunch %</th>
                                                    <th>Metal Value</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['recent_bills'] as $bill): ?>
                                                <tr>
                                                    <td><?php echo $bill['sl_no']; ?></td>
                                                    <td><?php echo $bill['customer_name']; ?></td>
                                                    <td><?php echo $bill['product_name']; ?></td>
                                                    <td><?php echo $bill['tunch_percentage'] ?? 0; ?>%</td>
                                                    <td>₹<?php echo number_format($bill['metal_value'] ?? 0, 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php elseif ($page === 'inventory'): ?>
                    <!-- INVENTORY PAGE -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-warehouse"></i> Inventory Management (Your Spreadsheet Data)</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <strong>✅ EXACT MATCH TO YOUR SPREADSHEET:</strong><br>
                                <div class="row">
                                    <div class="col-md-4"><strong>Row 5:</strong> Emerald Jewel Industry, Chain, 106.420g, ₹110.260</div>
                                    <div class="col-md-4"><strong>Row 6:</strong> Nala Gold, Bangles, 246.340g, ₹214.220</div>
                                    <div class="col-md-4"><strong>Row 7:</strong> Surat, Studs, 89.120g, ₹195.875</div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Sl.No</th>
                                            <th>Supplier Name</th>
                                            <th>Description</th>
                                            <th>Product Name</th>
                                            <th>Product Weight</th>
                                            <th>Without Stone</th>
                                            <th>With Stone Cost</th>
                                            <th>Without Stone Cost</th>
                                            <th>Procured in 24K</th>
                                            <th>Sold Value</th>
                                            <th>Balance in Stock</th>
                                            <th>With Stone 24K</th>
                                            <th>Without Stone 24K</th>
                                            <th>Weight in 24K</th>
                                            <th>Unit Price</th>
                                            <th>Stone Price</th>
                                            <th>Total Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data['inventory'] as $item): ?>
                                        <tr>
                                            <td><strong><?php echo $item['sl_no']; ?></strong></td>
                                            <td><?php echo $item['supplier_name']; ?></td>
                                            <td><?php echo $item['description']; ?></td>
                                            <td><strong><?php echo $item['product_name']; ?></strong></td>
                                            <td><?php echo ($item['product_weight'] ?? 0) == 0 ? '0' : number_format($item['product_weight'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['without_stone_weight'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['with_stone_cost'] ?? 0, 0); ?></td>
                                            <td><?php echo number_format($item['without_stone_cost'] ?? 0, 0); ?></td>
                                            <td><?php echo number_format($item['procured_in_24k'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['sold_value'] ?? 0, 3); ?></td>
                                            <td><?php echo ($item['balance_in_stock'] ?? 0) == 0 ? '0' : number_format($item['balance_in_stock'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['with_stone_24k'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['without_stone_24k'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['weight_in_24k'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['unit_price'] ?? 0, 3); ?></td>
                                            <td><?php echo number_format($item['stone_price'] ?? 0, 0); ?></td>
                                            <td><strong>₹<?php echo number_format($item['total_value'] ?? 0, 3); ?></strong></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php elseif ($page === 'billing'): ?>
                    <!-- BILLING PAGE -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-invoice-dollar"></i> Billing Management (Your Spreadsheet Data)</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>🧮 Indian Jewelry Tunch Calculation:</strong><br>
                                Metal Value = Tunch Weight × Base Rate<br>
                                <small>Tunch Weight = Net Weight × (Tunch Percentage ÷ 100)</small>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Sl.No</th>
                                            <th>Customer</th>
                                            <th>Location</th>
                                            <th>Product</th>
                                            <th>Gross Weight</th>
                                            <th>Stone Weight</th>
                                            <th>Net Weight</th>
                                            <th>Tunch %</th>
                                            <th>Tunch Weight</th>
                                            <th>Base Rate</th>
                                            <th>Metal Value</th>
                                            <th>Making</th>
                                            <th>Total Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data['recent_bills'] as $bill): ?>
                                        <tr>
                                            <td><strong><?php echo $bill['sl_no']; ?></strong></td>
                                            <td><?php echo $bill['customer_name']; ?></td>
                                            <td><?php echo $bill['location']; ?></td>
                                            <td><?php echo $bill['product_name']; ?></td>
                                            <td><?php echo number_format($bill['gross_weight'] ?? 0, 3); ?>g</td>
                                            <td><?php echo number_format($bill['stone_weight'] ?? 0, 3); ?>g</td>
                                            <td><?php echo number_format($bill['net_weight'] ?? 0, 3); ?>g</td>
                                            <td><?php echo $bill['tunch_percentage'] ?? 0; ?>%</td>
                                            <td><?php echo number_format($bill['tunch_weight'] ?? 0, 3); ?>g</td>
                                            <td>₹<?php echo number_format($bill['base_rate'] ?? 0, 2); ?></td>
                                            <td><strong>₹<?php echo number_format($bill['metal_value'] ?? 0, 2); ?></strong></td>
                                            <td>₹<?php echo number_format($bill['making_charges'] ?? 0, 0); ?></td>
                                            <td><strong class="text-success">₹<?php echo number_format($bill['total_amount'] ?? 0, 2); ?></strong></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php else: ?>
                    <div class="alert alert-info">
                        <h5>🇮🇳 Indian Jewelry Wholesale System</h5>
                        <p>Navigate using the sidebar to view your data</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
