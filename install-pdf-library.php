<?php
/**
 * Install PDF Library Instructions
 * This page provides instructions for installing a proper PDF library
 */
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Library Installation Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .alert-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0066cc;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>PDF Library Installation Guide</h1>
    
    <div class="alert alert-info">
        <h3>📋 Current Status</h3>
        <p>The print and PDF functionality is currently working with basic HTML-to-PDF conversion using the browser's built-in capabilities. For production use, you may want to install a proper PDF library for better control and formatting.</p>
    </div>

    <h2>🔧 Current Implementation</h2>
    <div class="alert alert-success">
        <h4>✅ What's Working Now:</h4>
        <ul>
            <li><strong>Print Bill:</strong> Opens a printer-friendly HTML page</li>
            <li><strong>PDF Generation:</strong> Uses browser's "Print to PDF" functionality</li>
            <li><strong>Professional Layout:</strong> Clean, formatted bill layout</li>
            <li><strong>Customer Information:</strong> Displays customer details, bill items, totals</li>
        </ul>
    </div>

    <h2>📈 Enhanced PDF Options (Optional)</h2>
    
    <h3>Option 1: TCPDF (Recommended)</h3>
    <div class="alert alert-info">
        <p><strong>TCPDF</strong> is a popular PHP library for generating PDF documents.</p>
        
        <h4>Installation via Composer:</h4>
        <div class="code-block">
composer require tecnickcom/tcpdf
        </div>
        
        <h4>Manual Installation:</h4>
        <ol>
            <li>Download TCPDF from <a href="https://tcpdf.org/" target="_blank">https://tcpdf.org/</a></li>
            <li>Extract to a <code>vendor/tcpdf/</code> folder in your project</li>
            <li>Include the library in your PHP files</li>
        </ol>
    </div>

    <h3>Option 2: mPDF</h3>
    <div class="alert alert-info">
        <p><strong>mPDF</strong> is another excellent PHP PDF library with good HTML/CSS support.</p>
        
        <h4>Installation via Composer:</h4>
        <div class="code-block">
composer require mpdf/mpdf
        </div>
    </div>

    <h3>Option 3: DomPDF</h3>
    <div class="alert alert-info">
        <p><strong>DomPDF</strong> converts HTML/CSS to PDF with good styling support.</p>
        
        <h4>Installation via Composer:</h4>
        <div class="code-block">
composer require dompdf/dompdf
        </div>
    </div>

    <h2>🚀 Quick Start (No Installation Required)</h2>
    <div class="alert alert-success">
        <h4>Current Solution Works Great For:</h4>
        <ul>
            <li>✅ Small to medium businesses</li>
            <li>✅ Quick deployment without dependencies</li>
            <li>✅ Users comfortable with browser PDF generation</li>
            <li>✅ Development and testing environments</li>
        </ul>
        
        <p><strong>How to use:</strong></p>
        <ol>
            <li>Go to <a href="sales.php">Sales Page</a></li>
            <li>Click the "Print" button for any sale</li>
            <li>Use your browser's "Print" function and select "Save as PDF"</li>
        </ol>
    </div>

    <h2>🔧 Testing Current Functionality</h2>
    <div style="text-align: center; margin: 30px 0;">
        <a href="test-print-pdf.php" class="btn">Test Print & PDF Functions</a>
        <a href="sales.php" class="btn">Go to Sales Page</a>
        <a href="billing.php" class="btn">Create New Sale</a>
    </div>

    <h2>📝 Customization</h2>
    <div class="alert alert-warning">
        <h4>To Customize Bill Layout:</h4>
        <ul>
            <li><strong>Edit print-bill.php:</strong> Modify the HTML structure and CSS styles</li>
            <li><strong>Edit generate-pdf.php:</strong> Adjust the PDF-specific formatting</li>
            <li><strong>Company Details:</strong> Update header information in both files</li>
            <li><strong>Logo:</strong> Add your company logo to the header section</li>
        </ul>
    </div>

    <h2>🎯 Production Recommendations</h2>
    <div class="alert alert-info">
        <h4>For Production Deployment:</h4>
        <ol>
            <li><strong>Test Current Solution:</strong> Try the current implementation first</li>
            <li><strong>User Feedback:</strong> See if users are comfortable with browser PDF generation</li>
            <li><strong>If Needed:</strong> Install a PDF library for more control</li>
            <li><strong>Backup Plan:</strong> Current solution works as fallback</li>
        </ol>
    </div>

    <div style="text-align: center; margin: 40px 0;">
        <h3>Ready to Test?</h3>
        <a href="test-print-pdf.php" class="btn" style="font-size: 18px; padding: 15px 30px;">Start Testing Print & PDF</a>
    </div>
</body>
</html>
