<?php
require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>📂 Available Categories</h2>";
    $categories = $db->fetchAll('SELECT * FROM categories');
    
    foreach($categories as $cat) {
        echo "<p>ID: {$cat['id']} - {$cat['category_name']}</p>";
    }
    
    echo "<h2>🚚 Available Suppliers</h2>";
    $suppliers = $db->fetchAll('SELECT * FROM suppliers');
    
    if (empty($suppliers)) {
        echo "<p>No suppliers found</p>";
    } else {
        foreach($suppliers as $sup) {
            echo "<p>ID: {$sup['id']} - {$sup['supplier_name']}</p>";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
