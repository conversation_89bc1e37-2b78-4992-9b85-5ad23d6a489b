<?php
/**
 * Toggle Maintenance Mode AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $enabled = $input['enabled'] ?? false;
    $message = $input['message'] ?? 'System is under maintenance. Please try again later.';
    $endTime = $input['endTime'] ?? null;
    
    $db = getDB();
    
    // Update or insert maintenance settings
    $settings = [
        'maintenance_mode' => $enabled ? '1' : '0',
        'maintenance_message' => $message,
        'maintenance_end_time' => $endTime
    ];
    
    foreach ($settings as $key => $value) {
        $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
        
        if ($existing) {
            $db->query("
                UPDATE settings 
                SET setting_value = ?, updated_at = NOW() 
                WHERE setting_key = ?
            ", [$value, $key]);
        } else {
            $db->query("
                INSERT INTO settings (setting_key, setting_value, setting_type, description, created_at) 
                VALUES (?, ?, 'system', ?, NOW())
            ", [$key, $value, "Maintenance mode setting: $key"]);
        }
    }
    
    // Create maintenance file if enabled
    $maintenanceFile = '../maintenance.flag';
    
    if ($enabled) {
        $maintenanceData = [
            'enabled' => true,
            'message' => $message,
            'end_time' => $endTime,
            'enabled_at' => date('Y-m-d H:i:s'),
            'enabled_by' => $_SESSION['user_id'] ?? 'system'
        ];
        
        file_put_contents($maintenanceFile, json_encode($maintenanceData, JSON_PRETTY_PRINT));
        
        // Log maintenance mode activation
        $db->query("
            INSERT INTO system_logs (log_type, level, message, context, user_id, created_at) 
            VALUES ('system', 'warning', 'Maintenance mode enabled', ?, ?, NOW())
        ", [
            json_encode(['message' => $message, 'end_time' => $endTime]),
            $_SESSION['user_id'] ?? null
        ]);
        
    } else {
        // Remove maintenance file
        if (file_exists($maintenanceFile)) {
            unlink($maintenanceFile);
        }
        
        // Log maintenance mode deactivation
        $db->query("
            INSERT INTO system_logs (log_type, level, message, context, user_id, created_at) 
            VALUES ('system', 'info', 'Maintenance mode disabled', '{}', ?, NOW())
        ", [$_SESSION['user_id'] ?? null]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => $enabled ? 'Maintenance mode enabled' : 'Maintenance mode disabled',
        'status' => [
            'enabled' => $enabled,
            'message' => $message,
            'end_time' => $endTime
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
