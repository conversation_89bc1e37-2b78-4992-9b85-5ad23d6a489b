<?php
/**
 * Test Metal Rates Update Functionality
 */

require_once 'config/database.php';

echo "<h1>🔧 Metal Rates Update Test</h1>";

try {
    $db = getDB();
    
    // Test 1: Check current rates
    echo "<h2>📊 Current Metal Rates</h2>";
    $current_rates = $db->fetchAll("
        SELECT metal_type, purity, rate_per_gram, rate_date 
        FROM metal_rates 
        WHERE rate_date = CURDATE() AND is_active = 1 
        ORDER BY metal_type, purity
    ");
    
    if (empty($current_rates)) {
        echo "<p>⚠️ No rates found for today. Let's create some...</p>";
        
        // Insert today's rates
        $today_rates = [
            ['Gold', '24K', 6200.00],
            ['Gold', '22K', 5800.00],
            ['Gold', '18K', 4650.00],
            ['Silver', '999', 85.00],
            ['Silver', '925', 78.00],
            ['Platinum', '950', 3200.00]
        ];
        
        foreach ($today_rates as $rate) {
            $db->query("
                INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, created_by) 
                VALUES (?, ?, ?, CURDATE(), 1)
                ON DUPLICATE KEY UPDATE 
                rate_per_gram = VALUES(rate_per_gram), 
                is_active = 1
            ", $rate);
        }
        
        echo "<p>✅ Created today's rates</p>";
        
        // Fetch again
        $current_rates = $db->fetchAll("
            SELECT metal_type, purity, rate_per_gram, rate_date 
            FROM metal_rates 
            WHERE rate_date = CURDATE() AND is_active = 1 
            ORDER BY metal_type, purity
        ");
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Metal</th><th>Purity</th><th>Rate/gram</th><th>Date</th></tr>";
    foreach ($current_rates as $rate) {
        echo "<tr>";
        echo "<td>{$rate['metal_type']}</td>";
        echo "<td>{$rate['purity']}</td>";
        echo "<td>₹" . number_format($rate['rate_per_gram'], 2) . "</td>";
        echo "<td>{$rate['rate_date']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 2: Test update functionality
    echo "<h2>🔄 Test Rate Update</h2>";
    
    if ($_POST && isset($_POST['test_update'])) {
        echo "<h3>Processing Update...</h3>";
        
        // Simulate the update process
        $test_rates = [
            'Gold_24K' => ['metal_type' => 'Gold', 'purity' => '24K', 'rate_per_gram' => '6250.00'],
            'Gold_22K' => ['metal_type' => 'Gold', 'purity' => '22K', 'rate_per_gram' => '5850.00'],
            'Silver_999' => ['metal_type' => 'Silver', 'purity' => '999', 'rate_per_gram' => '87.00']
        ];
        
        $updated_count = 0;
        foreach ($test_rates as $rate_data) {
            $metal_type = $rate_data['metal_type'];
            $purity = $rate_data['purity'];
            $rate_per_gram = floatval($rate_data['rate_per_gram']);
            
            if ($metal_type && $purity && $rate_per_gram > 0) {
                // Check if rate exists for today
                $existing = $db->fetch("
                    SELECT id FROM metal_rates 
                    WHERE metal_type = ? AND purity = ? AND rate_date = CURDATE()
                ", [$metal_type, $purity]);
                
                if ($existing) {
                    // Update existing rate
                    $db->query("
                        UPDATE metal_rates 
                        SET rate_per_gram = ?, is_active = 1 
                        WHERE id = ?
                    ", [$rate_per_gram, $existing['id']]);
                    echo "<p>✅ Updated {$metal_type} {$purity}: ₹{$rate_per_gram}</p>";
                } else {
                    // Insert new rate
                    $db->query("
                        INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, created_by) 
                        VALUES (?, ?, ?, CURDATE(), 1)
                    ", [$metal_type, $purity, $rate_per_gram]);
                    echo "<p>✅ Created {$metal_type} {$purity}: ₹{$rate_per_gram}</p>";
                }
                $updated_count++;
            }
        }
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "<h4>✅ Update Successful!</h4>";
        echo "<p>Updated $updated_count rates successfully.</p>";
        echo "</div>";
        
        echo "<p><a href='test-metal-rates.php'>Refresh to see updated rates</a></p>";
    } else {
        // Show update form
        echo "<form method='POST'>";
        echo "<p>Click the button below to test updating metal rates:</p>";
        echo "<button type='submit' name='test_update' value='1' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🔄 Test Rate Update";
        echo "</button>";
        echo "</form>";
    }
    
    // Test 3: Check the actual form structure from metal-rates.php
    echo "<h2>🔍 Form Structure Analysis</h2>";
    
    // Simulate the form data structure
    $metal_types = [
        'Gold' => ['24K', '22K', '18K'],
        'Silver' => ['999', '925'],
        'Platinum' => ['950']
    ];
    
    echo "<h4>Expected Form Structure:</h4>";
    echo "<pre>";
    echo "rates[Gold_24K][metal_type] = 'Gold'\n";
    echo "rates[Gold_24K][purity] = '24K'\n";
    echo "rates[Gold_24K][rate_per_gram] = '6200.00'\n";
    echo "rates[Gold_22K][metal_type] = 'Gold'\n";
    echo "rates[Gold_22K][purity] = '22K'\n";
    echo "rates[Gold_22K][rate_per_gram] = '5800.00'\n";
    echo "... and so on for all metals\n";
    echo "</pre>";
    
    // Test 4: Direct database operations
    echo "<h2>💾 Database Operations Test</h2>";
    
    // Test insert
    try {
        $test_metal = 'TestMetal';
        $test_purity = 'TEST';
        $test_rate = 999.99;
        
        $db->query("
            INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, created_by) 
            VALUES (?, ?, ?, CURDATE(), 1)
        ", [$test_metal, $test_purity, $test_rate]);
        
        echo "<p>✅ Insert test successful</p>";
        
        // Test update
        $new_rate = 888.88;
        $db->query("
            UPDATE metal_rates 
            SET rate_per_gram = ? 
            WHERE metal_type = ? AND purity = ? AND rate_date = CURDATE()
        ", [$new_rate, $test_metal, $test_purity]);
        
        echo "<p>✅ Update test successful</p>";
        
        // Clean up test data
        $db->query("
            DELETE FROM metal_rates 
            WHERE metal_type = ? AND purity = ?
        ", [$test_metal, $test_purity]);
        
        echo "<p>✅ Cleanup successful</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Database operation failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460; margin: 20px 0;'>";
    echo "<h3>🔧 Troubleshooting Tips</h3>";
    echo "<ul>";
    echo "<li>Check if the form is submitting with action='update_rates'</li>";
    echo "<li>Verify that the rates array is properly structured</li>";
    echo "<li>Ensure JavaScript is not interfering with form submission</li>";
    echo "<li>Check browser developer tools for any JavaScript errors</li>";
    echo "<li>Verify that the date field has a valid value</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Quick Links:</h3>";
    echo "<ul>";
    echo "<li><a href='metal-rates.php' target='_blank'>📊 Metal Rates Page</a></li>";
    echo "<li><a href='index.php' target='_blank'>🏠 Dashboard</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
