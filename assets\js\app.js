/**
 * Indian Jewellery Wholesale Management System v2.0
 * Main Application JavaScript
 */

class JewelleryApp {
    constructor() {
        this.sidebar = null;
        this.mainContent = null;
        this.sidebarOverlay = null;
        this.isMobile = window.innerWidth <= 1024;
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initSidebar();
        this.initEventListeners();
        this.initTooltips();
        this.initNotifications();
        this.restoreSidebarState();
    }
    
    initElements() {
        this.sidebar = document.getElementById('sidebar');
        this.mainContent = document.getElementById('mainContent');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    }
    
    initSidebar() {
        // Initialize submenu toggles
        const submenuToggles = document.querySelectorAll('[data-submenu-toggle]');
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSubmenu(toggle.parentElement);
            });
        });
        
        // Set initial sidebar state
        if (this.isMobile) {
            this.sidebar?.classList.remove('collapsed');
            this.mainContent?.classList.remove('sidebar-collapsed');
        }
    }
    
    initEventListeners() {
        // Sidebar toggle buttons
        this.sidebarToggle?.addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        this.mobileSidebarToggle?.addEventListener('click', () => {
            this.toggleMobileSidebar();
        });
        
        // Overlay click to close mobile sidebar
        this.sidebarOverlay?.addEventListener('click', () => {
            this.closeMobileSidebar();
        });
        
        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // Close mobile sidebar when clicking nav links
        const navLinks = document.querySelectorAll('.sidebar .nav-link:not([data-submenu-toggle])');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (this.isMobile) {
                    this.closeMobileSidebar();
                }
            });
        });
    }
    
    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
            return;
        }
        
        const isCollapsed = this.sidebar?.classList.contains('collapsed');
        
        if (isCollapsed) {
            this.sidebar?.classList.remove('collapsed');
            this.mainContent?.classList.remove('sidebar-collapsed');
        } else {
            this.sidebar?.classList.add('collapsed');
            this.mainContent?.classList.add('sidebar-collapsed');
        }
        
        // Save state to localStorage
        localStorage.setItem('sidebarCollapsed', !isCollapsed);
        
        // Trigger custom event
        this.dispatchEvent('sidebarToggle', { collapsed: !isCollapsed });
    }
    
    toggleMobileSidebar() {
        const isOpen = this.sidebar?.classList.contains('mobile-open');
        
        if (isOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }
    
    openMobileSidebar() {
        this.sidebar?.classList.add('mobile-open');
        this.sidebarOverlay?.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        this.dispatchEvent('mobileSidebarOpen');
    }
    
    closeMobileSidebar() {
        this.sidebar?.classList.remove('mobile-open');
        this.sidebarOverlay?.classList.remove('active');
        document.body.style.overflow = '';
        
        this.dispatchEvent('mobileSidebarClose');
    }
    
    toggleSubmenu(navItem) {
        const isOpen = navItem.classList.contains('open');
        
        // Close all other submenus
        const allSubmenus = document.querySelectorAll('.nav-item.has-submenu.open');
        allSubmenus.forEach(item => {
            if (item !== navItem) {
                item.classList.remove('open');
            }
        });
        
        // Toggle current submenu
        if (isOpen) {
            navItem.classList.remove('open');
        } else {
            navItem.classList.add('open');
        }
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 1024;
        
        if (wasMobile !== this.isMobile) {
            if (this.isMobile) {
                // Switched to mobile
                this.closeMobileSidebar();
                this.sidebar?.classList.remove('collapsed');
                this.mainContent?.classList.remove('sidebar-collapsed');
            } else {
                // Switched to desktop
                this.closeMobileSidebar();
                this.restoreSidebarState();
            }
        }
    }
    
    restoreSidebarState() {
        if (!this.isMobile) {
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                this.sidebar?.classList.add('collapsed');
                this.mainContent?.classList.add('sidebar-collapsed');
            }
        }
    }
    
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + B to toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebar();
        }
        
        // Ctrl/Cmd + N for new sale
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = 'billing.php';
        }
        
        // Escape to close mobile sidebar
        if (e.key === 'Escape') {
            this.closeMobileSidebar();
        }
    }
    
    initTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    initNotifications() {
        // Real-time notification system (disabled demo notifications)
        // Only show actual business notifications from the server
        this.checkForNewNotifications();

        // Check for new notifications every 5 minutes instead of showing demo ones
        setInterval(() => {
            this.checkForNewNotifications();
        }, 5 * 60 * 1000);
    }

    checkForNewNotifications() {
        // Check server for new notifications
        const basePath = window.basePath || '';
        fetch(basePath + 'ajax/check-notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.hasNew) {
                    // Only show notification if there are genuinely new ones
                    this.showNotification(data.message, data.type);
                }
            })
            .catch(error => {
                console.log('Notification check failed:', error);
            });
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 400px;
            box-shadow: var(--shadow-lg);
        `;
        
        const iconMap = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            danger: 'exclamation-circle',
            info: 'info-circle'
        };
        
        notification.innerHTML = `
            <i class="fas fa-${iconMap[type] || 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
        
        // Update notification badge
        this.updateNotificationBadge();
    }
    
    updateNotificationBadge(count = null) {
        const badge = document.querySelector('#notificationsDropdown .badge');
        if (badge) {
            if (count !== null) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline' : 'none';
            } else {
                // Increment current count
                const currentCount = parseInt(badge.textContent) || 0;
                badge.textContent = currentCount + 1;
                badge.style.display = 'inline';
            }
        }
    }
    
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
    
    // Utility methods
    static formatCurrency(amount) {
        return '₹' + parseFloat(amount).toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    static formatWeight(weight) {
        return parseFloat(weight).toFixed(3) + ' gm';
    }
    
    static formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN');
    }
    
    static formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('en-IN');
    }
    
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.jewelleryApp = new JewelleryApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JewelleryApp;
}
