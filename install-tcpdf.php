<?php
/**
 * Automatic TCPDF Installation
 */

set_time_limit(300); // 5 minutes for download

echo "<h2>TCPDF Installation</h2>";

$vendorDir = 'vendor';
$tcpdfDir = $vendorDir . '/tecnickcom/tcpdf';
$tcpdfFile = $tcpdfDir . '/tcpdf.php';

// Check if already installed
if (file_exists($tcpdfFile)) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ TCPDF Already Installed</h3>";
    echo "<p>TCPDF is already available at: <code>$tcpdfFile</code></p>";
    echo "<p><a href='test-pdf-generation.php'>Test PDF Generation</a></p>";
    echo "</div>";
    exit;
}

echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📦 Installing TCPDF</h3>";
echo "<p>This will download and install TCPDF library for professional PDF generation.</p>";
echo "</div>";

// Create vendor directories
if (!is_dir($vendorDir)) {
    mkdir($vendorDir, 0755, true);
    echo "✅ Created vendor directory<br>";
}

if (!is_dir($tcpdfDir)) {
    mkdir($tcpdfDir, 0755, true);
    echo "✅ Created TCPDF directory<br>";
}

// Download TCPDF
$tcpdfUrl = 'https://github.com/tecnickcom/TCPDF/archive/refs/tags/6.6.2.zip';
$zipFile = $vendorDir . '/tcpdf.zip';

echo "<p>📥 Downloading TCPDF from GitHub...</p>";

// Download using file_get_contents (simple method)
$context = stream_context_create([
    'http' => [
        'timeout' => 60,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$zipContent = @file_get_contents($tcpdfUrl, false, $context);

if ($zipContent === false) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Download Failed</h4>";
    echo "<p>Could not download TCPDF automatically. Please install manually:</p>";
    echo "<ol>";
    echo "<li>Download TCPDF from <a href='https://tcpdf.org/'>https://tcpdf.org/</a></li>";
    echo "<li>Extract to <code>vendor/tecnickcom/tcpdf/</code></li>";
    echo "<li>Ensure <code>tcpdf.php</code> is at <code>vendor/tecnickcom/tcpdf/tcpdf.php</code></li>";
    echo "</ol>";
    echo "<p><strong>Alternative:</strong> Use Composer: <code>composer require tecnickcom/tcpdf</code></p>";
    echo "</div>";
    exit;
}

// Save zip file
file_put_contents($zipFile, $zipContent);
echo "✅ Downloaded TCPDF zip file<br>";

// Extract zip file
if (class_exists('ZipArchive')) {
    $zip = new ZipArchive;
    if ($zip->open($zipFile) === TRUE) {
        echo "<p>📂 Extracting TCPDF...</p>";
        
        // Extract to temporary directory first
        $tempDir = $vendorDir . '/tcpdf_temp';
        $zip->extractTo($tempDir);
        $zip->close();
        
        // Find the extracted directory (it will have a version suffix)
        $extractedDirs = glob($tempDir . '/TCPDF-*');
        if (count($extractedDirs) > 0) {
            $sourceDir = $extractedDirs[0];
            
            // Copy files to the correct location
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($sourceDir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::SELF_FIRST
            );
            
            foreach ($iterator as $item) {
                $target = $tcpdfDir . '/' . $iterator->getSubPathName();
                if ($item->isDir()) {
                    if (!is_dir($target)) {
                        mkdir($target, 0755, true);
                    }
                } else {
                    copy($item, $target);
                }
            }
            
            // Clean up
            removeDirectory($tempDir);
            unlink($zipFile);
            
            echo "✅ TCPDF extracted successfully<br>";
            
            // Verify installation
            if (file_exists($tcpdfFile)) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h3>🎉 Installation Successful!</h3>";
                echo "<p>TCPDF has been installed successfully at: <code>$tcpdfFile</code></p>";
                echo "<p>You can now generate professional PDF documents.</p>";
                echo "</div>";
                
                echo "<div style='text-align: center; margin: 20px 0;'>";
                echo "<a href='test-pdf-generation.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test PDF Generation</a>";
                echo "<a href='sales.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Go to Sales</a>";
                echo "</div>";
                
            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
                echo "<h4>❌ Installation Verification Failed</h4>";
                echo "<p>The files were extracted but tcpdf.php was not found at the expected location.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Extraction Error</h4>";
            echo "<p>Could not find extracted TCPDF directory.</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Zip Extraction Failed</h4>";
        echo "<p>Could not open the downloaded zip file.</p>";
        echo "</div>";
    }
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ ZipArchive Not Available</h4>";
    echo "<p>PHP ZipArchive extension is not available. Please install TCPDF manually.</p>";
    echo "</div>";
}

// Helper function to remove directory recursively
function removeDirectory($dir) {
    if (!is_dir($dir)) return;
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? removeDirectory($path) : unlink($path);
    }
    rmdir($dir);
}
?>
