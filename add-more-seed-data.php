<?php
/**
 * Add More Comprehensive Seed Data
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h1>🔧 Adding More Comprehensive Seed Data</h1>";
    
    // Add more suppliers
    echo "<h2>🚚 Adding More Suppliers...</h2>";
    $suppliers = [
        ['Nafa Gold', 'Am<PERSON> Shah', '**********', '<EMAIL>', '456 Gold Plaza, Surat', 'Surat', 'Gujarat', '395001', '24**********2Z6', '**********', 'ICICI Bank', '***********', 'ICIC0002345', 150000.00, 15, 1],
        ['Surat Jewellers', '<PERSON>sh Patel', '**********', '<EMAIL>', '789 Diamond Market, Surat', 'Surat', 'Gujarat', '395002', '24**********3Z7', '**********', 'SBI Bank', '***********', 'SBIN0003456', 300000.00, 45, 1],
        ['<PERSON> Jewels', '<PERSON>', '**********', 'k<PERSON><PERSON><PERSON>@kjewels.com', '654 Krishna Plaza, Coimbatore', 'Coimbatore', 'Tamil Nadu', '641001', '33**********5Z9', '**********', 'Indian Bank', '***********', 'IDIB0005678', 250000.00, 30, 1]
    ];
    
    foreach ($suppliers as $supplier) {
        $stmt = $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $supplier);
        echo "<p>✅ Added supplier: {$supplier[0]}</p>";
    }
    
    // Add more products
    echo "<h2>💎 Adding More Products...</h2>";
    $products = [
        [26, 2, 'Gold Bangles Set', 'BG001', 'Traditional gold bangles set', 'Gold', '22K', 12.340, 0.000, 12.340, 0, 850, 5800, '********', 1],
        [27, 3, 'Diamond Studs', 'ST001', 'Diamond studded gold earrings', 'Gold', '18K', 2.040, 0.500, 1.540, 1500, 600, 4650, '********', 1],
        [28, 4, 'Gold Ring', 'RG001', 'Traditional gold ring', 'Gold', '22K', 5.250, 0.250, 5.000, 800, 400, 5800, '********', 1],
        [26, 5, 'Gold Necklace', 'NK001', 'Heavy gold necklace', 'Gold', '22K', 25.500, 0.000, 25.500, 0, 1200, 5800, '********', 1],
        [27, 6, 'Diamond Pendant', 'PD001', 'Diamond pendant with chain', 'Gold', '18K', 8.500, 1.200, 7.300, 2500, 800, 4650, '********', 1]
    ];
    
    foreach ($products as $product) {
        $stmt = $db->query("INSERT INTO products (supplier_id, category_id, product_name, product_code, description, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $product);
        $productId = $db->lastInsertId();
        
        // Create inventory for each product
        $stock = rand(15, 50);
        $metal_value = $product[9] * $product[12]; // net_weight * gold_rate
        $total_cost = $metal_value + $product[10] + $product[11]; // metal + stone + making
        $selling_price = $total_cost * 1.25; // 25% markup
        
        $stmt = $db->query("INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, minimum_stock_level, reorder_point, location, rack_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [
            $productId, $stock, $total_cost, $selling_price, 5, 10, 'A-01', 'R-' . str_pad($productId, 3, '0', STR_PAD_LEFT)
        ]);
        
        echo "<p>✅ Added product: {$product[2]} - Stock: $stock</p>";
    }
    
    // Add more customers
    echo "<h2>👥 Adding More Customers...</h2>";
    $customers = [
        ['Krishna Jewels Retail', 'Krishna Retail Hub', 'business', '9876543221', '<EMAIL>', '456 Market Road, Bangalore', 'Bangalore', 'Karnataka', '560001', '29HIJKL8901M8Z2', 'HIJKL8901M', '', 300000.00, 30, 3.0, 1],
        ['Dhanpal Jewels Store', 'Dhanpal Retail', 'business', '9876543222', '<EMAIL>', '789 Commercial Street, Hyderabad', 'Hyderabad', 'Telangana', '500001', '36IJKLM9012N9Z3', 'IJKLM9012N', '', 200000.00, 20, 2.5, 1],
        ['Priya Sharma', '', 'individual', '9876543223', '<EMAIL>', '321 Residential Area, Mumbai', 'Mumbai', 'Maharashtra', '400001', '', '', '1***********', 0, 0, 0, 1],
        ['Rahul Gupta', '', 'individual', '9876543224', '<EMAIL>', '654 Housing Society, Pune', 'Pune', 'Maharashtra', '411001', '', '', '************', 50000.00, 15, 0, 1],
        ['Sunita Jewellers', 'Sunita Retail Chain', 'business', '9876543225', '<EMAIL>', '987 Main Market, Delhi', 'Delhi', 'Delhi', '110001', '07**********0Z4', '**********', '', 400000.00, 35, 4.0, 1]
    ];
    
    foreach ($customers as $customer) {
        $stmt = $db->query("INSERT INTO customers (customer_name, business_name, customer_type, phone, email, address, city, state, pincode, gst_number, pan_number, aadhar_number, credit_limit, credit_days, discount_percentage, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $customer);
        echo "<p>✅ Added customer: {$customer[0]}</p>";
    }
    
    // Add more sales transactions
    echo "<h2>🛒 Adding More Sales Transactions...</h2>";
    
    // Sale 2: Bangles to Krishna Jewels
    $billNumber2 = 'BILL-' . date('Ymd') . '-0002';
    $subtotal2 = 71572.00; // 12.340 * 5800
    $discountAmount2 = 2147.16; // 3%
    $taxAmount2 = 2147.16; // 3%
    $grandTotal2 = $subtotal2 - $discountAmount2 + $taxAmount2;
    
    $stmt = $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $billNumber2, 5, date('Y-m-d'), '14:30:00', $subtotal2, 'percentage', 3.00, $discountAmount2, $taxAmount2, $grandTotal2, 'card', 'paid', 1, 0
    ]);
    
    $saleId2 = $db->lastInsertId();
    
    // Add sale item for bangles
    $stmt = $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $saleId2, 11, 1, 12.340, 12.340, 0, 850, 5800, 72422, 72422, 3.00, 2172.66, 74594.66
    ]);
    
    echo "<p>✅ Added sale: $billNumber2 - ₹" . number_format($grandTotal2, 2) . "</p>";
    
    // Sale 3: Individual customer buying earrings
    $billNumber3 = 'BILL-' . date('Ymd') . '-0003';
    $subtotal3 = 8651.00; // (1.540 * 4650) + 1500 + 600
    $discountAmount3 = 0; // No discount for individual
    $taxAmount3 = 259.53; // 3%
    $grandTotal3 = $subtotal3 + $taxAmount3;
    
    $stmt = $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $billNumber3, 6, date('Y-m-d'), '16:45:00', $subtotal3, 'none', 0, $discountAmount3, $taxAmount3, $grandTotal3, 'cash', 'paid', 1, 0
    ]);
    
    $saleId3 = $db->lastInsertId();
    
    // Add sale item for earrings
    $stmt = $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
        $saleId3, 12, 1, 2.040, 2.040, 1500, 600, 4650, 8751, 8751, 3.00, 262.53, 9013.53
    ]);
    
    echo "<p>✅ Added sale: $billNumber3 - ₹" . number_format($grandTotal3, 2) . "</p>";
    
    // Update inventory for the sales
    $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - 1 WHERE product_id = 11");
    $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - 1 WHERE product_id = 12");
    
    // Add stock movements
    $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) VALUES (11, 'out', 1, 'sale', ?, 'Sale transaction', 1)", [$saleId2]);
    $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) VALUES (12, 'out', 1, 'sale', ?, 'Sale transaction', 1)", [$saleId3]);
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; color: #155724; margin: 30px 0;'>";
    echo "<h2>🎉 Comprehensive Seed Data Added Successfully!</h2>";
    echo "<p>Total data now includes:</p>";
    echo "<ul>";
    echo "<li>✅ 4 Suppliers with complete business details</li>";
    echo "<li>✅ 6 Products across different categories</li>";
    echo "<li>✅ 9 Customers (6 business, 3 individual)</li>";
    echo "<li>✅ 3 Sales transactions with different scenarios</li>";
    echo "<li>✅ Complete inventory and stock movement tracking</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Test All Pages Now:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $test_pages = [
        'index.php' => '📊 Dashboard',
        'products.php' => '💎 Products',
        'inventory.php' => '📦 Inventory', 
        'suppliers.php' => '🚚 Suppliers',
        'customers.php' => '👥 Customers',
        'sales.php' => '📊 Sales',
        'billing.php' => '🛒 New Sale',
        'metal-rates.php' => '💰 Metal Rates',
        'categories.php' => '📂 Categories',
        'settings.php' => '⚙️ Settings',
        'users.php' => '👤 Users'
    ];
    
    foreach ($test_pages as $page => $title) {
        echo "<div style='background: #e9ecef; padding: 10px; border-radius: 5px; text-align: center;'>";
        echo "<a href='$page' target='_blank' style='text-decoration: none; color: #495057; font-weight: bold;'>$title</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Adding Seed Data</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
