<?php
/**
 * Cleanup Demo Notifications - Indian Jewellery Wholesale Management System v2.0
 * Run this script once to remove frequent demo notifications
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>Cleaning up demo notifications...</h2>";
    
    // Delete frequent demo notifications
    $demoPatterns = [
        'New sale completed - Bill #BILL-%',
        'Low stock alert for Gold Chain GC001',
        'Demo notification%',
        'Test notification%'
    ];
    
    $totalDeleted = 0;
    
    foreach ($demoPatterns as $pattern) {
        $result = $db->query("DELETE FROM notifications WHERE title LIKE ? OR message LIKE ?", [$pattern, $pattern]);
        $deleted = $result; // Number of affected rows
        $totalDeleted += $deleted;
        echo "<p>Deleted $deleted notifications matching pattern: $pattern</p>";
    }
    
    // Also delete notifications created in the last hour (likely demo ones)
    $recentDemo = $db->query("
        DELETE FROM notifications 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
        AND (title LIKE '%sale completed%' OR title LIKE '%demo%' OR title LIKE '%test%')
    ");
    
    $totalDeleted += $recentDemo;
    echo "<p>Deleted $recentDemo recent demo notifications</p>";
    
    // Update notification settings to minimal frequency
    $settings = [
        'notification_frequency' => 'minimal',
        'low_stock_alerts' => '1',
        'sales_milestones' => '0', // Disable sales milestone notifications
        'payment_reminders' => '1',
        'inventory_alerts' => '1',
        'system_updates' => '1',
        'auto_cleanup_days' => '7' // Clean up after 7 days instead of 30
    ];
    
    foreach ($settings as $key => $value) {
        $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
        
        if ($existing) {
            $db->query("UPDATE settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
        } else {
            $db->query("
                INSERT INTO settings (setting_key, setting_value, setting_type, description, category) 
                VALUES (?, ?, 'notification', ?, 'notifications')
            ", [$key, $value, "Notification setting: $key"]);
        }
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Cleanup Complete!</h3>";
    echo "<p><strong>Total notifications deleted:</strong> $totalDeleted</p>";
    echo "<p><strong>Settings updated:</strong></p>";
    echo "<ul>";
    echo "<li>Notification frequency set to 'minimal'</li>";
    echo "<li>Sales milestone notifications disabled</li>";
    echo "<li>Auto-cleanup period set to 7 days</li>";
    echo "<li>Only critical alerts will be shown</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Dashboard</a></p>";
    echo "<p><a href='notification-settings.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Notification Settings</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Failed to cleanup notifications: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification Cleanup - <?php echo APP_NAME ?? 'Jewellery Management System'; ?></title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f8f9fa;
        }
        h2 { color: #333; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h1>🔔 Notification System Optimized</h1>
        <p>The notification system has been optimized to reduce frequency and show only important alerts.</p>
        
        <h3>What was changed:</h3>
        <ul>
            <li>✅ Removed demo/test notifications that were showing frequently</li>
            <li>✅ Set notification frequency to "minimal" (only critical alerts)</li>
            <li>✅ Disabled sales milestone notifications (they were too frequent)</li>
            <li>✅ Reduced auto-cleanup period to 7 days</li>
            <li>✅ Updated JavaScript to stop generating demo notifications</li>
            <li>✅ Added proper notification checking system</li>
        </ul>
        
        <h3>Going forward:</h3>
        <ul>
            <li>🔔 You'll only see critical notifications (low stock, payment issues, etc.)</li>
            <li>⏰ Notifications are checked every 5 minutes instead of constantly</li>
            <li>🧹 Old notifications are automatically cleaned up after 7 days</li>
            <li>⚙️ You can customize these settings in the Notification Settings page</li>
        </ul>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.php" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;">
                🏠 Go to Dashboard
            </a>
            <a href="notification-settings.php" style="background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;">
                ⚙️ Notification Settings
            </a>
        </div>
    </div>
</body>
</html>
