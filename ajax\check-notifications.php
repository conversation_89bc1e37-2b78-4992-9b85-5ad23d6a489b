<?php
/**
 * Check for New Notifications - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';
require_once '../includes/notifications.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'] ?? null;
    
    // Get the last check time from session or use 5 minutes ago
    $lastCheck = $_SESSION['last_notification_check'] ?? date('Y-m-d H:i:s', strtotime('-5 minutes'));
    
    // Check for new notifications since last check
    $sql = "SELECT COUNT(*) as count FROM notifications WHERE created_at > ? AND is_read = 0";
    $params = [$lastCheck];
    
    if ($user_id) {
        $sql .= " AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_id;
    } else {
        $sql .= " AND user_id IS NULL";
    }
    
    $sql .= " AND (expires_at IS NULL OR expires_at > NOW())";
    
    $newCount = $db->fetch($sql, $params)['count'];
    
    // Update last check time
    $_SESSION['last_notification_check'] = date('Y-m-d H:i:s');
    
    if ($newCount > 0) {
        // Get the latest notification for display
        $sql = "SELECT * FROM notifications WHERE created_at > ? AND is_read = 0";
        $params = [$lastCheck];
        
        if ($user_id) {
            $sql .= " AND (user_id = ? OR user_id IS NULL)";
            $params[] = $user_id;
        } else {
            $sql .= " AND user_id IS NULL";
        }
        
        $sql .= " AND (expires_at IS NULL OR expires_at > NOW()) ORDER BY created_at DESC LIMIT 1";
        
        $latestNotification = $db->fetch($sql, $params);
        
        echo json_encode([
            'success' => true,
            'hasNew' => true,
            'count' => $newCount,
            'message' => $latestNotification['title'] ?? 'New notification',
            'type' => $latestNotification['type'] ?? 'info',
            'details' => $latestNotification['message'] ?? ''
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'hasNew' => false,
            'count' => 0
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
