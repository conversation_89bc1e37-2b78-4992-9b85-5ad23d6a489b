<?php
/**
 * Metal Rate Analytics AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Get current rates
    $currentRates = $db->fetchAll("
        SELECT metal_type, purity, rate_per_gram 
        FROM metal_rates 
        WHERE rate_date = CURDATE() AND is_active = 1
        ORDER BY metal_type, purity
    ");
    
    // Get previous day rates for comparison
    $previousRates = $db->fetchAll("
        SELECT metal_type, purity, rate_per_gram 
        FROM metal_rates 
        WHERE rate_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND is_active = 1
        ORDER BY metal_type, purity
    ");
    
    // Create lookup for previous rates
    $prevRatesLookup = [];
    foreach ($previousRates as $rate) {
        $key = $rate['metal_type'] . '_' . $rate['purity'];
        $prevRatesLookup[$key] = $rate['rate_per_gram'];
    }
    
    // Get rate trends for last 30 days
    $trends = $db->fetchAll("
        SELECT 
            rate_date,
            metal_type,
            purity,
            rate_per_gram
        FROM metal_rates 
        WHERE rate_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) 
        AND is_active = 1
        ORDER BY rate_date DESC, metal_type, purity
    ");
    
    // Get rate statistics
    $statistics = [];
    
    // Highest and lowest rates
    $goldStats = $db->fetch("
        SELECT 
            MAX(rate_per_gram) as highest,
            MIN(rate_per_gram) as lowest,
            AVG(rate_per_gram) as average
        FROM metal_rates 
        WHERE metal_type = 'Gold' 
        AND rate_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND is_active = 1
    ");
    
    $silverStats = $db->fetch("
        SELECT 
            MAX(rate_per_gram) as highest,
            MIN(rate_per_gram) as lowest,
            AVG(rate_per_gram) as average
        FROM metal_rates 
        WHERE metal_type = 'Silver' 
        AND rate_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND is_active = 1
    ");
    
    if ($goldStats) {
        $statistics[] = [
            'label' => 'Gold (30d High)',
            'value' => '₹' . number_format($goldStats['highest'], 2)
        ];
        $statistics[] = [
            'label' => 'Gold (30d Low)',
            'value' => '₹' . number_format($goldStats['lowest'], 2)
        ];
        $statistics[] = [
            'label' => 'Gold (30d Avg)',
            'value' => '₹' . number_format($goldStats['average'], 2)
        ];
    }
    
    if ($silverStats) {
        $statistics[] = [
            'label' => 'Silver (30d High)',
            'value' => '₹' . number_format($silverStats['highest'], 2)
        ];
        $statistics[] = [
            'label' => 'Silver (30d Low)',
            'value' => '₹' . number_format($silverStats['lowest'], 2)
        ];
        $statistics[] = [
            'label' => 'Silver (30d Avg)',
            'value' => '₹' . number_format($silverStats['average'], 2)
        ];
    }
    
    // Generate price alerts
    $alerts = [];
    
    foreach ($currentRates as $rate) {
        $key = $rate['metal_type'] . '_' . $rate['purity'];
        $currentRate = $rate['rate_per_gram'];
        $previousRate = $prevRatesLookup[$key] ?? null;
        
        if ($previousRate) {
            $change = $currentRate - $previousRate;
            $changePercent = ($change / $previousRate) * 100;
            
            if (abs($changePercent) > 2) { // Alert for changes > 2%
                $alerts[] = [
                    'type' => $change > 0 ? 'success' : 'danger',
                    'icon' => $change > 0 ? 'arrow-up' : 'arrow-down',
                    'message' => $rate['metal_type'] . ' ' . $rate['purity'] . ' ' . 
                               ($change > 0 ? 'increased' : 'decreased') . ' by ' . 
                               number_format(abs($changePercent), 2) . '% (₹' . 
                               number_format(abs($change), 2) . ')'
                ];
            }
        }
    }
    
    // Add general alerts if no specific alerts
    if (empty($alerts)) {
        $alerts[] = [
            'type' => 'info',
            'icon' => 'info-circle',
            'message' => 'Metal rates are stable with minimal changes from yesterday'
        ];
    }
    
    // Generate market insights
    $insights = [];
    
    // Calculate volatility
    $goldVolatility = $db->fetch("
        SELECT STDDEV(rate_per_gram) as volatility
        FROM metal_rates 
        WHERE metal_type = 'Gold' 
        AND purity = '24K'
        AND rate_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        AND is_active = 1
    ");
    
    if ($goldVolatility && $goldVolatility['volatility']) {
        $volatilityLevel = $goldVolatility['volatility'] > 100 ? 'High' : 
                          ($goldVolatility['volatility'] > 50 ? 'Medium' : 'Low');
        
        $insights[] = [
            'title' => 'Gold Market Volatility',
            'description' => "Current volatility is $volatilityLevel (₹" . 
                           number_format($goldVolatility['volatility'], 2) . " std dev)"
        ];
    }
    
    // Weekly trend
    $weeklyTrend = $db->fetch("
        SELECT 
            (SELECT rate_per_gram FROM metal_rates 
             WHERE metal_type = 'Gold' AND purity = '24K' 
             AND rate_date = CURDATE() AND is_active = 1) as current_rate,
            (SELECT rate_per_gram FROM metal_rates 
             WHERE metal_type = 'Gold' AND purity = '24K' 
             AND rate_date = DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND is_active = 1) as week_ago_rate
    ");
    
    if ($weeklyTrend && $weeklyTrend['current_rate'] && $weeklyTrend['week_ago_rate']) {
        $weeklyChange = $weeklyTrend['current_rate'] - $weeklyTrend['week_ago_rate'];
        $weeklyChangePercent = ($weeklyChange / $weeklyTrend['week_ago_rate']) * 100;
        
        $trendDirection = $weeklyChange > 0 ? 'upward' : 'downward';
        $insights[] = [
            'title' => 'Weekly Trend',
            'description' => "Gold 24K shows $trendDirection trend with " . 
                           number_format(abs($weeklyChangePercent), 2) . "% change over 7 days"
        ];
    }
    
    // Format trends data for chart
    $trendData = [];
    $trendsByMetal = [];
    
    foreach ($trends as $trend) {
        $key = $trend['metal_type'] . ' ' . $trend['purity'];
        if (!isset($trendsByMetal[$key])) {
            $trendsByMetal[$key] = [];
        }
        $trendsByMetal[$key][] = [
            'date' => $trend['rate_date'],
            'rate' => $trend['rate_per_gram']
        ];
    }
    
    // Return analytics data
    echo json_encode([
        'success' => true,
        'statistics' => $statistics,
        'alerts' => $alerts,
        'insights' => $insights,
        'trends' => $trendsByMetal,
        'currentRates' => $currentRates,
        'summary' => [
            'totalMetals' => count(array_unique(array_column($currentRates, 'metal_type'))),
            'totalRates' => count($currentRates),
            'lastUpdate' => date('d/m/Y H:i:s'),
            'dataPoints' => count($trends)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
