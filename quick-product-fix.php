<?php
/**
 * Quick Product Creation Fix - Identify and Resolve Performance Issues
 */

require_once 'config/database.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $db = getDB();
    
    echo "<h2>🔧 Quick Product Creation Fix</h2>";
    
    // Test 1: Simple database connection test
    echo "<h3>⚡ Test 1: Database Performance</h3>";
    
    $start_time = microtime(true);
    
    try {
        $result = $db->fetch("SELECT 1 as test");
        $db_time = microtime(true) - $start_time;
        
        if ($db_time < 0.1) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Database Connection Fast</h4>";
            echo "<p>Query time: " . round($db_time * 1000, 2) . "ms</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h4>⚠️ Database Connection Slow</h4>";
            echo "<p>Query time: " . round($db_time * 1000, 2) . "ms (should be < 100ms)</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Database Connection Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Test 2: Check if ProductCodeGenerator is causing delays
    echo "<h3>🏷️ Test 2: Product Code Generator Performance</h3>";
    
    $start_time = microtime(true);
    
    try {
        if (file_exists('lib/ProductCodeGenerator.php')) {
            require_once 'lib/ProductCodeGenerator.php';
            $generator = new ProductCodeGenerator($db);
            $testCode = $generator->generateProductCode("Test Product", null, "Gold");
            $gen_time = microtime(true) - $start_time;
            
            if ($gen_time < 0.5) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
                echo "<h4>✅ Code Generator Fast</h4>";
                echo "<p>Generated code: <strong>$testCode</strong></p>";
                echo "<p>Generation time: " . round($gen_time * 1000, 2) . "ms</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
                echo "<h4>⚠️ Code Generator Slow</h4>";
                echo "<p>Generated code: <strong>$testCode</strong></p>";
                echo "<p>Generation time: " . round($gen_time * 1000, 2) . "ms (should be < 500ms)</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ ProductCodeGenerator Missing</h4>";
            echo "<p>File lib/ProductCodeGenerator.php not found</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Code Generator Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Test 3: Simple product creation without complex logic
    echo "<h3>➕ Test 3: Simple Product Creation</h3>";
    
    if ($_POST && isset($_POST['create_simple_product'])) {
        $start_time = microtime(true);
        
        try {
            // Simple product creation without ProductCodeGenerator
            $simple_code = 'TEST' . time();
            
            $sql = "INSERT INTO products (product_name, product_code, metal_type, tunch_percentage, gross_weight, stone_weight, net_weight) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $_POST['product_name'],
                $simple_code,
                $_POST['metal_type'],
                floatval($_POST['tunch_percentage']),
                floatval($_POST['gross_weight']),
                floatval($_POST['stone_weight']),
                floatval($_POST['gross_weight']) - floatval($_POST['stone_weight'])
            ];
            
            $db->execute($sql, $params);
            $product_id = $db->lastInsertId();
            
            // Simple inventory creation
            $db->execute("INSERT INTO inventory (product_id, quantity_in_stock) VALUES (?, 0)", [$product_id]);
            
            $creation_time = microtime(true) - $start_time;
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>🎉 Simple Product Created Successfully!</h4>";
            echo "<p><strong>Product ID:</strong> $product_id</p>";
            echo "<p><strong>Product Code:</strong> $simple_code</p>";
            echo "<p><strong>Creation Time:</strong> " . round($creation_time * 1000, 2) . "ms</p>";
            echo "</div>";
            
            // Clean up
            $db->execute("DELETE FROM inventory WHERE product_id = ?", [$product_id]);
            $db->execute("DELETE FROM products WHERE id = ?", [$product_id]);
            
            echo "<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            echo "<small>✅ Test product cleaned up</small>";
            echo "</div>";
            
        } catch (Exception $e) {
            $creation_time = microtime(true) - $start_time;
            
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Simple Product Creation Failed</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "<p>Time before error: " . round($creation_time * 1000, 2) . "ms</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST' style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<input type='hidden' name='create_simple_product' value='1'>";
        echo "<div style='margin-bottom: 10px;'>";
        echo "<input type='text' name='product_name' placeholder='Test Product Name' required style='width: 200px; padding: 5px; margin: 5px;'>";
        echo "<select name='metal_type' required style='padding: 5px; margin: 5px;'>";
        echo "<option value='Gold'>Gold</option>";
        echo "<option value='Silver'>Silver</option>";
        echo "</select>";
        echo "</div>";
        echo "<div style='margin-bottom: 10px;'>";
        echo "<input type='number' name='tunch_percentage' placeholder='91.6' step='0.01' value='91.6' required style='width: 100px; padding: 5px; margin: 5px;'>";
        echo "<input type='number' name='gross_weight' placeholder='10.5' step='0.001' value='10.5' required style='width: 100px; padding: 5px; margin: 5px;'>";
        echo "<input type='number' name='stone_weight' placeholder='1.2' step='0.001' value='1.2' required style='width: 100px; padding: 5px; margin: 5px;'>";
        echo "</div>";
        echo "<button type='submit' style='background-color: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 3px;'>Create Simple Test Product</button>";
        echo "</form>";
    }
    
    // Test 4: Check database table structure
    echo "<h3>🗄️ Test 4: Database Table Check</h3>";
    
    try {
        $tables = ['products', 'inventory'];
        
        foreach ($tables as $table) {
            $start_time = microtime(true);
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            $query_time = microtime(true) - $start_time;
            
            if ($query_time < 0.1) {
                echo "<p>✅ <strong>$table:</strong> $count records (Query: " . round($query_time * 1000, 2) . "ms)</p>";
            } else {
                echo "<p>⚠️ <strong>$table:</strong> $count records (Query: " . round($query_time * 1000, 2) . "ms - SLOW)</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Database Table Check Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Quick Fix Recommendations
    echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🔧 Quick Fix Recommendations</h3>";
    echo "<ol>";
    echo "<li><strong>Disable ProductCodeGenerator temporarily:</strong> Use simple sequential codes</li>";
    echo "<li><strong>Simplify product creation:</strong> Remove complex validations temporarily</li>";
    echo "<li><strong>Check database indexes:</strong> Ensure product_code has proper index</li>";
    echo "<li><strong>Enable error logging:</strong> Check for silent errors</li>";
    echo "<li><strong>Test with minimal data:</strong> Use only required fields</li>";
    echo "</ol>";
    echo "</div>";
    
    // Create simplified product form
    echo "<h3>🚀 Simplified Product Creation Form</h3>";
    
    if ($_POST && isset($_POST['create_fast_product'])) {
        try {
            $fast_code = 'FAST' . rand(100, 999);
            
            $sql = "INSERT INTO products (product_name, product_code, metal_type, tunch_percentage) VALUES (?, ?, ?, ?)";
            $params = [$_POST['fast_name'], $fast_code, $_POST['fast_metal'], $_POST['fast_tunch']];
            
            $db->execute($sql, $params);
            $product_id = $db->lastInsertId();
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>🎉 Fast Product Created!</h4>";
            echo "<p>Product ID: $product_id | Code: $fast_code</p>";
            echo "<a href='products.php' style='background-color: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>View Products</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Fast Product Creation Failed</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST' style='background-color: #f0f8ff; padding: 20px; border-radius: 5px; border: 2px solid #007bff;'>";
        echo "<h4>⚡ Ultra-Fast Product Creation</h4>";
        echo "<input type='hidden' name='create_fast_product' value='1'>";
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px;'>Product Name:</label>";
        echo "<input type='text' name='fast_name' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' placeholder='Quick Test Product'>";
        echo "</div>";
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px;'>Metal:</label>";
        echo "<select name='fast_metal' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
        echo "<option value='Gold'>Gold</option>";
        echo "<option value='Silver'>Silver</option>";
        echo "</select>";
        echo "</div>";
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px;'>Tunch %:</label>";
        echo "<input type='number' name='fast_tunch' step='0.01' value='91.6' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
        echo "</div>";
        echo "<button type='submit' style='background-color: #28a745; color: white; padding: 12px 20px; border: none; border-radius: 4px; width: 100%; font-size: 16px;'>⚡ Create Fast Product</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Error</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
