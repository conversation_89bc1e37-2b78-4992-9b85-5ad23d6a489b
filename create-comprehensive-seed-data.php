<?php
/**
 * Create Comprehensive Seed Data Based on Requirements
 * This matches the jewellery business requirements shown in the screenshot
 */

require_once 'config/database.php';

try {
    $db = getDB();
    $db->beginTransaction();
    
    echo "<h1>🔧 Creating Comprehensive Jewellery Business Seed Data</h1>";
    
    // 1. Create detailed suppliers matching the screenshot
    echo "<h2>🚚 Creating Suppliers...</h2>";
    $suppliers = [
        ['Emerald Jewel Industry', '<PERSON><PERSON>', '**********', 'raj<PERSON>@emerald.com', '123 Jewel Street', 'Mumbai', 'Maharashtra', '400001', '27ABCDE1234F1Z5', 'ABCDE1234F', 'HDFC Bank', '***********', 'HDFC0001234', 200000.00, 30],
        ['Nafa Gold', 'Amit Shah', '**********', '<EMAIL>', '456 Gold Plaza', 'Surat', 'Gujarat', '395001', '24**********2Z6', '**********', 'ICICI Bank', '***********', 'ICIC0002345', 150000.00, 15],
        ['Surat Jewellers', '<PERSON><PERSON> Patel', '**********', '<EMAIL>', '789 Diamond Market', 'Surat', 'Gujarat', '395002', '24**********3Z7', '**********', 'SBI Bank', '***********', 'SBIN0003456', 300000.00, 45]
    ];
    
    foreach ($suppliers as $supplier) {
        $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $supplier);
        echo "<p>✅ Added supplier: {$supplier[0]}</p>";
    }
    
    // 2. Create detailed products matching the screenshot requirements
    echo "<h2>💎 Creating Products with Detailed Specifications...</h2>";
    $products = [
        // Coimbatore Chain - Emerald Jewel Industry
        ['Coimbatore Chain', 'CC001', 1, 1, 'Gold', '22K', 10.500, 0.000, 10.500, 0, 950, 5800, '********', 'Traditional Coimbatore style gold chain'],
        
        // Bangles - Nafa Gold  
        ['Bangles', 'BG001', 2, 2, 'Gold', '22K', 12.340, 0.000, 12.340, 0, 850, 5800, '********', 'Traditional gold bangles set'],
        
        // Studs - Surat Jewellers
        ['Studs', 'ST001', 3, 3, 'Gold', '18K', 2.040, 0.500, 1.540, 1500, 600, 4650, '********', 'Diamond studded gold earrings'],
        
        // Chain - VG Jewellery (will create this supplier)
        ['Chain', 'CH001', 1, 1, 'Gold', '22K', 9.754, 0.000, 9.754, 0, 1000, 5800, '********', 'Classic gold chain design'],
        
        // Bangles - Krishna Jewels (will create this supplier)
        ['Bangles', 'BG002', 2, 2, 'Gold', '22K', 31.478, 0.000, 31.478, 0, 750, 5800, '********', 'Heavy gold bangles'],
        
        // Studs - Dhanpal Jewels (will create this supplier)
        ['Studs', 'ST002', 3, 3, 'Gold', '18K', 3.388, 1.500, 1.888, 1600, 650, 4650, '********', 'Premium diamond studs']
    ];
    
    // Add additional suppliers for the products
    $additional_suppliers = [
        ['VG Jewellery', 'Vijay Gupta', '**********', '<EMAIL>', '321 VG Complex', 'Namakkal', 'Tamil Nadu', '637001', '33**********4Z8', '**********', 'Canara Bank', '***********', 'CNRB0004567', 100000.00, 20],
        ['Krishna Jewels', 'Krishna Murthy', '**********', '<EMAIL>', '654 Krishna Plaza', 'Coimbatore', 'Tamil Nadu', '641001', '33**********5Z9', '**********', 'Indian Bank', '***********', 'IDIB0005678', 250000.00, 30],
        ['Dhanpal Jewels', 'Dhanpal Singh', '**********', '<EMAIL>', '987 Dhanpal Market', 'Erode', 'Tamil Nadu', '638001', '33**********6Z0', '**********', 'Union Bank', '***********', 'UBIN0006789', 180000.00, 25]
    ];
    
    foreach ($additional_suppliers as $supplier) {
        $db->query("INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $supplier);
        echo "<p>✅ Added supplier: {$supplier[0]}</p>";
    }
    
    // Update product supplier IDs for the additional products
    $products[3][3] = 4; // Chain - VG Jewellery
    $products[4][3] = 5; // Bangles - Krishna Jewels  
    $products[5][3] = 6; // Studs - Dhanpal Jewels
    
    foreach ($products as $product) {
        $db->query("INSERT INTO products (product_name, product_code, category_id, supplier_id, metal_type, purity, base_weight, stone_weight, net_weight, stone_cost, making_charges, gold_rate, hsn_code, description, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $product);
        $productId = $db->lastInsertId();
        
        // Create detailed inventory records
        $stock = rand(10, 100);
        $metal_value = $product[10] * $product[11]; // net_weight * gold_rate
        $total_cost = $metal_value + $product[9] + $product[10]; // metal + stone + making
        $selling_price = $total_cost * 1.25; // 25% markup
        
        $db->query("INSERT INTO inventory (product_id, quantity_in_stock, cost_price, selling_price, minimum_stock_level, reorder_point, location, rack_number) VALUES (?, ?, ?, ?, 5, 10, 'A-01', ?)", [
            $productId, $stock, $total_cost, $selling_price, 'R-' . str_pad($productId, 3, '0', STR_PAD_LEFT)
        ]);
        
        echo "<p>✅ Added product: {$product[0]} - Stock: $stock, Cost: ₹" . number_format($total_cost, 2) . "</p>";
    }
    
    // 3. Create detailed customers matching business requirements
    echo "<h2>👥 Creating Customers...</h2>";
    $customers = [
        ['VG Jewellery Store', 'VG Retail Chain', 'business', '9876543220', '<EMAIL>', '123 Main Street', 'Chennai', 'Tamil Nadu', '600001', '33GHIJK7890L7Z1', 'GHIJK7890L', '', 500000.00, 45, 5.0],
        ['Krishna Jewels Retail', 'Krishna Retail Hub', 'business', '9876543221', '<EMAIL>', '456 Market Road', 'Bangalore', 'Karnataka', '560001', '29HIJKL8901M8Z2', 'HIJKL8901M', '', 300000.00, 30, 3.0],
        ['Dhanpal Jewels Store', 'Dhanpal Retail', 'business', '9876543222', '<EMAIL>', '789 Commercial Street', 'Hyderabad', 'Telangana', '500001', '36IJKLM9012N9Z3', 'IJKLM9012N', '', 200000.00, 20, 2.5],
        ['Priya Sharma', '', 'individual', '9876543223', '<EMAIL>', '321 Residential Area', 'Mumbai', 'Maharashtra', '400001', '', '', '1***********', 0, 0, 0],
        ['Rahul Gupta', '', 'individual', '9876543224', '<EMAIL>', '654 Housing Society', 'Pune', 'Maharashtra', '411001', '', '', '***********3', 50000.00, 15, 0]
    ];
    
    foreach ($customers as $customer) {
        $db->query("INSERT INTO customers (customer_name, business_name, customer_type, phone, email, address, city, state, pincode, gst_number, pan_number, aadhar_number, credit_limit, credit_days, discount_percentage, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)", $customer);
        echo "<p>✅ Added customer: {$customer[0]}</p>";
    }
    
    // 4. Create realistic sales transactions
    echo "<h2>🛒 Creating Sales Transactions...</h2>";
    $sales_data = [
        // Sale 1: VG Jewellery Store buying Coimbatore Chain
        [
            'bill_number' => 'BILL-*************',
            'customer_id' => 1,
            'sale_date' => '2025-07-29',
            'sale_time' => '10:30:00',
            'items' => [
                ['product_id' => 1, 'quantity' => 2, 'unit_weight' => 10.500, 'stone_cost' => 0, 'making_charges' => 950, 'gold_rate' => 5800]
            ]
        ],
        // Sale 2: Krishna Jewels buying Bangles
        [
            'bill_number' => 'BILL-*************', 
            'customer_id' => 2,
            'sale_date' => '2025-07-29',
            'sale_time' => '14:15:00',
            'items' => [
                ['product_id' => 2, 'quantity' => 1, 'unit_weight' => 12.340, 'stone_cost' => 0, 'making_charges' => 850, 'gold_rate' => 5800]
            ]
        ],
        // Sale 3: Individual customer buying Studs
        [
            'bill_number' => 'BILL-20250729-0003',
            'customer_id' => 4,
            'sale_date' => '2025-07-29', 
            'sale_time' => '16:45:00',
            'items' => [
                ['product_id' => 3, 'quantity' => 1, 'unit_weight' => 2.040, 'stone_cost' => 1500, 'making_charges' => 600, 'gold_rate' => 4650]
            ]
        ]
    ];
    
    foreach ($sales_data as $sale) {
        $subtotal = 0;
        $total_tax = 0;
        
        // Calculate totals
        foreach ($sale['items'] as $item) {
            $metal_value = $item['unit_weight'] * $item['gold_rate'];
            $item_total = ($metal_value + $item['stone_cost'] + $item['making_charges']) * $item['quantity'];
            $tax_amount = $item_total * 0.03; // 3% GST
            $subtotal += $item_total;
            $total_tax += $tax_amount;
        }
        
        $discount_amount = $subtotal * 0.02; // 2% discount
        $grand_total = $subtotal - $discount_amount + $total_tax;
        
        // Insert sale
        $db->query("INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, created_by, is_cancelled) VALUES (?, ?, ?, ?, ?, 'percentage', 2.00, ?, ?, ?, 'cash', 'paid', 1, 0)", [
            $sale['bill_number'], $sale['customer_id'], $sale['sale_date'], $sale['sale_time'], 
            $subtotal, $discount_amount, $total_tax, $grand_total
        ]);
        
        $sale_id = $db->lastInsertId();
        
        // Insert sale items
        foreach ($sale['items'] as $item) {
            $metal_value = $item['unit_weight'] * $item['gold_rate'];
            $unit_price = $metal_value + $item['stone_cost'] + $item['making_charges'];
            $total_price = $unit_price * $item['quantity'];
            $tax_amount = $total_price * 0.03;
            $line_total = $total_price + $tax_amount;
            
            $db->query("INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 3.00, ?, ?)", [
                $sale_id, $item['product_id'], $item['quantity'], $item['unit_weight'], 
                $item['unit_weight'] * $item['quantity'], $item['stone_cost'], $item['making_charges'], 
                $item['gold_rate'], $unit_price, $total_price, $tax_amount, $line_total
            ]);
            
            // Update inventory
            $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - ? WHERE product_id = ?", [$item['quantity'], $item['product_id']]);
            
            // Record stock movement
            $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, created_by) VALUES (?, 'out', ?, 'sale', ?, 'Sale transaction', 1)", [$item['product_id'], $item['quantity'], $sale_id]);
        }
        
        echo "<p>✅ Added sale: {$sale['bill_number']} - ₹" . number_format($grand_total, 2) . "</p>";
    }
    
    // 5. Update metal rates to current market rates
    echo "<h2>💰 Setting Current Metal Rates...</h2>";
    $current_rates = [
        ['Gold', '24K', 6200.00],
        ['Gold', '22K', 5800.00], 
        ['Gold', '18K', 4650.00],
        ['Silver', '999', 85.00],
        ['Silver', '925', 78.00],
        ['Platinum', '950', 3200.00]
    ];
    
    foreach ($current_rates as $rate) {
        $db->query("UPDATE metal_rates SET rate_per_gram = ?, updated_at = NOW() WHERE metal_type = ? AND purity = ? AND rate_date = CURDATE()", [$rate[2], $rate[0], $rate[1]]);
        echo "<p>✅ Updated {$rate[0]} {$rate[1]}: ₹{$rate[2]}/gm</p>";
    }
    
    $db->commit();
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; color: #155724; margin: 30px 0;'>";
    echo "<h2>🎉 Comprehensive Seed Data Created Successfully!</h2>";
    echo "<p>The system now has realistic jewellery business data:</p>";
    echo "<ul>";
    echo "<li>✅ 6 Suppliers with complete business details</li>";
    echo "<li>✅ 6 Products with accurate weight, stone, and making charge calculations</li>";
    echo "<li>✅ 5 Customers (3 business, 2 individual) with credit terms</li>";
    echo "<li>✅ 3 Realistic sales transactions with proper calculations</li>";
    echo "<li>✅ Current market metal rates</li>";
    echo "<li>✅ Inventory records with stock levels</li>";
    echo "<li>✅ Stock movement tracking</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🌐 Test Your System Now:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $test_pages = [
        'index.php' => '📊 Dashboard',
        'products.php' => '💎 Products',
        'inventory.php' => '📦 Inventory', 
        'suppliers.php' => '🚚 Suppliers',
        'customers.php' => '👥 Customers',
        'sales.php' => '📊 Sales',
        'billing.php' => '🛒 New Sale',
        'metal-rates.php' => '💰 Metal Rates'
    ];
    
    foreach ($test_pages as $page => $title) {
        echo "<div style='background: #e9ecef; padding: 10px; border-radius: 5px; text-align: center;'>";
        echo "<a href='$page' target='_blank' style='text-decoration: none; color: #495057; font-weight: bold;'>$title</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error Creating Seed Data</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
