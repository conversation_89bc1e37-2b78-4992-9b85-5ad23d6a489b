<?php
/**
 * Cleanup Old Notifications - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Get cleanup period from settings (default 30 days)
    $cleanupDays = $db->fetch("
        SELECT setting_value 
        FROM settings 
        WHERE setting_key = 'auto_cleanup_days'
    ")['setting_value'] ?? 30;
    
    // Delete read notifications older than cleanup period
    $result = $db->query("
        DELETE FROM notifications 
        WHERE is_read = 1 
        AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    ", [$cleanupDays]);
    
    $deletedCount = $result; // Number of affected rows
    
    // Also delete expired notifications
    $expiredResult = $db->query("
        DELETE FROM notifications 
        WHERE expires_at IS NOT NULL 
        AND expires_at < NOW()
    ");
    
    $expiredCount = $expiredResult;
    
    echo json_encode([
        'success' => true,
        'deleted' => $deletedCount + $expiredCount,
        'message' => "Cleaned up " . ($deletedCount + $expiredCount) . " old notifications"
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
