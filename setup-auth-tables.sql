-- Enhanced Authentication Tables for Indian Jewellery Wholesale Management System v2.0

-- Password Reset Tokens Table
CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at),
    INDEX idx_user_id (user_id)
);

-- Remember Me Tokens Table
CREATE TABLE IF NOT EXISTS remember_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at),
    INDEX idx_user_id (user_id)
);

-- Login Sessions Table
CREATE TABLE IF NOT EXISTS login_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(128) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- Security Events Table
CREATE TABLE IF NOT EXISTS security_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    event_type ENUM('login_success', 'login_failed', 'password_reset', 'account_locked', 'suspicious_activity') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- Two-Factor Authentication Backup Codes Table
CREATE TABLE IF NOT EXISTS two_factor_backup_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code VARCHAR(10) NOT NULL,
    used TINYINT(1) DEFAULT 0,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_code (code)
);

-- Add columns to existing users table for enhanced authentication
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_failed_login TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS two_factor_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(32) NULL,
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS must_change_password TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_password_reminder TIMESTAMP NULL;

-- Create indexes for new columns
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_login_attempts (login_attempts),
ADD INDEX IF NOT EXISTS idx_last_failed_login (last_failed_login),
ADD INDEX IF NOT EXISTS idx_account_locked_until (account_locked_until),
ADD INDEX IF NOT EXISTS idx_two_factor_enabled (two_factor_enabled);

-- Insert sample admin user with enhanced security (if not exists)
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    role, 
    is_active,
    created_at,
    password_changed_at
) VALUES (
    'admin',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'System Administrator',
    'admin',
    1,
    NOW(),
    NOW()
);

-- Insert sample manager user
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    role, 
    is_active,
    created_at,
    password_changed_at
) VALUES (
    'manager',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'Store Manager',
    'manager',
    1,
    NOW(),
    NOW()
);

-- Insert sample staff user
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    role, 
    is_active,
    created_at,
    password_changed_at
) VALUES (
    'staff',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'Sales Staff',
    'staff',
    1,
    NOW(),
    NOW()
);

-- Create stored procedures for authentication

DELIMITER //

-- Procedure to clean expired tokens
CREATE PROCEDURE IF NOT EXISTS CleanExpiredTokens()
BEGIN
    DELETE FROM password_resets WHERE expires_at < NOW() OR used = 1;
    DELETE FROM remember_tokens WHERE expires_at < NOW();
    UPDATE login_sessions SET is_active = 0 WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR);
END //

-- Procedure to log security events
CREATE PROCEDURE IF NOT EXISTS LogSecurityEvent(
    IN p_user_id INT,
    IN p_event_type VARCHAR(50),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    IN p_details JSON
)
BEGIN
    INSERT INTO security_events (user_id, event_type, ip_address, user_agent, details)
    VALUES (p_user_id, p_event_type, p_ip_address, p_user_agent, p_details);
END //

-- Procedure to check account lockout
CREATE PROCEDURE IF NOT EXISTS CheckAccountLockout(
    IN p_username VARCHAR(50),
    OUT p_is_locked BOOLEAN,
    OUT p_attempts INT
)
BEGIN
    DECLARE v_user_id INT;
    DECLARE v_attempts INT DEFAULT 0;
    DECLARE v_last_failed TIMESTAMP;
    
    SELECT id, login_attempts, last_failed_login 
    INTO v_user_id, v_attempts, v_last_failed
    FROM users 
    WHERE username = p_username AND is_active = 1;
    
    SET p_attempts = v_attempts;
    
    IF v_attempts >= 5 AND v_last_failed > DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN
        SET p_is_locked = TRUE;
    ELSE
        SET p_is_locked = FALSE;
        -- Reset attempts if lockout period has passed
        IF v_attempts >= 5 AND v_last_failed <= DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN
            UPDATE users SET login_attempts = 0 WHERE id = v_user_id;
        END IF;
    END IF;
END //

DELIMITER ;

-- Create events for automatic cleanup (if supported)
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS cleanup_expired_tokens
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredTokens();

-- Insert initial system settings for authentication
INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category) VALUES
('auth_max_login_attempts', '5', 'number', 'Maximum login attempts before account lockout', 'security'),
('auth_lockout_duration', '30', 'number', 'Account lockout duration in minutes', 'security'),
('auth_session_timeout', '1440', 'number', 'Session timeout in minutes (24 hours)', 'security'),
('auth_password_min_length', '6', 'number', 'Minimum password length', 'security'),
('auth_require_2fa', '0', 'boolean', 'Require two-factor authentication for all users', 'security'),
('auth_remember_me_duration', '30', 'number', 'Remember me duration in days', 'security'),
('auth_password_expiry_days', '90', 'number', 'Password expiry in days (0 = never)', 'security'),
('auth_enable_captcha', '1', 'boolean', 'Enable CAPTCHA after failed attempts', 'security');

-- Create views for security monitoring
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    'Total Users' as metric,
    COUNT(*) as value,
    'users' as category
FROM users WHERE is_active = 1
UNION ALL
SELECT 
    'Locked Accounts' as metric,
    COUNT(*) as value,
    'security' as category
FROM users 
WHERE is_active = 1 
AND login_attempts >= 5 
AND last_failed_login > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
UNION ALL
SELECT 
    'Active Sessions' as metric,
    COUNT(*) as value,
    'sessions' as category
FROM login_sessions 
WHERE is_active = 1 
AND last_activity > DATE_SUB(NOW(), INTERVAL 24 HOUR)
UNION ALL
SELECT 
    'Failed Logins Today' as metric,
    COUNT(*) as value,
    'security' as category
FROM security_events 
WHERE event_type = 'login_failed' 
AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    '2FA Enabled Users' as metric,
    COUNT(*) as value,
    'security' as category
FROM users 
WHERE is_active = 1 
AND two_factor_enabled = 1;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON password_resets TO 'your_db_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON remember_tokens TO 'your_db_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON login_sessions TO 'your_db_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON security_events TO 'your_db_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON two_factor_backup_codes TO 'your_db_user'@'localhost';
-- GRANT EXECUTE ON PROCEDURE CleanExpiredTokens TO 'your_db_user'@'localhost';
-- GRANT EXECUTE ON PROCEDURE LogSecurityEvent TO 'your_db_user'@'localhost';
-- GRANT EXECUTE ON PROCEDURE CheckAccountLockout TO 'your_db_user'@'localhost';

-- Success message
SELECT 'Enhanced authentication tables and procedures created successfully!' as message;
