<?php
/**
 * Sidebar Navigation Component
 * Indian Jewellery Wholesale Management System v2.0
 */

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_path = $_SERVER['REQUEST_URI'];

// Define navigation menu structure
$navigation = [
    [
        'section' => 'Main',
        'items' => [
            [
                'title' => 'Dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'url' => 'index.php',
                'active' => $current_page === 'index.php'
            ]
        ]
    ],
    [
        'section' => 'Inventory',
        'items' => [
            [
                'title' => 'Products',
                'icon' => 'fas fa-gem',
                'url' => 'products.php',
                'active' => $current_page === 'products.php'
            ],
            [
                'title' => 'Categories',
                'icon' => 'fas fa-tags',
                'url' => 'categories.php',
                'active' => $current_page === 'categories.php'
            ],
            [
                'title' => 'Stock Management',
                'icon' => 'fas fa-boxes',
                'url' => 'inventory.php',
                'active' => $current_page === 'inventory.php'
            ],
            [
                'title' => 'Suppliers',
                'icon' => 'fas fa-truck',
                'url' => 'suppliers.php',
                'active' => $current_page === 'suppliers.php'
            ]
        ]
    ],
    [
        'section' => 'Sales',
        'items' => [
            [
                'title' => 'New Sale',
                'icon' => 'fas fa-plus-circle',
                'url' => 'billing.php',
                'active' => $current_page === 'billing.php'
            ],
            [
                'title' => 'Sales History',
                'icon' => 'fas fa-history',
                'url' => 'sales.php',
                'active' => $current_page === 'sales.php'
            ],
            [
                'title' => 'Customers',
                'icon' => 'fas fa-users',
                'url' => 'customers.php',
                'active' => $current_page === 'customers.php'
            ]
        ]
    ],
    [
        'section' => 'Finance',
        'items' => [
            [
                'title' => 'Metal Rates',
                'icon' => 'fas fa-coins',
                'url' => 'metal-rates.php',
                'active' => $current_page === 'metal-rates.php'
            ],
            [
                'title' => 'Reports',
                'icon' => 'fas fa-chart-bar',
                'submenu' => [
                    [
                        'title' => 'Sales Report',
                        'url' => 'reports/sales.php',
                        'active' => strpos($current_path, 'reports/sales.php') !== false
                    ],
                    [
                        'title' => 'Inventory Report',
                        'url' => 'reports/inventory.php',
                        'active' => strpos($current_path, 'reports/inventory.php') !== false
                    ],
                    [
                        'title' => 'Customer Report',
                        'url' => 'reports/customers.php',
                        'active' => strpos($current_path, 'reports/customers.php') !== false
                    ]
                ]
            ]
        ]
    ],
    [
        'section' => 'System',
        'items' => [
            [
                'title' => 'Settings',
                'icon' => 'fas fa-cog',
                'url' => 'settings.php',
                'active' => $current_page === 'settings.php'
            ],
            [
                'title' => 'Users',
                'icon' => 'fas fa-user-shield',
                'url' => 'users.php',
                'active' => $current_page === 'users.php'
            ],
            [
                'title' => 'Backup',
                'icon' => 'fas fa-database',
                'url' => 'backup.php',
                'active' => $current_page === 'backup.php'
            ]
        ]
    ]
];

// Check if any submenu item is active
function hasActiveSubmenu($submenu) {
    foreach ($submenu as $item) {
        if ($item['active']) {
            return true;
        }
    }
    return false;
}
?>

<!-- Sidebar -->
<aside class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <a href="index.php" class="sidebar-brand">
            <i class="fas fa-gem"></i>
            <span class="brand-text"><?php echo APP_NAME; ?></span>
        </a>
        <button class="sidebar-toggle" id="sidebarToggle" type="button">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- Sidebar Navigation -->
    <nav class="sidebar-nav">
        <?php foreach ($navigation as $section): ?>
            <div class="nav-section">
                <div class="nav-section-title"><?php echo $section['section']; ?></div>
                <ul class="nav-list">
                    <?php foreach ($section['items'] as $item): ?>
                        <li class="nav-item <?php echo isset($item['submenu']) ? 'has-submenu' : ''; ?> <?php echo (isset($item['submenu']) && hasActiveSubmenu($item['submenu'])) ? 'open' : ''; ?>">
                            <?php if (isset($item['submenu'])): ?>
                                <!-- Menu item with submenu -->
                                <a href="#" class="nav-link <?php echo hasActiveSubmenu($item['submenu']) ? 'active' : ''; ?>" data-submenu-toggle>
                                    <span class="nav-icon">
                                        <i class="<?php echo $item['icon']; ?>"></i>
                                    </span>
                                    <span class="nav-text"><?php echo $item['title']; ?></span>
                                    <i class="fas fa-chevron-down submenu-arrow"></i>
                                </a>
                                <ul class="submenu">
                                    <?php foreach ($item['submenu'] as $subitem): ?>
                                        <li class="nav-item">
                                            <a href="<?php echo $subitem['url']; ?>" class="nav-link <?php echo $subitem['active'] ? 'active' : ''; ?>">
                                                <span class="nav-text"><?php echo $subitem['title']; ?></span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <!-- Regular menu item -->
                                <a href="<?php echo $item['url']; ?>" class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>">
                                    <span class="nav-icon">
                                        <i class="<?php echo $item['icon']; ?>"></i>
                                    </span>
                                    <span class="nav-text"><?php echo $item['title']; ?></span>
                                </a>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>
    </nav>
</aside>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Main Content Wrapper -->
<div class="main-content" id="mainContent">
    <!-- Top Header -->
    <header class="top-header">
        <div class="header-left">
            <button class="mobile-sidebar-toggle" id="mobileSidebarToggle" type="button">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="page-title">
                <?php
                // Generate page title based on current page
                $page_titles = [
                    'index.php' => 'Dashboard',
                    'products.php' => 'Products',
                    'categories.php' => 'Categories',
                    'inventory.php' => 'Stock Management',
                    'suppliers.php' => 'Suppliers',
                    'billing.php' => 'New Sale',
                    'sales.php' => 'Sales History',
                    'customers.php' => 'Customers',
                    'metal-rates.php' => 'Metal Rates',
                    'settings.php' => 'Settings',
                    'users.php' => 'Users',
                    'backup.php' => 'Backup'
                ];
                
                echo $page_titles[$current_page] ?? 'Page';
                ?>
            </h1>
        </div>
        
        <div class="header-right">
            <!-- Quick Actions Dropdown -->
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" id="quickActionsDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-2"></i>Quick Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="billing.php"><i class="fas fa-plus-circle me-2"></i>New Sale</a></li>
                    <li><a class="dropdown-item" href="products.php?action=add"><i class="fas fa-gem me-2"></i>Add Product</a></li>
                    <li><a class="dropdown-item" href="customers.php?action=add"><i class="fas fa-user-plus me-2"></i>Add Customer</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="inventory.php"><i class="fas fa-boxes me-2"></i>Manage Stock</a></li>
                </ul>
            </div>

            <!-- Notifications -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-bell"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                        <span class="visually-hidden">unread messages</span>
                    </span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                    <li class="dropdown-header">Notifications</li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Low stock alert for Gold Chain</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-coins text-info me-2"></i>Gold rate updated</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-file-invoice text-success me-2"></i>New sale completed</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
                </ul>
            </div>

            <!-- User Profile -->
            <div class="dropdown">
                <button class="btn btn-outline-dark dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php 
                    $user = getCurrentUser();
                    echo $user ? $user['username'] : 'Admin';
                    ?>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </header>

    <!-- Content Area -->
    <main class="content-area">
        <!-- Page content will be inserted here -->
