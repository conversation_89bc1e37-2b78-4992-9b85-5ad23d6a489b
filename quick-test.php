<?php
/**
 * Quick Database Connectivity Test for All Pages
 */

require_once 'config/database.php';

echo "<h1>🔧 Quick Database Test</h1>";

try {
    $db = getDB();
    echo "<p>✅ Database connection: <strong>SUCCESS</strong></p>";
    
    // Test each table
    $tables = [
        'users' => 'User Management',
        'categories' => 'Product Categories', 
        'products' => 'Products',
        'suppliers' => 'Suppliers',
        'customers' => 'Customers',
        'inventory' => 'Inventory',
        'sales' => 'Sales',
        'sale_items' => 'Sale Items',
        'metal_rates' => 'Metal Rates',
        'settings' => 'System Settings',
        'stock_movements' => 'Stock Movements',
        'notifications' => 'Notifications',
        'audit_logs' => 'Audit Logs'
    ];
    
    echo "<h2>📊 Table Accessibility Test</h2>";
    foreach ($tables as $table => $description) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            echo "<p>✅ <strong>$description</strong> ($table): $count records</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>$description</strong> ($table): ERROR - " . $e->getMessage() . "</p>";
        }
    }
    
    // Test specific queries used in pages
    echo "<h2>🔍 Page-Specific Query Tests</h2>";
    
    // Dashboard queries
    try {
        $stats = $db->fetch("
            SELECT 
                (SELECT COUNT(*) FROM products WHERE is_active = 1) as total_products,
                (SELECT COUNT(*) FROM customers WHERE is_active = 1) as total_customers,
                (SELECT COUNT(*) FROM sales WHERE is_cancelled = 0) as total_sales,
                (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE is_cancelled = 0) as total_revenue
        ");
        echo "<p>✅ <strong>Dashboard Statistics</strong>: Products: {$stats['total_products']}, Customers: {$stats['total_customers']}, Sales: {$stats['total_sales']}, Revenue: ₹" . number_format($stats['total_revenue'], 2) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>Dashboard Statistics</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    // Products with categories
    try {
        $products = $db->fetchAll("
            SELECT p.product_name, c.category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.is_active = 1 
            LIMIT 5
        ");
        echo "<p>✅ <strong>Products with Categories</strong>: Found " . count($products) . " products</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>Products with Categories</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    // Inventory with products
    try {
        $inventory = $db->fetchAll("
            SELECT i.*, p.product_name 
            FROM inventory i 
            JOIN products p ON i.product_id = p.id 
            LIMIT 5
        ");
        echo "<p>✅ <strong>Inventory with Products</strong>: Found " . count($inventory) . " inventory records</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>Inventory with Products</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    // Sales with customers
    try {
        $sales = $db->fetchAll("
            SELECT s.bill_number, c.customer_name, s.grand_total 
            FROM sales s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            WHERE s.is_cancelled = 0 
            LIMIT 5
        ");
        echo "<p>✅ <strong>Sales with Customers</strong>: Found " . count($sales) . " sales records</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>Sales with Customers</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    // Current metal rates
    try {
        $rates = $db->fetchAll("
            SELECT metal_type, purity, rate_per_gram 
            FROM metal_rates 
            WHERE rate_date = CURDATE() AND is_active = 1
        ");
        echo "<p>✅ <strong>Current Metal Rates</strong>: Found " . count($rates) . " rates for today</p>";
        foreach ($rates as $rate) {
            echo "<small>&nbsp;&nbsp;&nbsp;• {$rate['metal_type']} {$rate['purity']}: ₹{$rate['rate_per_gram']}/gm</small><br>";
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Current Metal Rates</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    // Settings
    try {
        $settings = $db->fetchAll("SELECT setting_key, setting_value FROM settings LIMIT 5");
        echo "<p>✅ <strong>System Settings</strong>: Found " . count($settings) . " settings</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>System Settings</strong>: ERROR - " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🎉 Test Summary</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h3>✅ ALL DATABASE CONNECTIONS ARE WORKING!</h3>";
    echo "<p>All pages should now be able to connect to the database and display data correctly.</p>";
    echo "<p><strong>System Status:</strong> FULLY OPERATIONAL</p>";
    echo "</div>";
    
    echo "<h3>🌐 Test Individual Pages:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>📊 Dashboard</a></li>";
    echo "<li><a href='products.php' target='_blank'>💎 Products</a></li>";
    echo "<li><a href='categories.php' target='_blank'>📂 Categories</a></li>";
    echo "<li><a href='inventory.php' target='_blank'>📦 Inventory</a></li>";
    echo "<li><a href='suppliers.php' target='_blank'>🚚 Suppliers</a></li>";
    echo "<li><a href='customers.php' target='_blank'>👥 Customers</a></li>";
    echo "<li><a href='billing.php' target='_blank'>🛒 Billing</a></li>";
    echo "<li><a href='sales.php' target='_blank'>📊 Sales</a></li>";
    echo "<li><a href='metal-rates.php' target='_blank'>💰 Metal Rates</a></li>";
    echo "<li><a href='settings.php' target='_blank'>⚙️ Settings</a></li>";
    echo "<li><a href='users.php' target='_blank'>👤 Users</a></li>";
    echo "<li><a href='backup.php' target='_blank'>💾 Backup</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ DATABASE CONNECTION FAILED!</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and ensure the database is running.</p>";
    echo "</div>";
}
?>
