<?php
/**
 * Billing System - Matches Your Exact Requirements
 * Formula: Final Value = Tunch Weight × Tunch Rate + Making + Stone Charges
 */

require_once 'config/database.php';

startSession();
$db = getDB();

$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'add_inventory_item') {
        try {
            // Add inventory item
            $sql = "INSERT INTO inventory_items (
                supplier_name, description, product_name, 
                gross_wt, stone_cost_purity, stone_cost_points, 
                procured_in_24k, sold_value, balance_in_stock,
                with_stone_24k, without_stone_24k, weight_in_24k, weight_in_22k
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $_POST['supplier_name'],
                $_POST['description'],
                $_POST['product_name'],
                floatval($_POST['gross_wt']),
                floatval($_POST['stone_cost_purity']),
                floatval($_POST['stone_cost_points']),
                floatval($_POST['procured_in_24k']),
                floatval($_POST['sold_value']),
                floatval($_POST['balance_in_stock']),
                floatval($_POST['with_stone_24k']),
                floatval($_POST['without_stone_24k']),
                floatval($_POST['weight_in_24k']),
                floatval($_POST['weight_in_22k'])
            ];
            
            $db->execute($sql, $params);
            $success = "✅ Inventory item added successfully!";
            
        } catch (Exception $e) {
            $error = "❌ Error adding inventory item: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'create_bill') {
        try {
            // Create billing entry
            $sql = "INSERT INTO billing_entries (
                customer_name, location, product_name, 
                gross_wt, stone_wt, net_wt, va_stone, 
                plain, wt_in_24k, unit_price, stone_price, total
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // Calculate values using your exact formula
            $gross_wt = floatval($_POST['gross_wt']);
            $stone_wt = floatval($_POST['stone_wt']);
            $net_wt = $gross_wt - $stone_wt; // Net Weight = Gross Weight - Stone Weight
            $tunch_percentage = floatval($_POST['tunch_percentage']);
            $base_rate = floatval($_POST['base_rate']);
            $making_charges = floatval($_POST['making_charges']);
            $stone_charges = floatval($_POST['stone_charges']);
            
            // Apply your exact formula
            $tunch_weight = $net_wt * ($tunch_percentage / 100); // Tunch Weight = Net Weight × (Tunch Percentage / 100)
            $tunch_rate = $base_rate * ($tunch_percentage / 100); // Tunch Rate = Base Rate × (Tunch Percentage / 100)
            $final_value = ($tunch_weight * $tunch_rate) + $making_charges + $stone_charges; // Final Value = Tunch Weight × Tunch Rate + Making + Stone Charges
            
            $params = [
                $_POST['customer_name'],
                $_POST['location'],
                $_POST['product_name'],
                $gross_wt,
                $stone_wt,
                $net_wt,
                floatval($_POST['va_stone']),
                floatval($_POST['plain']),
                $tunch_weight, // wt_in_24k (actually tunch weight)
                $tunch_rate, // unit_price (actually tunch rate)
                $stone_charges,
                $final_value
            ];
            
            $db->execute($sql, $params);
            $success = "✅ Bill created successfully! Final Value: ₹" . number_format($final_value, 2);
            
        } catch (Exception $e) {
            $error = "❌ Error creating bill: " . $e->getMessage();
        }
    }
}

// Get inventory items
$inventoryItems = $db->fetchAll("SELECT * FROM inventory_items ORDER BY id DESC LIMIT 10");

// Get billing entries
$billingEntries = $db->fetchAll("SELECT * FROM billing_entries ORDER BY id DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing Software - Inventory & Billing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .billing-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
        }
        .section-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .table-header {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 12px;
        }
        .calculation-preview {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .formula-display {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        .small-input {
            font-size: 12px;
            padding: 4px 8px;
        }
        .table-sm td, .table-sm th {
            padding: 4px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="billing-header">
        <div class="container">
            <h1 class="mb-0"><i class="fas fa-receipt me-2"></i>Billing Software</h1>
            <p class="mb-0">Inventory & Billing Management System</p>
        </div>
    </div>

    <div class="container-fluid mt-4">
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Formula Display -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card section-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Tunch Calculation Formula</h5>
                    </div>
                    <div class="card-body">
                        <div class="formula-display">
                            <strong>Your Exact Formula Implementation:</strong><br>
                            1. <strong>Net Weight</strong> = Gross Weight - Stone Weight<br>
                            2. <strong>Tunch Weight</strong> = Net Weight × (Tunch Percentage ÷ 100)<br>
                            3. <strong>Tunch Rate</strong> = Base Rate × (Tunch Percentage ÷ 100)<br>
                            4. <strong>Final Value</strong> = Tunch Weight × Tunch Rate + Making + Stone Charges
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Inventory Section -->
            <div class="col-md-6">
                <div class="card section-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>Inventory</h5>
                    </div>
                    <div class="card-body">
                        <!-- Add Inventory Item Form -->
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="add_inventory_item">
                            
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <label class="form-label">Supplier Name</label>
                                    <input type="text" class="form-control small-input" name="supplier_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control small-input" name="description" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Product Name</label>
                                    <input type="text" class="form-control small-input" name="product_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Gross Wt</label>
                                    <input type="number" class="form-control small-input" name="gross_wt" step="0.001" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Cost (Purity)</label>
                                    <input type="number" class="form-control small-input" name="stone_cost_purity" step="0.01">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Cost (Points)</label>
                                    <input type="number" class="form-control small-input" name="stone_cost_points" step="0.01">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Procured in 24k</label>
                                    <input type="number" class="form-control small-input" name="procured_in_24k" step="0.01">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Sold Value</label>
                                    <input type="number" class="form-control small-input" name="sold_value" step="0.01">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Balance in Stock</label>
                                    <input type="number" class="form-control small-input" name="balance_in_stock" step="0.001">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">With Stone 24k</label>
                                    <input type="number" class="form-control small-input" name="with_stone_24k" step="0.001">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Without Stone 24k</label>
                                    <input type="number" class="form-control small-input" name="without_stone_24k" step="0.001">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Weight in 24k</label>
                                    <input type="number" class="form-control small-input" name="weight_in_24k" step="0.001">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Weight in 22k</label>
                                    <input type="number" class="form-control small-input" name="weight_in_22k" step="0.001">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success btn-sm mt-3">
                                <i class="fas fa-plus me-1"></i>Add to Inventory
                            </button>
                        </form>

                        <!-- Inventory Table -->
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-header">
                                    <tr>
                                        <th>Sl No</th>
                                        <th>Supplier Name</th>
                                        <th>Description</th>
                                        <th>Product Name</th>
                                        <th>Gross Wt</th>
                                        <th>Stone Cost (Purity)</th>
                                        <th>Stone Cost (Points)</th>
                                        <th>Procured in 24k</th>
                                        <th>Sold Value</th>
                                        <th>Balance in Stock</th>
                                        <th>With Stone 24k</th>
                                        <th>Without Stone 24k</th>
                                        <th>Weight in 24k</th>
                                        <th>Weight in 22k</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inventoryItems as $index => $item): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($item['supplier_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['description']); ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo number_format($item['gross_wt'], 3); ?></td>
                                        <td><?php echo number_format($item['stone_cost_purity'], 2); ?></td>
                                        <td><?php echo number_format($item['stone_cost_points'], 2); ?></td>
                                        <td><?php echo number_format($item['procured_in_24k'], 2); ?></td>
                                        <td><?php echo number_format($item['sold_value'], 2); ?></td>
                                        <td><?php echo number_format($item['balance_in_stock'], 3); ?></td>
                                        <td><?php echo number_format($item['with_stone_24k'], 3); ?></td>
                                        <td><?php echo number_format($item['without_stone_24k'], 3); ?></td>
                                        <td><?php echo number_format($item['weight_in_24k'], 3); ?></td>
                                        <td><?php echo number_format($item['weight_in_22k'], 3); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Section -->
            <div class="col-md-6">
                <div class="card section-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>Billing</h5>
                    </div>
                    <div class="card-body">
                        <!-- Create Bill Form -->
                        <form method="POST" id="billingForm">
                            <input type="hidden" name="action" value="create_bill">
                            
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <label class="form-label">Customer Name</label>
                                    <input type="text" class="form-control small-input" name="customer_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Location</label>
                                    <input type="text" class="form-control small-input" name="location" required>
                                </div>
                                <div class="col-md-12">
                                    <label class="form-label">Product Name</label>
                                    <input type="text" class="form-control small-input" name="product_name" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Gross Wt</label>
                                    <input type="number" class="form-control small-input" id="gross_wt" name="gross_wt" step="0.001" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Wt</label>
                                    <input type="number" class="form-control small-input" id="stone_wt" name="stone_wt" step="0.001" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Net Wt (Auto)</label>
                                    <input type="number" class="form-control small-input" id="net_wt" name="net_wt" step="0.001" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Tunch %</label>
                                    <input type="number" class="form-control small-input" id="tunch_percentage" name="tunch_percentage" step="0.1" value="91.6" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Base Rate</label>
                                    <input type="number" class="form-control small-input" id="base_rate" name="base_rate" step="0.01" value="6600" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">VA Stone</label>
                                    <input type="number" class="form-control small-input" name="va_stone" step="0.01" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Making Charges</label>
                                    <input type="number" class="form-control small-input" id="making_charges" name="making_charges" step="0.01" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Charges</label>
                                    <input type="number" class="form-control small-input" id="stone_charges" name="stone_charges" step="0.01" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Plain</label>
                                    <input type="number" class="form-control small-input" name="plain" step="0.01" value="0">
                                </div>
                            </div>
                            
                            <!-- Real-time Calculation Preview -->
                            <div class="calculation-preview mt-3">
                                <h6><i class="fas fa-calculator me-2"></i>Live Calculation Preview</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <small><strong>Tunch Weight:</strong><br>
                                        <span id="tunchWeightDisplay">0.000g</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Tunch Rate:</strong><br>
                                        <span id="tunchRateDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Metal Value:</strong><br>
                                        <span id="metalValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Final Value:</strong><br>
                                        <span id="finalValueDisplay" style="font-weight: bold; color: #28a745;">₹0.00</span></small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>Formula:</strong> Final Value = Tunch Weight × Tunch Rate + Making + Stone Charges
                                    </small>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-info btn-sm mt-3">
                                <i class="fas fa-file-invoice me-1"></i>Create Bill
                            </button>
                        </form>

                        <!-- Billing Table -->
                        <div class="table-responsive mt-4">
                            <table class="table table-sm table-bordered">
                                <thead class="table-header">
                                    <tr>
                                        <th>Sl No</th>
                                        <th>Customer Name</th>
                                        <th>Location</th>
                                        <th>Product Name</th>
                                        <th>Gross Wt</th>
                                        <th>Stone Wt</th>
                                        <th>Net Wt</th>
                                        <th>VA Stone</th>
                                        <th>Plain</th>
                                        <th>Wt in 24k</th>
                                        <th>Unit Price</th>
                                        <th>Stone Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($billingEntries as $index => $entry): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($entry['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($entry['location']); ?></td>
                                        <td><?php echo htmlspecialchars($entry['product_name']); ?></td>
                                        <td><?php echo number_format($entry['gross_wt'], 3); ?></td>
                                        <td><?php echo number_format($entry['stone_wt'], 3); ?></td>
                                        <td><?php echo number_format($entry['net_wt'], 3); ?></td>
                                        <td><?php echo number_format($entry['va_stone'], 2); ?></td>
                                        <td><?php echo number_format($entry['plain'], 2); ?></td>
                                        <td><?php echo number_format($entry['wt_in_24k'], 3); ?></td>
                                        <td>₹<?php echo number_format($entry['unit_price'], 2); ?></td>
                                        <td>₹<?php echo number_format($entry['stone_price'], 2); ?></td>
                                        <td><strong>₹<?php echo number_format($entry['total'], 2); ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const grossWt = document.getElementById('gross_wt');
            const stoneWt = document.getElementById('stone_wt');
            const netWt = document.getElementById('net_wt');
            const tunchPercentage = document.getElementById('tunch_percentage');
            const baseRate = document.getElementById('base_rate');
            const makingCharges = document.getElementById('making_charges');
            const stoneCharges = document.getElementById('stone_charges');

            // Display elements
            const tunchWeightDisplay = document.getElementById('tunchWeightDisplay');
            const tunchRateDisplay = document.getElementById('tunchRateDisplay');
            const metalValueDisplay = document.getElementById('metalValueDisplay');
            const finalValueDisplay = document.getElementById('finalValueDisplay');

            function calculateNetWeight() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                netWt.value = net.toFixed(3);
                calculateTunch();
            }

            function calculateTunch() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                const tunchPct = parseFloat(tunchPercentage.value) || 0;
                const rate = parseFloat(baseRate.value) || 0;
                const making = parseFloat(makingCharges.value) || 0;
                const stoneCharge = parseFloat(stoneCharges.value) || 0;

                if (net <= 0 || tunchPct <= 0 || rate <= 0) {
                    tunchWeightDisplay.textContent = '0.000g';
                    tunchRateDisplay.textContent = '₹0.00';
                    metalValueDisplay.textContent = '₹0.00';
                    finalValueDisplay.textContent = '₹0.00';
                    return;
                }

                // Apply your exact formula
                // 1. Net Weight = Gross Weight - Stone Weight (already calculated)
                // 2. Tunch Weight = Net Weight × (Tunch Percentage / 100)
                const tunchWeight = net * (tunchPct / 100);

                // 3. Tunch Rate = Base Rate × (Tunch Percentage / 100)
                const tunchRate = rate * (tunchPct / 100);

                // Metal Value = Tunch Weight × Tunch Rate
                const metalValue = tunchWeight * tunchRate;

                // 4. Final Value = Tunch Weight × Tunch Rate + Making + Stone Charges
                const finalValue = metalValue + making + stoneCharge;

                // Update display
                tunchWeightDisplay.textContent = tunchWeight.toFixed(3) + 'g';
                tunchRateDisplay.textContent = '₹' + tunchRate.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                finalValueDisplay.textContent = '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            }

            // Event listeners
            grossWt.addEventListener('input', calculateNetWeight);
            stoneWt.addEventListener('input', calculateNetWeight);
            tunchPercentage.addEventListener('input', calculateTunch);
            baseRate.addEventListener('input', calculateTunch);
            makingCharges.addEventListener('input', calculateTunch);
            stoneCharges.addEventListener('input', calculateTunch);

            // Initial calculation
            calculateNetWeight();
        });
    </script>
</body>
</html>
