<?php
/**
 * Enhanced Jewelry Calculator - Advanced Calculation System
 * Based on the TypeScript reference implementation with flexible purity handling
 */

require_once 'PurityParser.php';

class EnhancedJewelryCalculator {
    
    /**
     * Calculate jewelry item with enhanced purity handling and comprehensive breakdown
     * 
     * @param array $item - Jewelry item data
     * @return array - Detailed calculation result
     */
    public static function calculateJewelryItem($item) {
        // Parse purity information using advanced parser
        $purityInfo = PurityParser::parsePurity($item['purity'], $item['metalType'] ?? null);
        $purityPercentage = $purityInfo['percentage'];
        
        // Step 1: Calculate Net Weight
        $netWeight = $item['grossWeight'] - ($item['stoneWeight'] ?? 0);
        
        // Step 2: Calculate Fine Weight (using precise purity)
        $fineWeight = $netWeight * ($purityPercentage / 100);
        
        // Step 3: Calculate Metal Value
        $metalValue = $fineWeight * $item['ratePerGram'];
        
        // Step 4: Calculate Wastage (enhanced with value calculation)
        $wastageWeight = isset($item['wastagePercentage']) 
            ? $netWeight * ($item['wastagePercentage'] / 100) 
            : 0;
        
        $wastageValue = $wastageWeight > 0 
            ? $wastageWeight * ($purityPercentage / 100) * $item['ratePerGram'] 
            : 0;
        
        // Step 5: Calculate Making Charges
        $makingCharges = isset($item['makingChargesRate']) 
            ? $netWeight * $item['makingChargesRate'] 
            : 0;
        
        // Step 6: Stone Amount
        $stoneAmount = $item['stoneAmount'] ?? 0;

        // Step 7: Calculate Total (rates typically include GST in jewelry business)
        $totalAmount = $metalValue + $wastageValue + $makingCharges + $stoneAmount;

        // Step 8: GST Calculation (Optional - only if rates are GST exclusive)
        $includeGst = $item['includeGst'] ?? false; // Default: rates include GST
        $cgst = 0;
        $sgst = 0;
        $totalGst = 0;
        $grandTotal = $totalAmount;

        if ($includeGst) {
            // Only add GST if specifically requested (rates are GST exclusive)
            $cgst = $totalAmount * 0.015; // 1.5%
            $sgst = $totalAmount * 0.015; // 1.5%
            $totalGst = $cgst + $sgst;
            $grandTotal = $totalAmount + $totalGst;
        }
        
        return [
            'netWeight' => $netWeight,
            'fineWeight' => $fineWeight,
            'metalValue' => $metalValue,
            'wastageWeight' => $wastageWeight,
            'wastageValue' => $wastageValue,
            'makingCharges' => $makingCharges,
            'stoneAmount' => $stoneAmount,
            'totalAmount' => $totalAmount, // Total without additional GST
            'cgst' => $cgst,
            'sgst' => $sgst,
            'totalGst' => $totalGst,
            'grandTotal' => $grandTotal, // Final total (with GST only if requested)
            'purityUsed' => $purityPercentage,
            'tunchValue' => $purityInfo['tunch'],
            'purityInfo' => $purityInfo,
            'gstIncluded' => $includeGst
        ];
    }
    
    /**
     * Calculate multiple items with detailed breakdown
     */
    public static function calculateMultipleItems($items) {
        $calculatedItems = array_map([self::class, 'calculateJewelryItem'], $items);

        $subtotal = array_sum(array_column($calculatedItems, 'totalAmount'));
        $totalWastageValue = array_sum(array_column($calculatedItems, 'wastageValue'));
        $totalMakingCharges = array_sum(array_column($calculatedItems, 'makingCharges'));
        $totalStoneAmount = array_sum(array_column($calculatedItems, 'stoneAmount'));
        $totalCgst = array_sum(array_column($calculatedItems, 'cgst'));
        $totalSgst = array_sum(array_column($calculatedItems, 'sgst'));
        $totalGst = $totalCgst + $totalSgst;
        $grandTotal = array_sum(array_column($calculatedItems, 'grandTotal'));
        
        // Calculate summary stats
        $totalGrossWeight = array_sum(array_column($items, 'grossWeight'));
        $totalNetWeight = array_sum(array_column($calculatedItems, 'netWeight'));
        $totalFineWeight = array_sum(array_column($calculatedItems, 'fineWeight'));
        $averagePurity = $totalNetWeight > 0 ? ($totalFineWeight / $totalNetWeight) * 100 : 0;
        
        return [
            'items' => $calculatedItems,
            'subtotal' => $subtotal,
            'totalWastageValue' => $totalWastageValue,
            'totalMakingCharges' => $totalMakingCharges,
            'totalStoneAmount' => $totalStoneAmount,
            'totalCgst' => $totalCgst,
            'totalSgst' => $totalSgst,
            'totalGst' => $totalGst,
            'grandTotal' => $grandTotal,
            'summary' => [
                'totalGrossWeight' => $totalGrossWeight,
                'totalNetWeight' => $totalNetWeight,
                'totalFineWeight' => $totalFineWeight,
                'averagePurity' => $averagePurity
            ]
        ];
    }
    
    /**
     * Enhanced validation with purity format validation
     */
    public static function validateJewelryItem($item) {
        $errors = [];
        
        if (!isset($item['grossWeight']) || $item['grossWeight'] <= 0) {
            $errors[] = "Gross weight must be greater than 0";
        }
        
        if (isset($item['stoneWeight']) && $item['stoneWeight'] < 0) {
            $errors[] = "Stone weight cannot be negative";
        }
        
        if (isset($item['stoneWeight']) && isset($item['grossWeight']) && 
            $item['stoneWeight'] >= $item['grossWeight']) {
            $errors[] = "Stone weight cannot be greater than or equal to gross weight";
        }
        
        if (!isset($item['purity'])) {
            $errors[] = "Purity is required";
        } else {
            $validation = PurityParser::validatePurity($item['purity'], $item['metalType'] ?? null);
            if (!$validation['valid']) {
                $errors = array_merge($errors, $validation['errors']);
            }
        }
        
        if (!isset($item['ratePerGram']) || $item['ratePerGram'] <= 0) {
            $errors[] = "Rate per gram must be greater than 0";
        }
        
        if (isset($item['wastagePercentage']) && 
            ($item['wastagePercentage'] < 0 || $item['wastagePercentage'] > 100)) {
            $errors[] = "Wastage percentage must be between 0 and 100";
        }
        
        if (isset($item['makingChargesRate']) && $item['makingChargesRate'] < 0) {
            $errors[] = "Making charges rate cannot be negative";
        }
        
        if (isset($item['stoneAmount']) && $item['stoneAmount'] < 0) {
            $errors[] = "Stone amount cannot be negative";
        }
        
        return $errors;
    }
    
    /**
     * Utility functions for formatting
     */
    public static function formatCurrency($amount) {
        return '₹' . number_format($amount, 2);
    }
    
    public static function formatWeight($weight) {
        return number_format($weight, 3) . 'g';
    }
    
    public static function formatPercentage($percentage) {
        return number_format($percentage, 2) . '%';
    }
    
    public static function formatTunch($tunch) {
        return (string)round($tunch);
    }
    
    /**
     * Get variable purity examples for testing
     */
    public static function getVariablePurityExamples() {
        $examples = [
            [
                'description' => 'Custom 75% Purity Gold',
                'item' => [
                    'grossWeight' => 16.210,
                    'stoneWeight' => 0.345,
                    'purity' => 75, // ANY number = direct percentage
                    'ratePerGram' => 10112,
                    'wastagePercentage' => 1,
                    'makingChargesRate' => 150,
                    'stoneAmount' => 5600,
                    'metalType' => 'Gold'
                ]
            ],
            [
                'description' => 'Custom 91% Purity Gold',
                'item' => [
                    'grossWeight' => 20.0,
                    'stoneWeight' => 1.0,
                    'purity' => 91, // 91 = 91%
                    'ratePerGram' => 9500,
                    'wastagePercentage' => 2,
                    'makingChargesRate' => 200,
                    'stoneAmount' => 3000,
                    'metalType' => 'Gold'
                ]
            ],
            [
                'description' => 'Custom 102% Purity (Above 100%)',
                'item' => [
                    'grossWeight' => 15.0,
                    'purity' => 102, // 102 = 102%
                    'ratePerGram' => 11500,
                    'wastagePercentage' => 1.5,
                    'makingChargesRate' => 250,
                    'stoneAmount' => 2000,
                    'metalType' => 'Gold'
                ]
            ],
            [
                'description' => 'Custom 85.5% Purity Silver',
                'item' => [
                    'grossWeight' => 25.5,
                    'purity' => 85.5, // 85.5 = 85.5%
                    'ratePerGram' => 85,
                    'wastagePercentage' => 0.5,
                    'makingChargesRate' => 25,
                    'metalType' => 'Silver'
                ]
            ],
            [
                'description' => 'Standard 916 (Reference Table)',
                'item' => [
                    'grossWeight' => 18.0,
                    'purity' => '916', // String lookup in reference table
                    'ratePerGram' => 10200,
                    'makingChargesRate' => 175,
                    'metalType' => 'Gold'
                ]
            ]
        ];
        
        $results = [];
        foreach ($examples as $example) {
            $results[] = [
                'description' => $example['description'],
                'item' => $example['item'],
                'result' => self::calculateJewelryItem($example['item'])
            ];
        }
        
        return $results;
    }
    
    /**
     * Test variable purity functionality
     */
    public static function testVariablePurity() {
        $output = "<h2>🧮 Variable Purity (Tunch) Testing</h2>\n";
        $output .= "<div style='font-family: monospace;'>\n";
        
        $examples = self::getVariablePurityExamples();
        
        foreach ($examples as $index => $example) {
            $output .= "<h4>" . ($index + 1) . ". {$example['description']}</h4>\n";
            $output .= "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
            $output .= "<strong>Input:</strong><br>\n";
            $output .= "Gross Weight: " . self::formatWeight($example['item']['grossWeight']) . "<br>\n";
            $output .= "Purity Input: {$example['item']['purity']}<br>\n";
            $output .= "<strong>Results:</strong><br>\n";
            $output .= "Purity Used: " . self::formatPercentage($example['result']['purityUsed']) . "<br>\n";
            $output .= "Tunch Value: " . self::formatTunch($example['result']['tunchValue']) . "<br>\n";
            $output .= "Net Weight: " . self::formatWeight($example['result']['netWeight']) . "<br>\n";
            $output .= "Fine Weight: " . self::formatWeight($example['result']['fineWeight']) . "<br>\n";
            $output .= "Metal Value: " . self::formatCurrency($example['result']['metalValue']) . "<br>\n";
            $output .= "Grand Total: " . self::formatCurrency($example['result']['grandTotal']) . "<br>\n";
            $output .= "</div>\n";
        }
        
        $output .= "</div>\n";
        return $output;
    }
}
?>
