<?php
/**
 * Test TCPDF Direct Implementation
 */

require_once 'config/database.php';

// Check if TCPDF is available
$tcpdfPath = 'vendor/tecnickcom/tcpdf/tcpdf.php';
if (!file_exists($tcpdfPath)) {
    die('TCPDF not found. Please install TCPDF first.');
}

require_once $tcpdfPath;

startSession();
$db = getDB();
$sale_id = $_GET['id'] ?? null;

if (!$sale_id) {
    die('Sale ID is required');
}

try {
    // Get sale details
    $sale = $db->fetch("
        SELECT s.*, c.customer_name, c.business_name, c.phone, c.email, c.address, c.gst_number
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.id = ?
    ", [$sale_id]);

    if (!$sale) {
        die('Sale not found');
    }

    // Get sale items
    $sale_items = $db->fetchAll("
        SELECT si.*, p.product_name, p.product_code, p.metal_type, p.purity, p.hsn_code
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$sale_id]);

} catch (Exception $e) {
    die('Error loading sale data: ' . $e->getMessage());
}

// Create new PDF document
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Set document information
$pdf->SetCreator('Indian Jewellery Wholesale System');
$pdf->SetAuthor('System Generated');
$pdf->SetTitle('Bill - ' . $sale['bill_number']);
$pdf->SetSubject('Sales Invoice');

// Set default header data
$pdf->SetHeaderData('', 0, APP_NAME, "Indian Jewellery Wholesale Management System\nBill Number: " . $sale['bill_number']);

// Set header and footer fonts
$pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
$pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

// Set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

// Set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

// Set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Add a page
$pdf->AddPage();

// Set font
$pdf->SetFont('helvetica', '', 10);

// Build HTML content
$html = '
<style>
    .header { text-align: center; margin-bottom: 20px; }
    .bill-info { margin-bottom: 20px; }
    .customer-info { width: 48%; float: left; }
    .bill-details { width: 48%; float: right; }
    .section-title { font-weight: bold; font-size: 12px; margin-bottom: 5px; border-bottom: 1px solid #ccc; }
    table { border-collapse: collapse; width: 100%; }
    th { background-color: #f0f0f0; font-weight: bold; padding: 5px; border: 1px solid #ccc; }
    td { padding: 5px; border: 1px solid #ccc; }
    .text-right { text-align: right; }
    .text-center { text-align: center; }
    .totals-table { width: 60%; margin-left: auto; margin-top: 15px; }
    .grand-total { font-weight: bold; background-color: #f0f0f0; }
    .clearfix { clear: both; }
</style>

<div class="bill-info">
    <div class="customer-info">
        <div class="section-title">Bill To:</div>';

if ($sale['customer_name']) {
    $html .= '<strong>' . htmlspecialchars($sale['customer_name']) . '</strong><br>';
    if ($sale['business_name']) {
        $html .= htmlspecialchars($sale['business_name']) . '<br>';
    }
    if ($sale['phone']) {
        $html .= 'Phone: ' . htmlspecialchars($sale['phone']) . '<br>';
    }
    if ($sale['gst_number']) {
        $html .= 'GST: ' . htmlspecialchars($sale['gst_number']);
    }
} else {
    $html .= '<em>Walk-in Customer</em>';
}

$html .= '
    </div>
    
    <div class="bill-details">
        <div class="section-title">Bill Details:</div>
        <strong>Bill No:</strong> ' . htmlspecialchars($sale['bill_number']) . '<br>
        <strong>Date:</strong> ' . date('d/m/Y', strtotime($sale['sale_date'])) . '<br>
        <strong>Time:</strong> ' . date('H:i:s', strtotime($sale['sale_time'])) . '<br>
        <strong>Payment:</strong> ' . ucfirst($sale['payment_method']) . '<br>
        <strong>Status:</strong> ' . ucfirst(str_replace('_', ' ', $sale['payment_status'])) . '
    </div>
    <div class="clearfix"></div>
</div>

<table>
    <thead>
        <tr>
            <th width="8%">S.No</th>
            <th width="35%">Product</th>
            <th width="10%">HSN</th>
            <th width="8%">Qty</th>
            <th width="12%">Weight (g)</th>
            <th width="12%">Rate</th>
            <th width="15%">Amount</th>
        </tr>
    </thead>
    <tbody>';

$sr_no = 1;
foreach ($sale_items as $item) {
    $html .= '<tr>
        <td class="text-center">' . $sr_no++ . '</td>
        <td>
            <strong>' . htmlspecialchars($item['product_name']) . '</strong><br>
            <small>' . htmlspecialchars($item['product_code']) . '</small>';
    
    if ($item['metal_type']) {
        $html .= '<br><small>' . htmlspecialchars($item['metal_type'] . ' ' . $item['purity']) . '</small>';
    }
    
    $html .= '</td>
        <td class="text-center">' . htmlspecialchars($item['hsn_code'] ?: '-') . '</td>
        <td class="text-center">' . $item['quantity'] . '</td>
        <td class="text-right">' . number_format($item['total_weight'], 3) . '</td>
        <td class="text-right">' . formatCurrency($item['unit_price']) . '</td>
        <td class="text-right">' . formatCurrency($item['total_price']) . '</td>
    </tr>';
}

$html .= '
    </tbody>
</table>

<table class="totals-table">
    <tr>
        <td>Subtotal:</td>
        <td class="text-right">' . formatCurrency($sale['subtotal']) . '</td>
    </tr>';

if ($sale['discount_amount'] > 0) {
    $html .= '<tr>
        <td>Discount:</td>
        <td class="text-right">-' . formatCurrency($sale['discount_amount']) . '</td>
    </tr>';
}

if ($sale['tax_amount'] > 0) {
    $html .= '<tr>
        <td>Tax:</td>
        <td class="text-right">' . formatCurrency($sale['tax_amount']) . '</td>
    </tr>';
}

if ($sale['round_off'] != 0) {
    $html .= '<tr>
        <td>Round Off:</td>
        <td class="text-right">' . formatCurrency($sale['round_off']) . '</td>
    </tr>';
}

$html .= '
    <tr class="grand-total">
        <td><strong>Grand Total:</strong></td>
        <td class="text-right"><strong>' . formatCurrency($sale['grand_total']) . '</strong></td>
    </tr>
    <tr>
        <td>Paid Amount:</td>
        <td class="text-right">' . formatCurrency($sale['paid_amount']) . '</td>
    </tr>';

if ($sale['balance_amount'] > 0) {
    $html .= '<tr>
        <td><strong>Balance Due:</strong></td>
        <td class="text-right"><strong>' . formatCurrency($sale['balance_amount']) . '</strong></td>
    </tr>';
}

$html .= '
</table>

<div style="margin-top: 30px; text-align: center; font-size: 9px; color: #666;">
    <p>Thank you for your business!</p>
    <p>This is a computer generated bill.</p>
    <p>Generated on: ' . date('d/m/Y H:i:s') . '</p>
</div>';

// Print text using writeHTMLCell()
$pdf->writeHTML($html, true, false, true, false, '');

// Generate filename
$filename = 'Bill_' . $sale['bill_number'] . '_' . date('Y-m-d') . '.pdf';

// Close and output PDF document
$pdf->Output($filename, 'D');
?>
