<!-- DASHBOARD PAGE -->
<div class="row mb-4">
    <div class="col-12">
        <div class="formula-display">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4><i class="fas fa-calculator"></i> Production Formula Applied</h4>
                    <p class="mb-0">
                        <strong>Metal Value = Tunch Weight × Base Rate</strong> (Direct Multiplication)<br>
                        <small>Example: 10.120g × 96% = 9.7159g × ₹10,112 = ₹98,240.10 ✅</small>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-check-circle fa-3x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- STATISTICS CARDS -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-boxes fa-2x mb-3"></i>
                <h3><?php echo number_format($data['stats']['total_items']); ?></h3>
                <p class="mb-0">Total Items</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-rupee-sign fa-2x mb-3"></i>
                <h3>₹<?php echo number_format($data['stats']['total_inventory_value'], 0); ?></h3>
                <p class="mb-0">Inventory Value</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-3"></i>
                <h3>₹<?php echo number_format($data['stats']['today_sales'], 0); ?></h3>
                <p class="mb-0">Today's Sales</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h3><?php echo $data['stats']['low_stock_count']; ?></h3>
                <p class="mb-0">Low Stock Items</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- RECENT BILLS -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-file-invoice-dollar"></i> Recent Bills</h5>
                <a href="?page=billing" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($data['recent_bills'])): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Customer</th>
                                    <th>Product</th>
                                    <th>Net Wt</th>
                                    <th>Metal Value</th>
                                    <th>Final Value</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['recent_bills'] as $bill): ?>
                                <tr>
                                    <td><strong><?php echo $bill['invoice_number']; ?></strong></td>
                                    <td><?php echo htmlspecialchars($bill['customer_name']); ?></td>
                                    <td><?php echo htmlspecialchars($bill['product_name']); ?></td>
                                    <td><?php echo number_format($bill['net_wt'], 3); ?>g</td>
                                    <td>₹<?php echo number_format($bill['metal_value'], 2); ?></td>
                                    <td><strong>₹<?php echo number_format($bill['final_value'], 2); ?></strong></td>
                                    <td><?php echo date('M j', strtotime($bill['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No bills created yet</p>
                        <a href="?page=billing" class="btn btn-primary">Create First Bill</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- LOW STOCK ALERTS -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock</h5>
                <a href="?page=inventory" class="btn btn-sm btn-warning">Manage</a>
            </div>
            <div class="card-body">
                <?php if (!empty($data['low_stock'])): ?>
                    <?php foreach ($data['low_stock'] as $item): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                        <div>
                            <strong><?php echo htmlspecialchars($item['product_name']); ?></strong><br>
                            <small class="text-muted">Stock: <?php echo number_format($item['balance_in_stock'], 3); ?>g</small>
                        </div>
                        <span class="badge bg-warning">Low</span>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted mb-0">All items well stocked</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- QUICK ACTIONS -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="?page=billing" class="btn btn-success">
                        <i class="fas fa-plus"></i> Create New Bill
                    </a>
                    <a href="?page=inventory" class="btn btn-primary">
                        <i class="fas fa-box"></i> Add Inventory
                    </a>
                    <a href="?page=reports" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MONTHLY SALES CHART PLACEHOLDER -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-area"></i> Monthly Sales Overview</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="p-3">
                            <h4 class="text-primary">₹<?php echo number_format($data['stats']['month_sales'], 0); ?></h4>
                            <p class="text-muted mb-0">This Month</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-3">
                            <h4 class="text-success">₹<?php echo number_format($data['stats']['total_inventory_value'], 0); ?></h4>
                            <p class="text-muted mb-0">Inventory Value</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-3">
                            <h4 class="text-info"><?php echo number_format($data['stats']['total_items']); ?></h4>
                            <p class="text-muted mb-0">Total Products</p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <p class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        All calculations use the correct formula: <strong>Metal Value = Tunch Weight × Base Rate</strong>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
