<!-- BILLING PAGE -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> Correct Formula Applied</h5>
            </div>
            <div class="card-body">
                <div class="formula-display">
                    <h6><i class="fas fa-check-circle"></i> Production Formula</h6>
                    <ol class="mb-0">
                        <li><strong>Net Weight</strong> = Gross Weight - Stone Weight</li>
                        <li><strong>Tunch Weight</strong> = Net Weight × (Tunch % ÷ 100)</li>
                        <li><strong>Metal Value</strong> = Tunch Weight × Base Rate</li>
                        <li><strong>Final Value</strong> = Metal Value + Making + Stone + Tax - Discount</li>
                    </ol>
                    <div class="mt-2">
                        <small><strong>✅ Example:</strong> 10.120g × 96% = 9.7159g × ₹10,112 = ₹98,240.10</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-invoice-dollar"></i> Create New Bill</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="create_bill" value="1">
                    
                    <!-- Customer Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Customer Name *</label>
                                <input type="text" class="form-control" name="customer_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Location *</label>
                                <input type="text" class="form-control" name="location" required>
                            </div>
                        </div>
                    </div>

                    <!-- Product Information -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Product Name *</label>
                                <input type="text" class="form-control" name="product_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Link to Inventory</label>
                                <select class="form-control" name="inventory_id">
                                    <option value="">Select Item</option>
                                    <?php foreach ($data['inventory'] as $item): ?>
                                        <option value="<?php echo $item['id']; ?>" 
                                                data-weight="<?php echo $item['current_wt']; ?>"
                                                data-stone="<?php echo $item['current_wt'] - $item['without_stone_wt']; ?>">
                                            <?php echo htmlspecialchars($item['product_name']); ?> 
                                            (<?php echo number_format($item['balance_in_stock'], 3); ?>g)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Weight Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Gross Weight (g) *</label>
                                <input type="number" step="0.001" class="form-control" name="gross_wt" id="gross_wt" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Stone Weight (g) *</label>
                                <input type="number" step="0.001" class="form-control" name="stone_wt" id="stone_wt" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Net Weight (g)</label>
                                <input type="number" step="0.001" class="form-control" name="net_wt" id="net_wt" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- Calculation Parameters -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tunch Percentage (%) *</label>
                                <input type="number" step="0.01" class="form-control" name="tunch_percentage" id="tunch_percentage" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Base Rate (₹ per 10g) *</label>
                                <input type="number" step="0.01" class="form-control" name="base_rate" id="base_rate" required>
                            </div>
                        </div>
                    </div>

                    <!-- Live Calculation Preview -->
                    <div class="calculation-preview mb-3">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small><strong>Tunch Weight:</strong><br>
                                <span id="tunchWeightDisplay" class="text-primary">0.000g</span></small>
                            </div>
                            <div class="col-md-3">
                                <small><strong>Metal Value:</strong><br>
                                <span id="metalValueDisplay" class="text-success">₹0.00</span></small>
                            </div>
                            <div class="col-md-3">
                                <small><strong>Subtotal:</strong><br>
                                <span id="subtotalDisplay" class="text-info">₹0.00</span></small>
                            </div>
                            <div class="col-md-3">
                                <small><strong>Final Value:</strong><br>
                                <span id="finalValueDisplay" class="text-danger">₹0.00</span></small>
                            </div>
                        </div>
                        <div class="mt-2" id="calculationDisplay">
                            <p class="text-muted text-center">Enter values to see live calculation</p>
                        </div>
                    </div>

                    <!-- Additional Charges -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Making Charges (₹)</label>
                                <input type="number" step="0.01" class="form-control" name="making_charges" id="making_charges" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Stone Charges (₹)</label>
                                <input type="number" step="0.01" class="form-control" name="stone_charges" id="stone_charges" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- Discount and Tax -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Discount (%)</label>
                                <input type="number" step="0.01" class="form-control" name="discount_percentage" id="discount_percentage" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tax (%)</label>
                                <input type="number" step="0.01" class="form-control" name="tax_percentage" id="tax_percentage" value="0">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success btn-lg w-100">
                        <i class="fas fa-file-invoice-dollar"></i> Create Bill (Production Formula)
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bills -->
<?php if (!empty($data['recent_bills'])): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history"></i> Recent Bills</h5>
                <span class="badge bg-primary"><?php echo count($data['recent_bills']); ?> bills</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Product</th>
                                <th>Net Wt</th>
                                <th>Tunch %</th>
                                <th>Tunch Wt</th>
                                <th>Base Rate</th>
                                <th>Metal Value</th>
                                <th>Making</th>
                                <th>Stone</th>
                                <th>Final Value</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['recent_bills'] as $bill): ?>
                            <tr>
                                <td><strong><?php echo $bill['invoice_number']; ?></strong></td>
                                <td><?php echo htmlspecialchars($bill['customer_name']); ?></td>
                                <td><?php echo htmlspecialchars($bill['product_name']); ?></td>
                                <td><?php echo number_format($bill['net_wt'], 3); ?>g</td>
                                <td><?php echo $bill['tunch_percentage']; ?>%</td>
                                <td><?php echo number_format($bill['tunch_weight'], 3); ?>g</td>
                                <td>₹<?php echo number_format($bill['base_rate'], 2); ?></td>
                                <td><strong>₹<?php echo number_format($bill['metal_value'], 2); ?></strong></td>
                                <td>₹<?php echo number_format($bill['making_charges'], 2); ?></td>
                                <td>₹<?php echo number_format($bill['stone_charges'], 2); ?></td>
                                <td><strong class="text-success">₹<?php echo number_format($bill['final_value'], 2); ?></strong></td>
                                <td>
                                    <span class="badge bg-<?php echo $bill['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                        <?php echo ucfirst($bill['payment_status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($bill['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
