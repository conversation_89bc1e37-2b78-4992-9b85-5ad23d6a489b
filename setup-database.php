<?php
/**
 * Quick Database Setup Script
 * Run this script to create the database and tables automatically
 */

try {
    // Database configuration
    $host = 'localhost';
    $dbname = 'jewellery_wholesale_v2';
    $username = 'root';
    $password = '';
    
    echo "🔧 Setting up Indian Jewellery Wholesale Management System v2.0...\n\n";
    
    // Connect to MySQL server (without database)
    echo "📡 Connecting to MySQL server...\n";
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "✅ Connected to MySQL server successfully!\n\n";
    
    // Create database if it doesn't exist
    echo "🗄️ Creating database '$dbname'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$dbname`");
    echo "✅ Database '$dbname' created/selected successfully!\n\n";
    
    // Read and execute schema
    echo "📋 Reading database schema...\n";
    $schema = file_get_contents('database/schema.sql');
    if (!$schema) {
        throw new Exception("Could not read database/schema.sql file");
    }
    echo "✅ Schema file loaded successfully!\n\n";
    
    // Execute the entire schema as one statement
    echo "🔨 Creating database tables and inserting data...\n";
    try {
        $pdo->exec($schema);
        echo "✅ Database schema executed successfully!\n";
    } catch (PDOException $e) {
        echo "❌ Error executing schema: " . $e->getMessage() . "\n";
        throw $e;
    }

    
    // Verify tables were created
    echo "🔍 Verifying database setup...\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = [
        'users', 'settings', 'metal_rates', 'suppliers', 'categories', 
        'products', 'inventory', 'customers', 'sales', 'sale_items', 
        'stock_movements', 'notifications', 'audit_logs'
    ];
    
    $missingTables = array_diff($expectedTables, $tables);
    
    if (empty($missingTables)) {
        echo "✅ All " . count($tables) . " tables created successfully!\n";
        echo "📊 Tables: " . implode(', ', $tables) . "\n\n";
    } else {
        echo "⚠️ Missing tables: " . implode(', ', $missingTables) . "\n\n";
    }
    
    // Check if default data was inserted
    echo "📝 Verifying default data...\n";
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $categoryCount = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
    $settingsCount = $pdo->query("SELECT COUNT(*) FROM settings")->fetchColumn();
    $ratesCount = $pdo->query("SELECT COUNT(*) FROM metal_rates")->fetchColumn();
    
    echo "✅ Default data inserted:\n";
    echo "   👤 Users: $userCount\n";
    echo "   📂 Categories: $categoryCount\n";
    echo "   ⚙️ Settings: $settingsCount\n";
    echo "   💰 Metal Rates: $ratesCount\n\n";
    
    // Create installation marker
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    file_put_contents('config/.installed', date('Y-m-d H:i:s'));
    
    echo "🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!\n\n";
    echo "🔐 Default Admin Credentials:\n";
    echo "   Username: admin\n";
    echo "   Password: admin123\n\n";
    echo "🌐 You can now access the system at: http://localhost:8000/v2/\n";
    echo "🔑 Login page: http://localhost:8000/v2/login.php\n\n";
    echo "⚠️ IMPORTANT: Please change the default admin password after logging in!\n\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "💡 Please check your database configuration and try again.\n";
    exit(1);
}
?>
