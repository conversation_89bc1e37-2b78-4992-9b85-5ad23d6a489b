<?php
/**
 * Enhanced PDF Generator
 * Uses TCPDF if available, otherwise falls back to HTML2PDF
 */

// Try to include TCPDF if it exists
$tcpdfPath = __DIR__ . "/../vendor/tecnickcom/tcpdf/tcpdf.php";
$tcpdfAvailable = file_exists($tcpdfPath);

if ($tcpdfAvailable) {
    require_once $tcpdfPath;
}

class EnhancedPDF {
    private $pdf;
    private $useTCPDF;
    
    public function __construct($orientation = "P", $unit = "mm", $format = "A4") {
        global $tcpdfAvailable;
        
        $this->useTCPDF = $tcpdfAvailable;
        
        if ($this->useTCPDF) {
            $this->pdf = new TCPDF($orientation, $unit, $format);
            $this->pdf->SetCreator("Indian Jewellery Wholesale System");
            $this->pdf->SetAuthor("System Generated");
            $this->pdf->SetTitle("Bill/Invoice");
            $this->pdf->SetMargins(15, 15, 15);
            $this->pdf->SetAutoPageBreak(TRUE, 15);
        }
    }
    
    public function addPage() {
        if ($this->useTCPDF) {
            $this->pdf->AddPage();
        }
    }
    
    public function setFont($family, $style = "", $size = 12) {
        if ($this->useTCPDF) {
            $this->pdf->SetFont($family, $style, $size);
        }
    }
    
    public function writeHTML($html) {
        if ($this->useTCPDF) {
            $this->pdf->writeHTML($html, true, false, true, false, "");
        }
    }
    
    public function output($filename = "document.pdf", $mode = "D") {
        if ($this->useTCPDF) {
            $this->pdf->Output($filename, $mode);
        } else {
            // Fallback to enhanced HTML method
            $this->outputHTML($filename, $mode);
        }
    }
    
    private function outputHTML($filename, $mode) {
        if ($mode == "D") {
            header("Content-Type: application/pdf");
            header("Content-Disposition: attachment; filename=\"$filename\"");
        }
        
        // Enhanced HTML with better PDF styling
        echo "<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <title>PDF Document</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .pdf-container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .totals-table {
            width: 300px;
            margin-left: auto;
            margin-top: 20px;
        }
        .totals-table td {
            border: none;
            padding: 5px 10px;
        }
        .grand-total {
            font-weight: bold;
            border-top: 2px solid #333;
        }
    </style>
</head>
<body>
    <div class=\"pdf-container\">
        <!-- Content will be inserted here -->
    </div>
    <script>
        // Auto-print for PDF generation
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>";
    }
    
    public function isUsingTCPDF() {
        return $this->useTCPDF;
    }
}
?>