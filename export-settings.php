<?php
/**
 * Export Settings Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'json';

try {
    // Get all settings
    $settings = $db->fetchAll("
        SELECT setting_key, setting_value, setting_type, description
        FROM settings 
        WHERE is_active = 1
        ORDER BY setting_key
    ");

    if ($format === 'json') {
        // JSON Export
        $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $exportData = [
            'export_info' => [
                'version' => '2.0',
                'exported_at' => date('Y-m-d H:i:s'),
                'total_settings' => count($settings)
            ],
            'settings' => []
        ];

        foreach ($settings as $setting) {
            $exportData['settings'][$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }

        echo json_encode($exportData, JSON_PRETTY_PRINT);
        exit;

    } elseif ($format === 'csv') {
        // CSV Export
        $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = ['Setting Key', 'Setting Value', 'Type', 'Description'];
        fputcsv($output, $headers);

        foreach ($settings as $setting) {
            $row = [
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['setting_type'],
                $setting['description']
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'backup') {
        // Full System Backup
        $filename = 'system_backup_' . date('Y-m-d_H-i-s') . '.sql';
        
        header('Content-Type: application/sql');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        // Get database name
        $dbName = $db->fetch("SELECT DATABASE() as db_name")['db_name'];
        
        echo "-- System Backup for Indian Jewellery Wholesale Management System v2.0\n";
        echo "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        echo "-- Database: $dbName\n\n";
        
        echo "SET FOREIGN_KEY_CHECKS = 0;\n";
        echo "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
        echo "SET AUTOCOMMIT = 0;\n";
        echo "START TRANSACTION;\n\n";

        // Get all tables
        $tables = $db->fetchAll("SHOW TABLES");
        $tableKey = "Tables_in_$dbName";

        foreach ($tables as $table) {
            $tableName = $table[$tableKey];
            
            // Get table structure
            $createTable = $db->fetch("SHOW CREATE TABLE `$tableName`");
            echo "-- Table structure for `$tableName`\n";
            echo "DROP TABLE IF EXISTS `$tableName`;\n";
            echo $createTable['Create Table'] . ";\n\n";
            
            // Get table data
            $rows = $db->fetchAll("SELECT * FROM `$tableName`");
            if (!empty($rows)) {
                echo "-- Data for table `$tableName`\n";
                
                // Get column names
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';
                
                echo "INSERT INTO `$tableName` ($columnList) VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                echo implode(",\n", $values) . ";\n\n";
            }
        }
        
        echo "SET FOREIGN_KEY_CHECKS = 1;\n";
        echo "COMMIT;\n";
        echo "\n-- Backup completed successfully\n";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting settings: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='settings.php'>← Back to Settings</a></p>";
    echo "</div>";
}
?>
