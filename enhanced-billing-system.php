<?php
/**
 * Enhanced Billing System with Advanced Purity Handling
 * Implements the TypeScript reference system in PHP
 */

require_once 'config/database.php';
require_once 'lib/PurityParser.php';
require_once 'lib/EnhancedJewelryCalculator.php';

startSession();
$db = getDB();

$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'calculate_jewelry') {
        try {
            // Prepare jewelry item data
            $item = [
                'grossWeight' => floatval($_POST['gross_weight']),
                'stoneWeight' => floatval($_POST['stone_weight'] ?? 0),
                'purity' => $_POST['purity'], // Can be string or number
                'metalType' => $_POST['metal_type'],
                'ratePerGram' => floatval($_POST['rate_per_gram']),
                'wastagePercentage' => floatval($_POST['wastage_percentage'] ?? 0),
                'makingChargesRate' => floatval($_POST['making_charges_rate'] ?? 0),
                'stoneAmount' => floatval($_POST['stone_amount'] ?? 0),
                'includeGst' => isset($_POST['include_gst']) && $_POST['include_gst'] === '1'
            ];
            
            // Validate the item
            $validationErrors = EnhancedJewelryCalculator::validateJewelryItem($item);
            
            if (!empty($validationErrors)) {
                $error = "❌ Validation Errors: " . implode(', ', $validationErrors);
            } else {
                // Calculate the jewelry item
                $calculation = EnhancedJewelryCalculator::calculateJewelryItem($item);
                
                // Store in database
                $sql = "INSERT INTO enhanced_calculations (
                    customer_name, product_name, metal_type, gross_weight, stone_weight, net_weight,
                    purity_input, purity_percentage, tunch_value, purity_format, purity_label,
                    rate_per_gram, fine_weight, metal_value, wastage_percentage, wastage_weight,
                    wastage_value, making_charges_rate, making_charges, stone_amount,
                    total_before_tax, cgst, sgst, total_gst, grand_total, gst_included, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $params = [
                    $_POST['customer_name'],
                    $_POST['product_name'],
                    $item['metalType'],
                    $item['grossWeight'],
                    $item['stoneWeight'],
                    $calculation['netWeight'],
                    $_POST['purity'],
                    $calculation['purityUsed'],
                    $calculation['tunchValue'],
                    $calculation['purityInfo']['format'],
                    $calculation['purityInfo']['label'],
                    $item['ratePerGram'],
                    $calculation['fineWeight'],
                    $calculation['metalValue'],
                    $item['wastagePercentage'],
                    $calculation['wastageWeight'],
                    $calculation['wastageValue'],
                    $item['makingChargesRate'],
                    $calculation['makingCharges'],
                    $calculation['stoneAmount'],
                    $calculation['totalAmount'], // Total without additional GST
                    $calculation['cgst'],
                    $calculation['sgst'],
                    $calculation['totalGst'],
                    $calculation['grandTotal'],
                    $calculation['gstIncluded'] ? 1 : 0
                ];
                
                $db->execute($sql, $params);
                $success = "✅ Jewelry calculation completed! Grand Total: " . EnhancedJewelryCalculator::formatCurrency($calculation['grandTotal']);
                
                // Store calculation for display
                $_SESSION['last_calculation'] = $calculation;
                $_SESSION['last_item'] = $item;
            }
            
        } catch (Exception $e) {
            $error = "❌ Calculation Error: " . $e->getMessage();
        }
    }
}

// Get recent calculations
$recentCalculations = $db->fetchAll("SELECT * FROM enhanced_calculations ORDER BY id DESC LIMIT 10");

// Get purity suggestions for different metals
$goldPurities = PurityParser::getPuritySuggestions('Gold');
$silverPurities = PurityParser::getPuritySuggestions('Silver');
$platinumPurities = PurityParser::getPuritySuggestions('Platinum');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Billing System - Advanced Purity Handling</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .billing-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
        }
        .section-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .calculation-preview {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .purity-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .purity-suggestions {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        .purity-option {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
        }
        .purity-option:hover {
            background-color: #f8f9fa;
        }
        .advanced-features {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="billing-header">
        <div class="container">
            <h1 class="mb-0"><i class="fas fa-gem me-2"></i>Enhanced Billing System</h1>
            <p class="mb-0">Advanced Purity Handling & Comprehensive Jewelry Calculations</p>
        </div>
    </div>

    <div class="container-fluid mt-4">
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Advanced Features Info -->
        <div class="advanced-features">
            <h4><i class="fas fa-star me-2"></i>Advanced Features</h4>
            <div class="row">
                <div class="col-md-3">
                    <h6>🎯 Flexible Purity Input</h6>
                    <small>• Standard codes (916, 22K, 925)<br>• Direct percentages (91.6, 75)<br>• Any custom value</small>
                </div>
                <div class="col-md-3">
                    <h6>🧮 Comprehensive Calculations</h6>
                    <small>• Fine weight calculations<br>• Wastage value included<br>• GST breakdown (CGST/SGST)</small>
                </div>
                <div class="col-md-3">
                    <h6>💎 Multi-Metal Support</h6>
                    <small>• Gold (10K to 24K)<br>• Silver (800 to 999)<br>• Platinum (850 to 999)</small>
                </div>
                <div class="col-md-3">
                    <h6>📊 Real-time Validation</h6>
                    <small>• Purity format checking<br>• Weight validations<br>• Rate validations</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Enhanced Calculation Form -->
            <div class="col-md-8">
                <div class="card section-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Enhanced Jewelry Calculator</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="enhancedCalculatorForm">
                            <input type="hidden" name="action" value="calculate_jewelry">
                            
                            <!-- Customer & Product Info -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Customer Name</label>
                                    <input type="text" class="form-control" name="customer_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Product Name</label>
                                    <input type="text" class="form-control" name="product_name" required>
                                </div>
                            </div>
                            
                            <!-- Metal Type & Purity -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">Metal Type</label>
                                    <select class="form-select" id="metal_type" name="metal_type" required>
                                        <option value="">Select Metal</option>
                                        <option value="Gold">Gold</option>
                                        <option value="Silver">Silver</option>
                                        <option value="Platinum">Platinum</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Purity/Tunch</label>
                                    <input type="text" class="form-control" id="purity" name="purity" 
                                           placeholder="916, 22K, 91.6, 75, etc." required>
                                    <div class="form-text">Enter standard code, percentage, or custom value</div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Rate per Gram (₹)</label>
                                    <input type="number" class="form-control" id="rate_per_gram" name="rate_per_gram" 
                                           step="0.01" required>
                                </div>
                            </div>
                            
                            <!-- Purity Information Display -->
                            <div id="purityInfo" class="purity-info" style="display: none;">
                                <h6><i class="fas fa-info-circle me-2"></i>Purity Information</h6>
                                <div id="purityDetails"></div>
                            </div>
                            
                            <!-- Weight Information -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">Gross Weight (g)</label>
                                    <input type="number" class="form-control" id="gross_weight" name="gross_weight" 
                                           step="0.001" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Stone Weight (g)</label>
                                    <input type="number" class="form-control" id="stone_weight" name="stone_weight" 
                                           step="0.001" value="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Net Weight (Auto)</label>
                                    <input type="number" class="form-control" id="net_weight" readonly>
                                </div>
                            </div>
                            
                            <!-- Charges & Additional Costs -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">Wastage %</label>
                                    <input type="number" class="form-control" id="wastage_percentage" name="wastage_percentage" 
                                           step="0.1" value="0" min="0" max="100">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Making Charges (₹/g)</label>
                                    <input type="number" class="form-control" id="making_charges_rate" name="making_charges_rate" 
                                           step="0.01" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Stone Amount (₹)</label>
                                    <input type="number" class="form-control" id="stone_amount" name="stone_amount" 
                                           step="0.01" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Fine Weight (Auto)</label>
                                    <input type="number" class="form-control" id="fine_weight" readonly>
                                </div>
                            </div>

                            <!-- GST Option -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include_gst" name="include_gst" value="1">
                                        <label class="form-check-label" for="include_gst">
                                            <strong>Add GST (3%)</strong> - Check only if rates are GST exclusive
                                        </label>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>Note:</strong> In jewelry business, rates typically include GST. Only check this if your rates are GST exclusive.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Real-time Calculation Preview -->
                            <div class="calculation-preview">
                                <h6><i class="fas fa-calculator me-2"></i>Live Calculation Preview</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <small><strong>Metal Value:</strong><br>
                                        <span id="metalValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Wastage Value:</strong><br>
                                        <span id="wastageValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Making Charges:</strong><br>
                                        <span id="makingChargesDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Total Amount:</strong><br>
                                        <span id="totalAmountDisplay">₹0.00</span></small>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-3">
                                        <small><strong>CGST (1.5%):</strong><br>
                                        <span id="cgstDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>SGST (1.5%):</strong><br>
                                        <span id="sgstDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Total GST:</strong><br>
                                        <span id="totalGstDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-3">
                                        <small><strong>Grand Total:</strong><br>
                                        <span id="grandTotalDisplay" style="font-weight: bold; color: #28a745;">₹0.00</span></small>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calculator me-1"></i>Calculate & Save
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Purity Suggestions Panel -->
            <div class="col-md-4">
                <div class="card section-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Purity Quick Select</h6>
                    </div>
                    <div class="card-body">
                        <div id="puritySuggestions">
                            <p class="text-muted">Select a metal type to see purity options</p>
                        </div>
                    </div>
                </div>
                
                <!-- Last Calculation Display -->
                <?php if (isset($_SESSION['last_calculation'])): ?>
                <div class="card section-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Last Calculation</h6>
                    </div>
                    <div class="card-body">
                        <?php 
                        $calc = $_SESSION['last_calculation'];
                        $item = $_SESSION['last_item'];
                        ?>
                        <table class="table table-sm">
                            <tr><td><strong>Purity Used:</strong></td><td><?php echo EnhancedJewelryCalculator::formatPercentage($calc['purityUsed']); ?></td></tr>
                            <tr><td><strong>Tunch Value:</strong></td><td><?php echo EnhancedJewelryCalculator::formatTunch($calc['tunchValue']); ?></td></tr>
                            <tr><td><strong>Fine Weight:</strong></td><td><?php echo EnhancedJewelryCalculator::formatWeight($calc['fineWeight']); ?></td></tr>
                            <tr><td><strong>Metal Value:</strong></td><td><?php echo EnhancedJewelryCalculator::formatCurrency($calc['metalValue']); ?></td></tr>
                            <tr><td><strong>Grand Total:</strong></td><td><strong><?php echo EnhancedJewelryCalculator::formatCurrency($calc['grandTotal']); ?></strong></td></tr>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Calculations Table -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card section-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Calculations</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Product</th>
                                        <th>Metal</th>
                                        <th>Purity Input</th>
                                        <th>Purity %</th>
                                        <th>Gross Wt</th>
                                        <th>Fine Wt</th>
                                        <th>Grand Total</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentCalculations as $calc): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($calc['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($calc['product_name']); ?></td>
                                        <td><?php echo $calc['metal_type']; ?></td>
                                        <td><?php echo htmlspecialchars($calc['purity_input']); ?></td>
                                        <td><?php echo number_format($calc['purity_percentage'], 2); ?>%</td>
                                        <td><?php echo number_format($calc['gross_weight'], 3); ?>g</td>
                                        <td><?php echo number_format($calc['fine_weight'], 3); ?>g</td>
                                        <td><strong>₹<?php echo number_format($calc['grand_total'], 2); ?></strong></td>
                                        <td><?php echo date('M j, Y', strtotime($calc['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Purity suggestions data
        const puritySuggestions = {
            'Gold': <?php echo json_encode($goldPurities); ?>,
            'Silver': <?php echo json_encode($silverPurities); ?>,
            'Platinum': <?php echo json_encode($platinumPurities); ?>
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            const metalType = document.getElementById('metal_type');
            const purityInput = document.getElementById('purity');
            const grossWeight = document.getElementById('gross_weight');
            const stoneWeight = document.getElementById('stone_weight');
            const netWeight = document.getElementById('net_weight');
            const fineWeight = document.getElementById('fine_weight');
            const ratePerGram = document.getElementById('rate_per_gram');
            const wastagePercentage = document.getElementById('wastage_percentage');
            const makingChargesRate = document.getElementById('making_charges_rate');
            const stoneAmount = document.getElementById('stone_amount');
            const includeGst = document.getElementById('include_gst');

            // Display elements
            const purityInfo = document.getElementById('purityInfo');
            const purityDetails = document.getElementById('purityDetails');
            const puritySuggestionsDiv = document.getElementById('puritySuggestions');
            const metalValueDisplay = document.getElementById('metalValueDisplay');
            const wastageValueDisplay = document.getElementById('wastageValueDisplay');
            const makingChargesDisplay = document.getElementById('makingChargesDisplay');
            const totalAmountDisplay = document.getElementById('totalAmountDisplay');
            const cgstDisplay = document.getElementById('cgstDisplay');
            const sgstDisplay = document.getElementById('sgstDisplay');
            const totalGstDisplay = document.getElementById('totalGstDisplay');
            const grandTotalDisplay = document.getElementById('grandTotalDisplay');
            
            // Update purity suggestions when metal type changes
            metalType.addEventListener('change', function() {
                updatePuritySuggestions();
                calculatePreview();
            });
            
            function updatePuritySuggestions() {
                const metal = metalType.value;
                if (metal && puritySuggestions[metal]) {
                    let html = '<h6>Standard ' + metal + ' Purities:</h6>';
                    html += '<div class="purity-suggestions">';
                    
                    puritySuggestions[metal].forEach(function(purity) {
                        html += '<div class="purity-option" onclick="selectPurity(\'' + purity.code + '\')">';
                        html += '<strong>' + purity.code + '</strong> - ' + purity.percentage + '% (' + purity.label + ')';
                        html += '</div>';
                    });
                    
                    html += '</div>';
                    html += '<div class="mt-2"><small class="text-muted">Click to select, or enter custom value</small></div>';
                    puritySuggestionsDiv.innerHTML = html;
                } else {
                    puritySuggestionsDiv.innerHTML = '<p class="text-muted">Select a metal type to see purity options</p>';
                }
            }
            
            // Select purity from suggestions
            window.selectPurity = function(purityCode) {
                purityInput.value = purityCode;
                updatePurityInfo();
                calculatePreview();
            };
            
            // Update purity information display
            function updatePurityInfo() {
                const purity = purityInput.value.trim();
                const metal = metalType.value;
                
                if (purity && metal) {
                    // This would normally call the PHP parser, but for demo we'll simulate
                    let purityPercentage = parseFloat(purity);
                    let tunchValue = Math.round(purityPercentage * 10);
                    let label = purity + '% (Custom)';
                    let format = 'custom';
                    
                    // Check if it's a standard purity
                    if (puritySuggestions[metal]) {
                        const standardPurity = puritySuggestions[metal].find(p => p.code === purity);
                        if (standardPurity) {
                            purityPercentage = standardPurity.percentage;
                            tunchValue = standardPurity.tunch;
                            label = standardPurity.label;
                            format = 'standard';
                        }
                    }
                    
                    purityDetails.innerHTML = 
                        '<strong>Percentage:</strong> ' + purityPercentage.toFixed(2) + '%<br>' +
                        '<strong>Tunch Value:</strong> ' + tunchValue + '<br>' +
                        '<strong>Label:</strong> ' + label + '<br>' +
                        '<strong>Format:</strong> ' + format;
                    
                    purityInfo.style.display = 'block';
                } else {
                    purityInfo.style.display = 'none';
                }
            }
            
            // Calculate net weight
            function calculateNetWeight() {
                const gross = parseFloat(grossWeight.value) || 0;
                const stone = parseFloat(stoneWeight.value) || 0;
                const net = gross - stone;
                netWeight.value = net.toFixed(3);
                calculatePreview();
            }
            
            // Calculate preview
            function calculatePreview() {
                const gross = parseFloat(grossWeight.value) || 0;
                const stone = parseFloat(stoneWeight.value) || 0;
                const net = gross - stone;
                const purity = purityInput.value.trim();
                const metal = metalType.value;
                const rate = parseFloat(ratePerGram.value) || 0;
                const wastage = parseFloat(wastagePercentage.value) || 0;
                const making = parseFloat(makingChargesRate.value) || 0;
                const stoneAmt = parseFloat(stoneAmount.value) || 0;
                
                if (net <= 0 || !purity || rate <= 0) {
                    // Reset displays
                    fineWeight.value = '0.000';
                    metalValueDisplay.textContent = '₹0.00';
                    wastageValueDisplay.textContent = '₹0.00';
                    makingChargesDisplay.textContent = '₹0.00';
                    totalAmountDisplay.textContent = '₹0.00';
                    cgstDisplay.textContent = '₹0.00';
                    sgstDisplay.textContent = '₹0.00';
                    totalGstDisplay.textContent = '₹0.00';
                    grandTotalDisplay.textContent = '₹0.00';
                    return;
                }
                
                // Get purity percentage
                let purityPercentage = parseFloat(purity);
                if (metal && puritySuggestions[metal]) {
                    const standardPurity = puritySuggestions[metal].find(p => p.code === purity);
                    if (standardPurity) {
                        purityPercentage = standardPurity.percentage;
                    }
                }
                
                // Calculate fine weight
                const fineWt = net * (purityPercentage / 100);
                fineWeight.value = fineWt.toFixed(3);
                
                // Calculate metal value
                const metalValue = fineWt * rate;
                
                // Calculate wastage
                const wastageWeight = net * (wastage / 100);
                const wastageValue = wastageWeight * (purityPercentage / 100) * rate;
                
                // Calculate making charges
                const makingCharges = net * making;

                // Calculate total amount (rates typically include GST)
                const totalAmount = metalValue + wastageValue + makingCharges + stoneAmt;

                // GST calculation (only if checkbox is checked - rates are GST exclusive)
                const shouldIncludeGst = includeGst.checked;
                let cgst = 0;
                let sgst = 0;
                let totalGst = 0;
                let grandTotal = totalAmount;

                if (shouldIncludeGst) {
                    cgst = totalAmount * 0.015; // 1.5%
                    sgst = totalAmount * 0.015; // 1.5%
                    totalGst = cgst + sgst;
                    grandTotal = totalAmount + totalGst;
                }

                // Update displays
                metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                wastageValueDisplay.textContent = '₹' + wastageValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                makingChargesDisplay.textContent = '₹' + makingCharges.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                totalAmountDisplay.textContent = '₹' + totalAmount.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                cgstDisplay.textContent = '₹' + cgst.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                sgstDisplay.textContent = '₹' + sgst.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                totalGstDisplay.textContent = '₹' + totalGst.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                grandTotalDisplay.textContent = '₹' + grandTotal.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            }
            
            // Event listeners
            grossWeight.addEventListener('input', calculateNetWeight);
            stoneWeight.addEventListener('input', calculateNetWeight);
            purityInput.addEventListener('input', function() {
                updatePurityInfo();
                calculatePreview();
            });
            ratePerGram.addEventListener('input', calculatePreview);
            wastagePercentage.addEventListener('input', calculatePreview);
            makingChargesRate.addEventListener('input', calculatePreview);
            stoneAmount.addEventListener('input', calculatePreview);
            includeGst.addEventListener('change', calculatePreview);
            
            // Initial setup
            updatePuritySuggestions();
        });
    </script>
</body>
</html>
