<?php
// Database connection
class Database {
    private $pdo;
    
    public function __construct() {
        try {
            $this->pdo = new PDO('sqlite:jewelry_billing.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createTable();
        } catch(PDOException $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }
    
    private function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS final_billing_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            location TEXT NOT NULL,
            product_name TEXT NOT NULL,
            gross_wt REAL NOT NULL,
            stone_wt REAL NOT NULL,
            net_wt REAL NOT NULL,
            tunch_percentage REAL NOT NULL,
            base_rate REAL NOT NULL,
            tunch_weight REAL NOT NULL,
            metal_value REAL NOT NULL,
            making_charges REAL NOT NULL,
            stone_charges REAL NOT NULL,
            final_value REAL NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->pdo->exec($sql);
    }
    
    public function execute($sql, $params) {
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }
    
    public function query($sql) {
        return $this->pdo->query($sql);
    }
}

$db = new Database();
$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $customer_name = $_POST['customer_name'];
        $location = $_POST['location'];
        $product_name = $_POST['product_name'];
        $gross_wt = floatval($_POST['gross_wt']);
        $stone_wt = floatval($_POST['stone_wt']);
        $net_wt = $gross_wt - $stone_wt;
        $tunch_percentage = floatval($_POST['tunch_percentage']);
        $base_rate = floatval($_POST['base_rate']);
        $making_charges = floatval($_POST['making_charges']);
        $stone_charges = floatval($_POST['stone_charges']);
        
        // Apply your EXACT handwritten formula
        $tunch_weight = $net_wt * ($tunch_percentage / 100); // Tunch Weight = Net Weight × (Tunch Percentage / 100)
        $metal_value = $tunch_weight * $base_rate; // Metal Value = Tunch Weight × Base Rate (DIRECT multiplication like your handwriting)
        $final_value = $metal_value + $making_charges + $stone_charges; // Final Value = Metal Value + Making + Stone Charges
        
        // Store in database
        $sql = "INSERT INTO final_billing_entries (
            customer_name, location, product_name, gross_wt, stone_wt, net_wt,
            tunch_percentage, base_rate, tunch_weight, metal_value, 
            making_charges, stone_charges, final_value, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))";
        
        $params = [
            $_POST['customer_name'],
            $_POST['location'],
            $_POST['product_name'],
            $gross_wt,
            $stone_wt,
            $net_wt,
            $tunch_percentage,
            $base_rate,
            $tunch_weight,
            $metal_value,
            $making_charges,
            $stone_charges,
            $final_value
        ];
        
        $db->execute($sql, $params);
        $success = "✅ Bill created successfully! Final Value: ₹" . number_format($final_value, 2) . " (Matches your handwritten calculation)";
        
    } catch (Exception $e) {
        $error = "❌ Error: " . $e->getMessage();
    }
}

// Get recent entries
$recent_entries = $db->query("SELECT * FROM final_billing_entries ORDER BY created_at DESC LIMIT 10")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FINAL CORRECTED Jewelry Billing System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .formula-display {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        .calculation-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .handwritten-match {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🏆 FINAL CORRECTED Jewelry Billing System</h1>
                <div class="alert alert-success">
                    <h5>✅ CALCULATION FIXED - Now matches your handwritten calculation exactly!</h5>
                    <p><strong>Your handwriting:</strong> 10.120g × 96% = 9.7159g × ₹10,112 = ₹98,240.10</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 Formula Used</h5>
                    </div>
                    <div class="card-body">
                        <div class="formula-display">
                            <strong>Your EXACT Handwritten Formula:</strong><br>
                            1. <strong>Net Weight</strong> = Gross Weight - Stone Weight<br>
                            2. <strong>Tunch Weight</strong> = Net Weight × (Tunch Percentage ÷ 100)<br>
                            3. <strong>Metal Value</strong> = Tunch Weight × Base Rate (DIRECT multiplication)<br>
                            4. <strong>Final Value</strong> = Metal Value + Making + Stone Charges<br>
                            <br>
                            <strong style="color: #28a745;">✅ Matches your handwritten calculation: 9.7159g × ₹10,112 = ₹98,240.10</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>💎 Create New Bill</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Customer Name</label>
                                        <input type="text" class="form-control" name="customer_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Location</label>
                                        <input type="text" class="form-control" name="location" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Product Name</label>
                                <input type="text" class="form-control" name="product_name" required>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Gross Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="gross_wt" id="gross_wt" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Stone Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="stone_wt" id="stone_wt" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Net Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="net_wt" id="net_wt" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Tunch Percentage (%)</label>
                                        <input type="number" step="0.01" class="form-control" name="tunch_percentage" id="tunch_percentage" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Base Rate (₹ per 10g)</label>
                                        <input type="number" step="0.01" class="form-control" name="base_rate" id="base_rate" required>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-preview">
                                <div class="row">
                                    <div class="col-md-4">
                                        <small><strong>Tunch Weight:</strong><br>
                                        <span id="tunchWeightDisplay">0.000g</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>Metal Value:</strong><br>
                                        <span id="metalValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>Preview Total:</strong><br>
                                        <span id="finalValueDisplay">₹0.00</span></small>
                                    </div>
                                </div>
                                <div class="mt-2" id="comparisonDisplay">
                                    <p class="text-muted">Enter values to see calculation</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Making Charges (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="making_charges" id="making_charges" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Stone Charges (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="stone_charges" id="stone_charges" value="0">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-success btn-lg w-100">💎 Create Bill (Correct Formula)</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($recent_entries)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Recent Bills (Correct Calculation)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Product</th>
                                        <th>Net Wt (g)</th>
                                        <th>Tunch %</th>
                                        <th>Tunch Wt (g)</th>
                                        <th>Base Rate</th>
                                        <th>Metal Value</th>
                                        <th>Making</th>
                                        <th>Stone</th>
                                        <th>Final Value</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_entries as $entry): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($entry['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($entry['product_name']); ?></td>
                                        <td><?php echo number_format($entry['net_wt'], 3); ?>g</td>
                                        <td><?php echo $entry['tunch_percentage']; ?>%</td>
                                        <td><?php echo number_format($entry['tunch_weight'], 3); ?>g</td>
                                        <td>₹<?php echo number_format($entry['base_rate'], 2); ?></td>
                                        <td>₹<?php echo number_format($entry['metal_value'], 2); ?></td>
                                        <td>₹<?php echo number_format($entry['making_charges'], 2); ?></td>
                                        <td>₹<?php echo number_format($entry['stone_charges'], 2); ?></td>
                                        <td><strong>₹<?php echo number_format($entry['final_value'], 2); ?></strong></td>
                                        <td><?php echo date('M j, Y', strtotime($entry['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form elements
            const grossWt = document.getElementById('gross_wt');
            const stoneWt = document.getElementById('stone_wt');
            const netWt = document.getElementById('net_wt');
            const tunchPercentage = document.getElementById('tunch_percentage');
            const baseRate = document.getElementById('base_rate');
            const makingCharges = document.getElementById('making_charges');
            const stoneCharges = document.getElementById('stone_charges');

            // Display elements
            const tunchWeightDisplay = document.getElementById('tunchWeightDisplay');
            const metalValueDisplay = document.getElementById('metalValueDisplay');
            const finalValueDisplay = document.getElementById('finalValueDisplay');
            const comparisonDisplay = document.getElementById('comparisonDisplay');

            function calculateNetWeight() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                netWt.value = net.toFixed(3);
                calculateAll();
            }

            function calculateAll() {
                const net = parseFloat(netWt.value) || 0;
                const tunchPct = parseFloat(tunchPercentage.value) || 0;
                const rate = parseFloat(baseRate.value) || 0;
                const making = parseFloat(makingCharges.value) || 0;
                const stoneCharge = parseFloat(stoneCharges.value) || 0;

                if (net <= 0 || tunchPct <= 0 || rate <= 0) {
                    tunchWeightDisplay.textContent = '0.000g';
                    metalValueDisplay.textContent = '₹0.00';
                    finalValueDisplay.textContent = '₹0.00';
                    comparisonDisplay.innerHTML = '<p class="text-muted">Enter values to see calculation</p>';
                    return;
                }

                // Apply your EXACT handwritten formula
                // 1. Net Weight = Gross Weight - Stone Weight (already calculated)
                // 2. Tunch Weight = Net Weight × (Tunch Percentage / 100)
                const tunchWeight = net * (tunchPct / 100);

                // 3. Metal Value = Tunch Weight × Base Rate (DIRECT multiplication like your handwriting)
                const metalValue = tunchWeight * rate;

                // 4. Final Value = Metal Value + Making + Stone Charges (matches your handwriting)
                const finalValue = metalValue + making + stoneCharge;

                // Update display
                tunchWeightDisplay.textContent = tunchWeight.toFixed(3) + 'g';
                metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                finalValueDisplay.textContent = '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});

                // Show handwritten match
                comparisonDisplay.innerHTML = `
                    <div class="handwritten-match">
                        <strong>✅ Matches your handwritten calculation:</strong><br>
                        ${net.toFixed(3)}g × ${tunchPct}% = ${tunchWeight.toFixed(3)}g × ₹${rate.toLocaleString('en-IN')} = ₹${metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2})}
                    </div>
                `;
            }

            // Event listeners
            grossWt.addEventListener('input', calculateNetWeight);
            stoneWt.addEventListener('input', calculateNetWeight);
            tunchPercentage.addEventListener('input', calculateAll);
            baseRate.addEventListener('input', calculateAll);
            makingCharges.addEventListener('input', calculateAll);
            stoneCharges.addEventListener('input', calculateAll);
        });
    </script>
</body>
</html>
