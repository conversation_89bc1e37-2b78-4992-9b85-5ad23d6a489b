<?php
/**
 * Final Fix for Duplicate Product Codes Issue
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🔧 Final Fix for Product Code Duplication</h2>";
    
    // Step 1: Remove existing unique constraint if it exists
    echo "<h3>🗄️ Step 1: Removing Existing Constraints</h3>";
    
    try {
        // Get all constraints on products table
        $constraints = $db->fetchAll("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'products' 
            AND CONSTRAINT_TYPE = 'UNIQUE'
            AND CONSTRAINT_NAME LIKE '%product_code%'
        ");
        
        foreach ($constraints as $constraint) {
            try {
                $db->execute("ALTER TABLE products DROP INDEX " . $constraint['CONSTRAINT_NAME']);
                echo "✅ Removed constraint: {$constraint['CONSTRAINT_NAME']}<br>";
            } catch (Exception $e) {
                echo "⚠️ Could not remove constraint {$constraint['CONSTRAINT_NAME']}: " . $e->getMessage() . "<br>";
            }
        }
        
        // Also try to remove common constraint names
        $commonNames = ['unique_product_code', 'product_code', 'products_product_code_unique'];
        foreach ($commonNames as $name) {
            try {
                $db->execute("ALTER TABLE products DROP INDEX $name");
                echo "✅ Removed constraint: $name<br>";
            } catch (Exception $e) {
                // Ignore if constraint doesn't exist
            }
        }
        
    } catch (Exception $e) {
        echo "⚠️ Error checking constraints: " . $e->getMessage() . "<br>";
    }
    
    // Step 2: Get all products and assign unique codes
    echo "<h3>📋 Step 2: Reassigning All Product Codes</h3>";
    
    $products = $db->fetchAll("SELECT id, product_name, metal_type FROM products ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>Product Name</th><th style='padding: 8px;'>Old Code</th><th style='padding: 8px;'>New Code</th><th style='padding: 8px;'>Status</th></tr>";
    
    $updated = 0;
    $errors = 0;
    
    foreach ($products as $product) {
        // Get current code
        $currentCode = $db->fetch("SELECT product_code FROM products WHERE id = ?", [$product['id']])['product_code'];
        
        // Generate new unique code
        $newCode = generateUniqueProductCode($product, $db);
        
        try {
            $db->execute("UPDATE products SET product_code = ? WHERE id = ?", [$newCode, $product['id']]);
            
            echo "<tr style='background-color: #d4edda;'>";
            echo "<td style='padding: 8px;'>{$product['id']}</td>";
            echo "<td style='padding: 8px;'>{$product['product_name']}</td>";
            echo "<td style='padding: 8px;'>$currentCode</td>";
            echo "<td style='padding: 8px;'><strong>$newCode</strong></td>";
            echo "<td style='padding: 8px;'>✅ Updated</td>";
            echo "</tr>";
            
            $updated++;
            
        } catch (Exception $e) {
            echo "<tr style='background-color: #f8d7da;'>";
            echo "<td style='padding: 8px;'>{$product['id']}</td>";
            echo "<td style='padding: 8px;'>{$product['product_name']}</td>";
            echo "<td style='padding: 8px;'>$currentCode</td>";
            echo "<td style='padding: 8px;'>-</td>";
            echo "<td style='padding: 8px;'>❌ Error: " . $e->getMessage() . "</td>";
            echo "</tr>";
            
            $errors++;
        }
    }
    
    echo "</table>";
    
    // Step 3: Verify no duplicates exist
    echo "<h3>🔍 Step 3: Verifying No Duplicates</h3>";
    
    $duplicateCheck = $db->fetchAll("
        SELECT product_code, COUNT(*) as count 
        FROM products 
        GROUP BY product_code 
        HAVING COUNT(*) > 1
    ");
    
    if (count($duplicateCheck) > 0) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Duplicates Still Found</h4>";
        foreach ($duplicateCheck as $dup) {
            echo "<p>Code '{$dup['product_code']}' appears {$dup['count']} times</p>";
        }
        echo "</div>";
        
        // Fix remaining duplicates
        foreach ($duplicateCheck as $dup) {
            $duplicateProducts = $db->fetchAll("SELECT id, product_name FROM products WHERE product_code = ?", [$dup['product_code']]);
            
            foreach ($duplicateProducts as $index => $dupProduct) {
                if ($index > 0) { // Keep first one, change others
                    $newCode = $dup['product_code'] . '_' . $dupProduct['id'];
                    $db->execute("UPDATE products SET product_code = ? WHERE id = ?", [$newCode, $dupProduct['id']]);
                    echo "✅ Fixed duplicate: Product ID {$dupProduct['id']} → $newCode<br>";
                }
            }
        }
        
    } else {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ No Duplicates Found</h4>";
        echo "<p>All product codes are now unique.</p>";
        echo "</div>";
    }
    
    // Step 4: Add unique constraint back
    echo "<h3>🔒 Step 4: Adding Unique Constraint</h3>";
    
    try {
        $db->execute("ALTER TABLE products ADD UNIQUE KEY unique_product_code (product_code)");
        echo "✅ Added unique constraint to product_code column<br>";
    } catch (Exception $e) {
        echo "❌ Error adding unique constraint: " . $e->getMessage() . "<br>";
        
        // Try alternative approach
        try {
            $db->execute("CREATE UNIQUE INDEX unique_product_code ON products (product_code)");
            echo "✅ Added unique index to product_code column<br>";
        } catch (Exception $e2) {
            echo "❌ Error adding unique index: " . $e2->getMessage() . "<br>";
        }
    }
    
    // Step 5: Final verification
    echo "<h3>✅ Step 5: Final Verification</h3>";
    
    $finalProducts = $db->fetchAll("SELECT id, product_name, product_code FROM products ORDER BY product_code");
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Final Product Codes</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>Product Code</th><th style='padding: 8px;'>Product Name</th></tr>";
    
    foreach ($finalProducts as $product) {
        echo "<tr>";
        echo "<td style='padding: 8px;'><strong>{$product['product_code']}</strong></td>";
        echo "<td style='padding: 8px;'>{$product['product_name']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Summary
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Product Code Fix Complete!</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Products Updated:</strong> $updated</li>";
    echo "<li>✅ <strong>Errors:</strong> $errors</li>";
    echo "<li>✅ <strong>Duplicates Removed:</strong> All product codes are now unique</li>";
    echo "<li>✅ <strong>Constraint Added:</strong> Database will prevent future duplicates</li>";
    echo "<li>✅ <strong>System Ready:</strong> Billing and inventory systems can now work properly</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='enhanced-billing.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Enhanced Billing</a>";
    echo "<a href='inventory.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>View Inventory</a>";
    echo "<a href='test-tunch-calculator.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Calculator</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Error</h4>";
    echo "<p>Error fixing product codes: " . $e->getMessage() . "</p>";
    echo "</div>";
}

/**
 * Generate unique product code
 */
function generateUniqueProductCode($product, $db) {
    // Determine prefix based on product name and metal type
    $name = strtolower($product['product_name']);
    $metal = strtolower($product['metal_type'] ?? '');
    
    $prefix = 'JW'; // Default: Jewellery Wholesale
    
    // Gold products
    if (strpos($metal, 'gold') !== false || strpos($name, 'gold') !== false) {
        if (strpos($name, 'ring') !== false) $prefix = 'GR';
        elseif (strpos($name, 'necklace') !== false || strpos($name, 'chain') !== false) $prefix = 'GN';
        elseif (strpos($name, 'earring') !== false) $prefix = 'GE';
        elseif (strpos($name, 'bracelet') !== false) $prefix = 'GB';
        elseif (strpos($name, 'pendant') !== false) $prefix = 'GP';
        else $prefix = 'GJ';
    }
    // Silver products
    elseif (strpos($metal, 'silver') !== false || strpos($name, 'silver') !== false) {
        if (strpos($name, 'ring') !== false) $prefix = 'SR';
        elseif (strpos($name, 'necklace') !== false || strpos($name, 'chain') !== false) $prefix = 'SN';
        elseif (strpos($name, 'earring') !== false) $prefix = 'SE';
        elseif (strpos($name, 'bracelet') !== false) $prefix = 'SB';
        else $prefix = 'SJ';
    }
    
    // Generate unique code
    $sequence = 1;
    do {
        $code = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);
        
        // Check if code exists
        $exists = $db->fetch("SELECT id FROM products WHERE product_code = ? AND id != ?", [$code, $product['id']]);
        
        if (!$exists) {
            return $code;
        }
        
        $sequence++;
        
        // Fallback: use product ID if we can't find unique code
        if ($sequence > 999) {
            return $prefix . '_' . $product['id'];
        }
        
    } while (true);
}
?>
