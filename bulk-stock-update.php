<?php
/**
 * Bulk Stock Update Handler - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: inventory.php');
    exit;
}

$db = getDB();

try {
    $updateType = $_POST['update_type'] ?? '';
    $quantity = intval($_POST['quantity'] ?? 0);
    $notes = sanitizeInput($_POST['notes'] ?? '');
    $productIds = $_POST['products'] ?? [];

    if (empty($updateType) || $quantity <= 0 || empty($productIds)) {
        throw new Exception('Invalid input data');
    }

    $db->beginTransaction();

    $updatedCount = 0;
    $errors = [];

    foreach ($productIds as $productId) {
        try {
            // Get current stock
            $currentStock = $db->fetch("
                SELECT i.quantity_in_stock, p.product_name 
                FROM inventory i 
                JOIN products p ON i.product_id = p.id 
                WHERE i.product_id = ?
            ", [$productId]);

            if (!$currentStock) {
                $errors[] = "Product ID $productId not found";
                continue;
            }

            $newQuantity = $currentStock['quantity_in_stock'];
            $movementType = '';

            switch ($updateType) {
                case 'add':
                    $newQuantity += $quantity;
                    $movementType = 'in';
                    break;
                    
                case 'subtract':
                    if ($quantity > $currentStock['quantity_in_stock']) {
                        $errors[] = "Cannot subtract $quantity from {$currentStock['product_name']} (current: {$currentStock['quantity_in_stock']})";
                        continue 2;
                    }
                    $newQuantity -= $quantity;
                    $movementType = 'out';
                    break;
                    
                case 'set':
                    $newQuantity = $quantity;
                    $movementType = $quantity > $currentStock['quantity_in_stock'] ? 'in' : 'out';
                    $actualQuantity = abs($quantity - $currentStock['quantity_in_stock']);
                    break;
                    
                default:
                    $errors[] = "Invalid update type: $updateType";
                    continue 2;
            }

            // Update inventory
            $db->query("UPDATE inventory SET quantity_in_stock = ? WHERE product_id = ?", [$newQuantity, $productId]);

            // Record stock movement
            $movementQuantity = $updateType === 'set' ? $actualQuantity : $quantity;
            $db->query("
                INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, notes, created_by) 
                VALUES (?, ?, ?, 'bulk_update', ?, 1)
            ", [$productId, $movementType, $movementQuantity, $notes]);

            $updatedCount++;

        } catch (Exception $e) {
            $errors[] = "Error updating {$currentStock['product_name']}: " . $e->getMessage();
        }
    }

    $db->commit();

    // Prepare success/error messages
    $message = "Successfully updated $updatedCount products.";
    if (!empty($errors)) {
        $message .= " Errors: " . implode(', ', $errors);
    }

    // Redirect with message
    $_SESSION['bulk_update_message'] = $message;
    $_SESSION['bulk_update_type'] = empty($errors) ? 'success' : 'warning';

} catch (Exception $e) {
    $db->rollback();
    $_SESSION['bulk_update_message'] = "Bulk update failed: " . $e->getMessage();
    $_SESSION['bulk_update_type'] = 'error';
}

header('Location: inventory.php');
exit;
?>
