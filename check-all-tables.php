<?php
/**
 * Check All Required Tables
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>Database Table Status Check</h2>";
    
    // List of expected tables based on the schema and application needs
    $expected_tables = [
        'users' => 'User authentication and management',
        'settings' => 'System configuration settings',
        'metal_rates' => 'Daily metal rate management',
        'suppliers' => 'Supplier information',
        'categories' => 'Product categories',
        'products' => 'Product catalog',
        'inventory' => 'Stock and pricing information',
        'customers' => 'Customer database',
        'sales' => 'Sales transactions',
        'sale_items' => 'Individual sale line items',
        'stock_movements' => 'Inventory movement tracking',
        'notifications' => 'System notifications',
        'audit_logs' => 'System audit trail',
        'backups' => 'Backup history tracking'
    ];
    
    // Get all existing tables
    $existing_tables = $db->fetchAll("SHOW TABLES");
    $existing_table_names = [];
    foreach ($existing_tables as $table) {
        $existing_table_names[] = array_values($table)[0];
    }
    
    echo "<h3>Table Status</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>Table Name</th><th>Status</th><th>Record Count</th><th>Description</th></tr>";
    
    $missing_tables = [];
    
    foreach ($expected_tables as $table_name => $description) {
        echo "<tr>";
        echo "<td><strong>$table_name</strong></td>";
        
        if (in_array($table_name, $existing_table_names)) {
            try {
                $count = $db->fetch("SELECT COUNT(*) as count FROM $table_name");
                echo "<td style='color: green;'>✅ EXISTS</td>";
                echo "<td>{$count['count']} records</td>";
            } catch (Exception $e) {
                echo "<td style='color: orange;'>⚠️ EXISTS (Error counting)</td>";
                echo "<td>Error: " . $e->getMessage() . "</td>";
            }
        } else {
            echo "<td style='color: red;'>❌ MISSING</td>";
            echo "<td>-</td>";
            $missing_tables[] = $table_name;
        }
        
        echo "<td>$description</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if (count($missing_tables) > 0) {
        echo "<br><div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #856404;'>⚠️ Missing Tables Found</h4>";
        echo "<p>The following tables are missing and may cause errors:</p>";
        echo "<ul>";
        foreach ($missing_tables as $table) {
            echo "<li><strong>$table</strong> - {$expected_tables[$table]}</li>";
        }
        echo "</ul>";
        echo "<p><strong>Recommendation:</strong> Run the database schema setup again or create these tables manually.</p>";
        echo "</div>";
    } else {
        echo "<br><div style='background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ All Tables Present</h4>";
        echo "<p>All expected database tables are present and accessible.</p>";
        echo "</div>";
    }
    
    // Show additional database info
    echo "<br><h3>Database Information</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    
    $db_info = [
        'Database Name' => DB_NAME,
        'Total Tables' => count($existing_table_names),
        'Expected Tables' => count($expected_tables),
        'Missing Tables' => count($missing_tables)
    ];
    
    foreach ($db_info as $key => $value) {
        echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    
    // Show all existing tables (including any extra ones)
    echo "<br><h3>All Existing Tables</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo "<p><strong>Tables found in database:</strong></p>";
    echo "<ul>";
    foreach ($existing_table_names as $table) {
        $status = isset($expected_tables[$table]) ? '✅' : '❓';
        echo "<li>$status $table</li>";
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error checking database: " . $e->getMessage();
}
?>
