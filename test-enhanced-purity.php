<?php
/**
 * Test Enhanced Purity System - Demonstrate Advanced Purity Handling
 * Based on the TypeScript reference implementation
 */

require_once 'lib/PurityParser.php';
require_once 'lib/EnhancedJewelryCalculator.php';

echo "<h2>🧮 Enhanced Purity System Testing</h2>";
echo "<p>Testing the advanced purity parsing system based on the TypeScript reference implementation.</p>";

// Test 1: Standard Purity Codes
echo "<h3>📋 Test 1: Standard Purity Codes</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$standardTests = [
    ['Gold', '916', '22K Gold Standard'],
    ['Gold', '22K', '22K Gold Alternative'],
    ['Gold', '750', '18K Gold Standard'],
    ['Gold', '18K', '18K Gold Alternative'],
    ['Silver', '925', 'Sterling Silver'],
    ['Silver', '999', 'Fine Silver'],
    ['Platinum', '950', 'Platinum Standard']
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #e9ecef;'><th style='padding: 8px;'>Metal</th><th style='padding: 8px;'>Input</th><th style='padding: 8px;'>Description</th><th style='padding: 8px;'>Percentage</th><th style='padding: 8px;'>Tunch</th><th style='padding: 8px;'>Label</th><th style='padding: 8px;'>Format</th></tr>";

foreach ($standardTests as $test) {
    try {
        $result = PurityParser::parsePurity($test[1], $test[0]);
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$test[0]}</td>";
        echo "<td style='padding: 8px;'><strong>{$test[1]}</strong></td>";
        echo "<td style='padding: 8px;'>{$test[2]}</td>";
        echo "<td style='padding: 8px;'>{$result['percentage']}%</td>";
        echo "<td style='padding: 8px;'>{$result['tunch']}</td>";
        echo "<td style='padding: 8px;'>{$result['label']}</td>";
        echo "<td style='padding: 8px;'><span style='color: green;'>{$result['format']}</span></td>";
        echo "</tr>";
    } catch (Exception $e) {
        echo "<tr><td colspan='7' style='padding: 8px; color: red;'>Error: {$e->getMessage()}</td></tr>";
    }
}

echo "</table>";
echo "</div>";

// Test 2: Custom Percentage Values
echo "<h3>🎯 Test 2: Custom Percentage Values (Any Number = Direct Percentage)</h3>";
echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$customTests = [
    ['Gold', 75, 'Custom 75% Gold'],
    ['Gold', 91.6, 'Custom 91.6% Gold'],
    ['Gold', 102, 'Above 100% (Extreme Case)'],
    ['Silver', 85.5, 'Custom 85.5% Silver'],
    ['Gold', 65, 'Low Purity Gold'],
    ['Platinum', 88.5, 'Custom Platinum'],
    ['Gold', 120, 'Extreme High Value'],
    ['Silver', 50.25, 'Decimal Precision']
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #e9ecef;'><th style='padding: 8px;'>Metal</th><th style='padding: 8px;'>Input</th><th style='padding: 8px;'>Description</th><th style='padding: 8px;'>Percentage</th><th style='padding: 8px;'>Tunch</th><th style='padding: 8px;'>Label</th><th style='padding: 8px;'>Format</th></tr>";

foreach ($customTests as $test) {
    try {
        $result = PurityParser::parsePurity($test[1], $test[0]);
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$test[0]}</td>";
        echo "<td style='padding: 8px;'><strong>{$test[1]}</strong></td>";
        echo "<td style='padding: 8px;'>{$test[2]}</td>";
        echo "<td style='padding: 8px;'>{$result['percentage']}%</td>";
        echo "<td style='padding: 8px;'>{$result['tunch']}</td>";
        echo "<td style='padding: 8px;'>{$result['label']}</td>";
        echo "<td style='padding: 8px;'><span style='color: blue;'>{$result['format']}</span></td>";
        echo "</tr>";
    } catch (Exception $e) {
        echo "<tr><td colspan='7' style='padding: 8px; color: red;'>Error: {$e->getMessage()}</td></tr>";
    }
}

echo "</table>";
echo "</div>";

// Test 3: Complete Jewelry Calculations
echo "<h3>💎 Test 3: Complete Jewelry Calculations with Variable Purity</h3>";

$jewelryTests = [
    [
        'description' => 'Custom 75% Purity Gold Ring',
        'item' => [
            'grossWeight' => 16.210,
            'stoneWeight' => 0.345,
            'purity' => 75, // Direct percentage
            'metalType' => 'Gold',
            'ratePerGram' => 6500,
            'wastagePercentage' => 1,
            'makingChargesRate' => 150,
            'stoneAmount' => 2500
        ]
    ],
    [
        'description' => 'Standard 916 Gold Chain',
        'item' => [
            'grossWeight' => 20.0,
            'stoneWeight' => 0.0,
            'purity' => '916', // Standard code
            'metalType' => 'Gold',
            'ratePerGram' => 6700,
            'wastagePercentage' => 2,
            'makingChargesRate' => 200,
            'stoneAmount' => 0
        ]
    ],
    [
        'description' => 'Custom 102% Purity (Above 100%)',
        'item' => [
            'grossWeight' => 15.0,
            'stoneWeight' => 0.0,
            'purity' => 102, // Above 100%
            'metalType' => 'Gold',
            'ratePerGram' => 7000,
            'wastagePercentage' => 1.5,
            'makingChargesRate' => 250,
            'stoneAmount' => 1000
        ]
    ],
    [
        'description' => 'Sterling Silver Bracelet',
        'item' => [
            'grossWeight' => 25.5,
            'stoneWeight' => 2.0,
            'purity' => '925', // Standard silver
            'metalType' => 'Silver',
            'ratePerGram' => 85,
            'wastagePercentage' => 0.5,
            'makingChargesRate' => 25,
            'stoneAmount' => 500
        ]
    ]
];

foreach ($jewelryTests as $index => $test) {
    echo "<h4>💍 Test Case " . ($index + 1) . ": {$test['description']}</h4>";
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    try {
        $calculation = EnhancedJewelryCalculator::calculateJewelryItem($test['item']);
        
        echo "<div style='display: flex; gap: 20px;'>";
        
        // Input Details
        echo "<div style='flex: 1;'>";
        echo "<h6>📝 Input Details</h6>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><td style='padding: 4px;'><strong>Gross Weight:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatWeight($test['item']['grossWeight']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Stone Weight:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatWeight($test['item']['stoneWeight'] ?? 0) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Purity Input:</strong></td><td style='padding: 4px;'><strong>{$test['item']['purity']}</strong></td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Metal Type:</strong></td><td style='padding: 4px;'>{$test['item']['metalType']}</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Rate per Gram:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($test['item']['ratePerGram']) . "</td></tr>";
        echo "</table>";
        echo "</div>";
        
        // Purity Analysis
        echo "<div style='flex: 1;'>";
        echo "<h6>🔍 Purity Analysis</h6>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><td style='padding: 4px;'><strong>Purity Used:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatPercentage($calculation['purityUsed']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Tunch Value:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatTunch($calculation['tunchValue']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Purity Label:</strong></td><td style='padding: 4px;'>{$calculation['purityInfo']['label']}</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Format:</strong></td><td style='padding: 4px;'>{$calculation['purityInfo']['format']}</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Net Weight:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatWeight($calculation['netWeight']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Fine Weight:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatWeight($calculation['fineWeight']) . "</td></tr>";
        echo "</table>";
        echo "</div>";
        
        // Calculation Results
        echo "<div style='flex: 1;'>";
        echo "<h6>💰 Calculation Results</h6>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><td style='padding: 4px;'><strong>Metal Value:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['metalValue']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Wastage Value:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['wastageValue']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Making Charges:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['makingCharges']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Stone Amount:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['stoneAmount']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Total Before Tax:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['totalBeforeTax']) . "</td></tr>";
        echo "<tr><td style='padding: 4px;'><strong>Total GST:</strong></td><td style='padding: 4px;'>" . EnhancedJewelryCalculator::formatCurrency($calculation['totalGst']) . "</td></tr>";
        echo "<tr style='background-color: #d4edda;'><td style='padding: 4px;'><strong>GRAND TOTAL:</strong></td><td style='padding: 4px;'><strong>" . EnhancedJewelryCalculator::formatCurrency($calculation['grandTotal']) . "</strong></td></tr>";
        echo "</table>";
        echo "</div>";
        
        echo "</div>";
        
        // Formula Breakdown
        echo "<div style='margin-top: 15px; padding: 10px; background-color: #f1f3f4; border-radius: 4px;'>";
        echo "<h6>📐 Formula Breakdown</h6>";
        echo "<code style='font-size: 11px;'>";
        echo "Fine Weight = {$calculation['netWeight']}g × ({$calculation['purityUsed']}% ÷ 100) = {$calculation['fineWeight']}g<br>";
        echo "Metal Value = {$calculation['fineWeight']}g × ₹{$test['item']['ratePerGram']} = ₹{$calculation['metalValue']}<br>";
        echo "Grand Total = Metal Value + Wastage + Making + Stone + GST = ₹{$calculation['grandTotal']}";
        echo "</code>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>Error:</strong> {$e->getMessage()}</p>";
    }
    
    echo "</div>";
}

// Test 4: Purity Utility Functions
echo "<h3>🛠️ Test 4: Purity Utility Functions</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

echo "<h5>Available Metal Types:</h5>";
$metalTypes = PurityParser::getSupportedMetalTypes();
echo "<p>" . implode(', ', $metalTypes) . "</p>";

echo "<h5>Gold Purity Suggestions:</h5>";
$goldSuggestions = PurityParser::getPuritySuggestions('Gold');
echo "<ul>";
foreach (array_slice($goldSuggestions, 0, 5) as $suggestion) {
    echo "<li><strong>{$suggestion['code']}</strong> - {$suggestion['percentage']}% ({$suggestion['label']})</li>";
}
echo "</ul>";

echo "<h5>Purity Validation Tests:</h5>";
$validationTests = [
    ['Gold', '916', 'Valid standard code'],
    ['Gold', 91.6, 'Valid percentage'],
    ['Gold', 'invalid', 'Invalid input'],
    ['Silver', 150, 'High percentage (still valid)'],
    ['Gold', -5, 'Negative value (invalid)']
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #e9ecef;'><th style='padding: 8px;'>Metal</th><th style='padding: 8px;'>Input</th><th style='padding: 8px;'>Description</th><th style='padding: 8px;'>Valid</th><th style='padding: 8px;'>Errors</th></tr>";

foreach ($validationTests as $test) {
    $validation = PurityParser::validatePurity($test[1], $test[0]);
    $status = $validation['valid'] ? '✅ Valid' : '❌ Invalid';
    $errors = empty($validation['errors']) ? 'None' : implode(', ', $validation['errors']);
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>{$test[0]}</td>";
    echo "<td style='padding: 8px;'><strong>{$test[1]}</strong></td>";
    echo "<td style='padding: 8px;'>{$test[2]}</td>";
    echo "<td style='padding: 8px;'>$status</td>";
    echo "<td style='padding: 8px;'>$errors</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Summary
echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ Enhanced Purity System Summary</h3>";
echo "<div style='display: flex; gap: 20px;'>";

echo "<div style='flex: 1;'>";
echo "<h5>🎯 Key Features</h5>";
echo "<ul>";
echo "<li>✅ <strong>Flexible Input:</strong> Standard codes, percentages, custom values</li>";
echo "<li>✅ <strong>Multi-Metal Support:</strong> Gold, Silver, Platinum</li>";
echo "<li>✅ <strong>No Restrictions:</strong> Any numeric value accepted</li>";
echo "<li>✅ <strong>Format Detection:</strong> Standard vs custom identification</li>";
echo "<li>✅ <strong>Comprehensive Validation:</strong> Input validation with error messages</li>";
echo "</ul>";
echo "</div>";

echo "<div style='flex: 1;'>";
echo "<h5>🧮 Calculation Features</h5>";
echo "<ul>";
echo "<li>✅ <strong>Fine Weight:</strong> Precise purity-based calculations</li>";
echo "<li>✅ <strong>Wastage Value:</strong> Enhanced wastage calculations</li>";
echo "<li>✅ <strong>GST Breakdown:</strong> CGST/SGST separation</li>";
echo "<li>✅ <strong>Complete Totals:</strong> Comprehensive pricing</li>";
echo "<li>✅ <strong>Real-time Preview:</strong> Live calculation updates</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='enhanced-billing-system.php' style='background-color: #6f42c1; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Try Enhanced Billing System</a>";
echo "<a href='create-billing-tables.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Setup Database</a>";
echo "</div>";
?>
