<?php
/**
 * Tunch Calculator for Indian Jewellery Wholesale Business
 * Implements the standard Indian jewellery calculation formulas
 */

class TunchCalculator {
    private $db;
    
    public function __construct($database = null) {
        $this->db = $database ?: getDB();
    }
    
    /**
     * Calculate Tunch based on the standard formula
     * 
     * @param float $grossWeight Total weight including stones
     * @param float $stoneWeight Weight of stones/gems
     * @param float $tunchPercentage Purity percentage (e.g., 91.6 for 22K)
     * @param float $baseRate Base gold/silver rate per gram
     * @param float $makingCharges Making charges
     * @param float $stoneCharges Stone charges
     * @param float $wastagePercentage Wastage percentage
     * @return array Detailed calculation breakdown
     */
    public function calculateTunch($grossWeight, $stoneWeight, $tunchPercentage, $baseRate, $makingCharges = 0, $stoneCharges = 0, $wastagePercentage = 0) {
        // Step 1: Calculate Net Weight
        $netWeight = $grossWeight - $stoneWeight;
        
        // Step 2: Calculate Tunch Weight
        $tunchWeight = $netWeight * ($tunchPercentage / 100);
        
        // Step 3: Calculate Tunch Rate
        $tunchRate = $baseRate * ($tunchPercentage / 100);
        
        // Step 4: Calculate Metal Value
        $metalValue = $tunchWeight * $tunchRate;
        
        // Step 5: Calculate Wastage Amount
        $wastageAmount = $metalValue * ($wastagePercentage / 100);
        
        // Step 6: Calculate Final Value
        $finalValue = $metalValue + $makingCharges + $stoneCharges + $wastageAmount;
        
        return [
            "gross_weight" => round($grossWeight, 3),
            "stone_weight" => round($stoneWeight, 3),
            "net_weight" => round($netWeight, 3),
            "tunch_percentage" => $tunchPercentage,
            "tunch_weight" => round($tunchWeight, 3),
            "base_rate" => $baseRate,
            "tunch_rate" => round($tunchRate, 2),
            "metal_value" => round($metalValue, 2),
            "making_charges" => $makingCharges,
            "stone_charges" => $stoneCharges,
            "wastage_percentage" => $wastagePercentage,
            "wastage_amount" => round($wastageAmount, 2),
            "final_value" => round($finalValue, 2)
        ];
    }
    
    /**
     * Get current daily rate for metal and purity
     */
    public function getDailyRate($metalType, $purity, $date = null) {
        $date = $date ?: date("Y-m-d");
        
        try {
            $rate = $this->db->fetch("
                SELECT base_rate, tunch_rate 
                FROM daily_rates 
                WHERE metal_type = ? AND purity = ? AND rate_date = ? AND is_active = 1
            ", [$metalType, $purity, $date]);
            
            return $rate ?: null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Calculate product pricing based on current rates
     */
    public function calculateProductPrice($productId) {
        try {
            // Get product details
            $product = $this->db->fetch("
                SELECT p.*, i.* 
                FROM products p 
                LEFT JOIN inventory i ON p.id = i.product_id 
                WHERE p.id = ?
            ", [$productId]);
            
            if (!$product) {
                throw new Exception("Product not found");
            }
            
            // Get current daily rate
            $dailyRate = $this->getDailyRate($product["metal_type"], $product["purity"]);
            if (!$dailyRate) {
                throw new Exception("Daily rate not found for " . $product["metal_type"] . " " . $product["purity"]);
            }
            
            // Calculate using tunch formula
            return $this->calculateTunch(
                $product["gross_weight"],
                $product["stone_weight"],
                $product["tunch_percentage"],
                $dailyRate["base_rate"],
                $product["making_charges_per_gram"] * $product["net_weight"],
                $product["stone_cost"],
                $product["wastage_percentage"]
            );
            
        } catch (Exception $e) {
            throw new Exception("Error calculating product price: " . $e->getMessage());
        }
    }
    
    /**
     * Update inventory prices based on current rates
     */
    public function updateInventoryPrices($productId = null) {
        try {
            $whereClause = $productId ? "WHERE p.id = $productId" : "";
            
            $products = $this->db->fetchAll("
                SELECT p.id, p.metal_type, p.purity, p.gross_weight, p.stone_weight, 
                       p.tunch_percentage, p.making_charges_per_gram, p.wastage_percentage,
                       i.stone_cost_per_piece
                FROM products p 
                LEFT JOIN inventory i ON p.id = i.product_id 
                $whereClause
                AND p.is_active = 1
            ");
            
            $updated = 0;
            foreach ($products as $product) {
                $calculation = $this->calculateProductPrice($product["id"]);
                
                // Update inventory with calculated values
                $this->db->execute("
                    UPDATE inventory SET 
                        base_rate = ?,
                        tunch_rate = ?,
                        total_metal_value = ?,
                        making_cost = ?,
                        wastage_amount = ?,
                        selling_price = ?,
                        updated_at = NOW()
                    WHERE product_id = ?
                ", [
                    $calculation["base_rate"],
                    $calculation["tunch_rate"],
                    $calculation["metal_value"],
                    $calculation["making_charges"],
                    $calculation["wastage_amount"],
                    $calculation["final_value"],
                    $product["id"]
                ]);
                
                $updated++;
            }
            
            return $updated;
            
        } catch (Exception $e) {
            throw new Exception("Error updating inventory prices: " . $e->getMessage());
        }
    }
    
    /**
     * Save calculation to audit trail
     */
    public function saveCalculation($saleItemId, $calculation) {
        try {
            $this->db->execute("
                INSERT INTO tunch_calculations (
                    sale_item_id, gross_weight, stone_weight, net_weight,
                    tunch_percentage, tunch_weight, base_rate, tunch_rate,
                    metal_value, making_charges, stone_charges, wastage_amount,
                    final_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $saleItemId,
                $calculation["gross_weight"],
                $calculation["stone_weight"],
                $calculation["net_weight"],
                $calculation["tunch_percentage"],
                $calculation["tunch_weight"],
                $calculation["base_rate"],
                $calculation["tunch_rate"],
                $calculation["metal_value"],
                $calculation["making_charges"],
                $calculation["stone_charges"],
                $calculation["wastage_amount"],
                $calculation["final_value"]
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (Exception $e) {
            throw new Exception("Error saving calculation: " . $e->getMessage());
        }
    }
    
    /**
     * Get standard tunch percentages for different purities
     */
    public static function getStandardTunchPercentages() {
        return [
            "24K" => 99.9,
            "22K" => 91.6,
            "21K" => 87.5,
            "20K" => 83.3,
            "18K" => 75.0,
            "16K" => 66.7,
            "14K" => 58.3,
            "925" => 92.5,  // Silver
            "999" => 99.9   // Pure Silver
        ];
    }
}
?>