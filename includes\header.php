<?php
/**
 * Top Header Component
 * Indian Jewellery Wholesale Management System v2.0
 */

// Get current page for page title
$current_page = basename($_SERVER['PHP_SELF']);

// Determine if we're in reports directory
$is_in_reports = strpos($_SERVER['SCRIPT_NAME'], '/reports/') !== false;
$base_path = $is_in_reports ? '../' : '';
?>

<!-- Top Header -->
<header class="top-header">
    <div class="header-left">
        <button class="mobile-sidebar-toggle" id="mobileSidebarToggle" type="button">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="page-title">
            <?php
            // Generate page title based on current page
            $page_titles = [
                'index.php' => 'Dashboard',
                'products.php' => 'Products',
                'categories.php' => 'Categories',
                'inventory.php' => 'Stock Management',
                'suppliers.php' => 'Suppliers',
                'billing.php' => 'New Sale',
                'sales.php' => 'Sales History',
                'customers.php' => 'Customers',
                'metal-rates.php' => 'Metal Rates',
                'settings.php' => 'Settings',
                'users.php' => 'Users',
                'backup.php' => 'Backup',
                'sales.php' => 'Sales Reports',
                'financial.php' => 'Financial Reports',
                'inventory.php' => 'Inventory Reports',
                'customers.php' => 'Customer Reports'
            ];
            
            // Check if we're in reports directory
            if ($is_in_reports) {
                $report_titles = [
                    'index.php' => 'Reports Dashboard',
                    'sales.php' => 'Sales Reports',
                    'financial.php' => 'Financial Reports',
                    'inventory.php' => 'Inventory Reports',
                    'customers.php' => 'Customer Reports'
                ];
                echo $report_titles[$current_page] ?? 'Reports';
            } else {
                echo $page_titles[$current_page] ?? 'Page';
            }
            ?>
        </h1>
    </div>
    
    <div class="header-right">
        <!-- Quick Actions Dropdown -->
        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="quickActionsDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>Quick Actions
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>billing.php"><i class="fas fa-plus-circle me-2"></i>New Sale</a></li>
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>products.php?action=add"><i class="fas fa-gem me-2"></i>Add Product</a></li>
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>customers.php?action=add"><i class="fas fa-user-plus me-2"></i>Add Customer</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>inventory.php"><i class="fas fa-boxes me-2"></i>Manage Stock</a></li>
            </ul>
        </div>

        <!-- Notifications -->
        <div class="dropdown">
            <button class="btn btn-outline-secondary position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-bell"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    3
                    <span class="visually-hidden">unread messages</span>
                </span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                <li class="dropdown-header">Notifications</li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Low stock alert for Gold Chain</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-coins text-info me-2"></i>Gold rate updated</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-invoice text-success me-2"></i>New sale completed</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
            </ul>
        </div>

        <!-- User Profile -->
        <div class="dropdown">
            <button class="btn btn-outline-dark dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-2"></i>
                <?php 
                $user = getCurrentUser();
                echo $user ? $user['username'] : 'Admin';
                ?>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="<?php echo $base_path; ?>logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
            </ul>
        </div>
    </div>
</header>
