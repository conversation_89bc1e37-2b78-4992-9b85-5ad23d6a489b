<?php
/**
 * AJAX handler for marking notifications as read
 */

require_once '../config/database.php';
require_once '../includes/notifications.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('Invalid request');
    }
    
    $db = getDB();
    
    switch ($input['action']) {
        case 'mark_all_read':
            // Mark all unread notifications as read
            $result = $db->query("UPDATE notifications SET is_read = 1 WHERE is_read = 0");
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'All notifications marked as read'
                ]);
            } else {
                throw new Exception('Failed to update notifications');
            }
            break;
            
        case 'mark_single_read':
            if (!isset($input['notification_id'])) {
                throw new Exception('Notification ID required');
            }
            
            $result = markNotificationAsRead($input['notification_id']);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
            } else {
                throw new Exception('Failed to mark notification as read');
            }
            break;
            
        default:
            throw new Exception('Unknown action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
