<?php
/**
 * Add Sample Data for Indian Jewellery Wholesale Business
 */

require_once 'config/database.php';
require_once 'lib/TunchCalculator.php';

try {
    $db = getDB();
    $calculator = new TunchCalculator($db);
    
    echo "<h2>💎 Adding Sample Indian Jewellery Data</h2>";
    
    // 1. Add Daily Rates
    echo "<h3>📈 Adding Daily Rates</h3>";
    
    $dailyRates = [
        ['Gold', '24K', 7200, 7128],
        ['Gold', '22K', 6600, 6046],
        ['Gold', '21K', 6300, 5513],
        ['Gold', '20K', 6000, 4998],
        ['Gold', '18K', 5400, 4050],
        ['Silver', '999', 85, 84.9],
        ['Silver', '925', 80, 74.0]
    ];
    
    $ratesAdded = 0;
    foreach ($dailyRates as $rate) {
        try {
            $existing = $db->fetch("
                SELECT id FROM daily_rates 
                WHERE rate_date = CURDATE() AND metal_type = ? AND purity = ?
            ", [$rate[0], $rate[1]]);
            
            if (!$existing) {
                $db->execute("
                    INSERT INTO daily_rates (rate_date, metal_type, purity, base_rate, tunch_rate, market_rate, is_active) 
                    VALUES (CURDATE(), ?, ?, ?, ?, ?, 1)
                ", [$rate[0], $rate[1], $rate[2], $rate[3], $rate[2]]);
                
                echo "✅ Added rate: {$rate[0]} {$rate[1]} - Base: ₹{$rate[2]}, Tunch: ₹{$rate[3]}<br>";
                $ratesAdded++;
            } else {
                echo "⚠️ Rate already exists: {$rate[0]} {$rate[1]}<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error adding rate {$rate[0]} {$rate[1]}: " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. Add Schemes
    echo "<h3>🎁 Adding Sample Schemes</h3>";
    
    $schemes = [
        [
            'name' => 'Diwali Special - 10% Off',
            'type' => 'discount',
            'discount_percentage' => 10.00,
            'min_amount' => 50000,
            'max_amount' => 10000,
            'valid_from' => date('Y-m-01'),
            'valid_to' => date('Y-m-t', strtotime('+1 month'))
        ],
        [
            'name' => 'Making Charges Free',
            'type' => 'making_free',
            'discount_percentage' => 0,
            'min_amount' => 100000,
            'max_amount' => 0,
            'valid_from' => date('Y-m-01'),
            'valid_to' => date('Y-m-t', strtotime('+2 months'))
        ],
        [
            'name' => 'Stone Setting Free',
            'type' => 'stone_free',
            'discount_percentage' => 0,
            'min_amount' => 75000,
            'max_amount' => 5000,
            'valid_from' => date('Y-m-01'),
            'valid_to' => date('Y-m-t', strtotime('+1 month'))
        ]
    ];
    
    $schemesAdded = 0;
    foreach ($schemes as $scheme) {
        try {
            $existing = $db->fetch("SELECT id FROM schemes WHERE scheme_name = ?", [$scheme['name']]);
            
            if (!$existing) {
                $db->execute("
                    INSERT INTO schemes (scheme_name, scheme_type, discount_percentage, min_amount, max_amount, valid_from, valid_to, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                ", [
                    $scheme['name'], $scheme['type'], $scheme['discount_percentage'],
                    $scheme['min_amount'], $scheme['max_amount'], 
                    $scheme['valid_from'], $scheme['valid_to']
                ]);
                
                echo "✅ Added scheme: {$scheme['name']}<br>";
                $schemesAdded++;
            } else {
                echo "⚠️ Scheme already exists: {$scheme['name']}<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error adding scheme {$scheme['name']}: " . $e->getMessage() . "<br>";
        }
    }
    
    // 3. Update Existing Products with Indian Jewellery Data
    echo "<h3>💍 Updating Products with Indian Jewellery Data</h3>";
    
    $jewellerySamples = [
        [
            'name' => 'Gold Ring - Classic',
            'gross_weight' => 8.500,
            'stone_weight' => 0.200,
            'tunch_percentage' => 91.6,
            'making_charges_per_gram' => 800,
            'wastage_percentage' => 2.5,
            'hallmark_charges' => 50,
            'va_percentage' => 1.5,
            'metal_type' => 'Gold',
            'purity' => '22K'
        ],
        [
            'name' => 'Gold Ring - Modern',
            'gross_weight' => 6.200,
            'stone_weight' => 0.800,
            'tunch_percentage' => 75.0,
            'making_charges_per_gram' => 1200,
            'wastage_percentage' => 3.0,
            'hallmark_charges' => 50,
            'va_percentage' => 2.0,
            'metal_type' => 'Gold',
            'purity' => '18K'
        ],
        [
            'name' => 'Gold Necklace Set',
            'gross_weight' => 45.800,
            'stone_weight' => 2.300,
            'tunch_percentage' => 91.6,
            'making_charges_per_gram' => 600,
            'wastage_percentage' => 2.0,
            'hallmark_charges' => 100,
            'va_percentage' => 1.0,
            'metal_type' => 'Gold',
            'purity' => '22K'
        ],
        [
            'name' => 'Gold Earrings',
            'gross_weight' => 12.400,
            'stone_weight' => 1.800,
            'tunch_percentage' => 91.6,
            'making_charges_per_gram' => 1000,
            'wastage_percentage' => 3.5,
            'hallmark_charges' => 75,
            'va_percentage' => 1.5,
            'metal_type' => 'Gold',
            'purity' => '22K'
        ],
        [
            'name' => 'Silver Bracelet',
            'gross_weight' => 25.600,
            'stone_weight' => 0.000,
            'tunch_percentage' => 92.5,
            'making_charges_per_gram' => 50,
            'wastage_percentage' => 1.5,
            'hallmark_charges' => 25,
            'va_percentage' => 0.5,
            'metal_type' => 'Silver',
            'purity' => '925'
        ]
    ];
    
    $productsUpdated = 0;
    $products = $db->fetchAll("SELECT id, product_name FROM products ORDER BY id LIMIT 5");
    
    foreach ($products as $index => $product) {
        if (isset($jewellerySamples[$index])) {
            $sample = $jewellerySamples[$index];
            
            try {
                // Calculate net weight
                $netWeight = $sample['gross_weight'] - $sample['stone_weight'];
                
                // Update product
                $db->execute("
                    UPDATE products SET 
                        product_name = ?,
                        metal_type = ?,
                        purity = ?,
                        gross_weight = ?,
                        stone_weight = ?,
                        net_weight = ?,
                        tunch_percentage = ?,
                        making_charges_per_gram = ?,
                        wastage_percentage = ?,
                        hallmark_charges = ?,
                        va_percentage = ?,
                        scheme_applicable = 1,
                        updated_at = NOW()
                    WHERE id = ?
                ", [
                    $sample['name'], $sample['metal_type'], $sample['purity'],
                    $sample['gross_weight'], $sample['stone_weight'], $netWeight,
                    $sample['tunch_percentage'], $sample['making_charges_per_gram'],
                    $sample['wastage_percentage'], $sample['hallmark_charges'],
                    $sample['va_percentage'], $product['id']
                ]);
                
                // Calculate pricing using Tunch Calculator
                $calculation = $calculator->calculateProductPrice($product['id']);
                
                // Update inventory with calculated prices
                $db->execute("
                    UPDATE inventory SET 
                        base_rate = ?,
                        tunch_rate = ?,
                        stone_cost_per_piece = ?,
                        making_cost = ?,
                        total_metal_value = ?,
                        wastage_amount = ?,
                        cost_price = ?,
                        selling_price = ?,
                        mrp = ?,
                        updated_at = NOW()
                    WHERE product_id = ?
                ", [
                    $calculation['base_rate'],
                    $calculation['tunch_rate'],
                    $calculation['stone_charges'],
                    $calculation['making_charges'],
                    $calculation['metal_value'],
                    $calculation['wastage_amount'],
                    $calculation['final_value'] * 0.85, // Cost price (85% of selling)
                    $calculation['final_value'],
                    $calculation['final_value'] * 1.15, // MRP (15% above selling)
                    $product['id']
                ]);
                
                echo "✅ Updated product: {$sample['name']} - Selling Price: ₹" . number_format($calculation['final_value'], 2) . "<br>";
                $productsUpdated++;
                
            } catch (Exception $e) {
                echo "❌ Error updating product {$product['product_name']}: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    // 4. Add Sample Customers with Indian Names
    echo "<h3>👥 Adding Indian Jewellery Customers</h3>";
    
    $indianCustomers = [
        ['Rajesh Kumar Jewellers', 'Rajesh Kumar', '9876543210', '<EMAIL>', 'Mumbai', '27ABCDE1234F1Z5'],
        ['Priya Gold House', 'Priya Sharma', '9876543211', '<EMAIL>', 'Delhi', '07FGHIJ5678K2L6'],
        ['Amit Jewellery Mart', 'Amit Patel', '9876543212', '<EMAIL>', 'Surat', '24MNOPQ9012R3S7'],
        ['Sunita Ornaments', 'Sunita Devi', '9876543213', '<EMAIL>', 'Jaipur', '08TUVWX3456Y4Z8'],
        ['Kiran Silver Works', 'Kiran Singh', '9876543214', '<EMAIL>', 'Kolkata', '19ABCDE7890F5G9']
    ];
    
    $customersAdded = 0;
    foreach ($indianCustomers as $customer) {
        try {
            $existing = $db->fetch("SELECT id FROM customers WHERE phone = ?", [$customer[2]]);
            
            if (!$existing) {
                $db->execute("
                    INSERT INTO customers (business_name, customer_name, phone, email, city, gst_number, customer_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, 'wholesale', 1)
                ", $customer);
                
                echo "✅ Added customer: {$customer[0]}<br>";
                $customersAdded++;
            } else {
                echo "⚠️ Customer already exists: {$customer[0]}<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error adding customer {$customer[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    // Summary
    echo "<h3>📊 Data Addition Summary</h3>";
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Successfully Added:</h4>";
    echo "<ul>";
    echo "<li><strong>Daily Rates:</strong> $ratesAdded new rates</li>";
    echo "<li><strong>Schemes:</strong> $schemesAdded new schemes</li>";
    echo "<li><strong>Products Updated:</strong> $productsUpdated with Indian jewellery data</li>";
    echo "<li><strong>Customers:</strong> $customersAdded Indian jewellery customers</li>";
    echo "</ul>";
    echo "</div>";
    
    // Show sample calculations
    echo "<h3>🧮 Sample Tunch Calculations</h3>";
    
    $sampleProducts = $db->fetchAll("
        SELECT p.product_name, p.gross_weight, p.stone_weight, p.net_weight, 
               p.tunch_percentage, p.metal_type, p.purity,
               i.selling_price, i.base_rate, i.tunch_rate
        FROM products p 
        LEFT JOIN inventory i ON p.id = i.product_id 
        WHERE p.gross_weight > 0 
        LIMIT 3
    ");
    
    if (count($sampleProducts) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Product</th>";
        echo "<th style='padding: 8px;'>Gross Wt</th>";
        echo "<th style='padding: 8px;'>Stone Wt</th>";
        echo "<th style='padding: 8px;'>Net Wt</th>";
        echo "<th style='padding: 8px;'>Tunch %</th>";
        echo "<th style='padding: 8px;'>Base Rate</th>";
        echo "<th style='padding: 8px;'>Selling Price</th>";
        echo "</tr>";
        
        foreach ($sampleProducts as $product) {
            echo "<tr>";
            echo "<td style='padding: 8px;'><strong>{$product['product_name']}</strong><br><small>{$product['metal_type']} {$product['purity']}</small></td>";
            echo "<td style='padding: 8px;'>{$product['gross_weight']}g</td>";
            echo "<td style='padding: 8px;'>{$product['stone_weight']}g</td>";
            echo "<td style='padding: 8px;'>{$product['net_weight']}g</td>";
            echo "<td style='padding: 8px;'>{$product['tunch_percentage']}%</td>";
            echo "<td style='padding: 8px;'>₹{$product['base_rate']}</td>";
            echo "<td style='padding: 8px;'><strong>₹" . number_format($product['selling_price'], 2) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='test-tunch-calculator.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Calculator</a>";
    echo "<a href='inventory.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>View Inventory</a>";
    echo "<a href='billing.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Billing</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Error Adding Sample Data</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
