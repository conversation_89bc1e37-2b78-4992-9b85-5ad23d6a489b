<?php
/**
 * Customer Payment History - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();

$db = getDB();
$customer_id = $_GET['id'] ?? null;

if (!$customer_id) {
    die('Customer ID required');
}

try {
    // Get customer details
    $customer = $db->fetch("
        SELECT customer_name, business_name, phone, credit_limit, credit_days,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0) 
                FROM sales WHERE customer_id = ? AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers 
        WHERE id = ? AND is_active = 1
    ", [$customer_id, $customer_id]);

    if (!$customer) {
        die('Customer not found');
    }

    // Get sales with payment details
    $salesWithPayments = $db->fetchAll("
        SELECT s.*, 
               COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0) as paid_amount,
               (s.grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0)) as balance_amount
        FROM sales s
        WHERE s.customer_id = ? AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC, s.sale_time DESC
    ", [$customer_id]);

    // Get payment transactions
    $payments = $db->fetchAll("
        SELECT p.*, s.bill_number, s.grand_total, u.username
        FROM payments p
        JOIN sales s ON p.sale_id = s.id
        LEFT JOIN users u ON p.created_by = u.id
        WHERE s.customer_id = ?
        ORDER BY p.payment_date DESC, p.created_at DESC
    ", [$customer_id]);

} catch (Exception $e) {
    die('Error loading payment history: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment History - <?php echo htmlspecialchars($customer['customer_name']); ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
        .payment-card { border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .paid-row { background-color: #d4edda; }
        .partial-row { background-color: #fff3cd; }
        .unpaid-row { background-color: #f8d7da; }
        .payment-badge { font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="mb-1">Payment History</h3>
                <p class="text-muted mb-0">
                    <?php echo htmlspecialchars($customer['customer_name']); ?>
                    <?php if ($customer['business_name']): ?>
                        - <?php echo htmlspecialchars($customer['business_name']); ?>
                    <?php endif; ?>
                </p>
            </div>
            <button class="btn btn-outline-secondary" onclick="window.close()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card payment-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                        <h4 class="text-danger"><?php echo formatCurrency($customer['outstanding_amount']); ?></h4>
                        <p class="mb-0">Outstanding Amount</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card payment-card">
                    <div class="card-body text-center">
                        <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo formatCurrency($customer['credit_limit']); ?></h4>
                        <p class="mb-0">Credit Limit</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card payment-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo $customer['credit_days']; ?></h4>
                        <p class="mb-0">Credit Days</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales and Payment Status -->
        <div class="card payment-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Sales & Payment Status</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Bill Number</th>
                                <th>Sale Date</th>
                                <th>Total Amount</th>
                                <th>Paid Amount</th>
                                <th>Balance</th>
                                <th>Payment Status</th>
                                <th>Days Overdue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($salesWithPayments)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        <i class="fas fa-receipt fa-3x mb-3 opacity-25"></i>
                                        <p>No sales found</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($salesWithPayments as $sale): ?>
                                    <?php 
                                    $rowClass = '';
                                    $statusBadge = '';
                                    $daysOverdue = 0;
                                    
                                    if ($sale['balance_amount'] <= 0) {
                                        $rowClass = 'paid-row';
                                        $statusBadge = '<span class="badge bg-success payment-badge">Paid</span>';
                                    } elseif ($sale['paid_amount'] > 0) {
                                        $rowClass = 'partial-row';
                                        $statusBadge = '<span class="badge bg-warning payment-badge">Partial</span>';
                                    } else {
                                        $rowClass = 'unpaid-row';
                                        $statusBadge = '<span class="badge bg-danger payment-badge">Unpaid</span>';
                                    }
                                    
                                    // Calculate days overdue
                                    if ($sale['balance_amount'] > 0 && $customer['credit_days'] > 0) {
                                        $dueDate = date('Y-m-d', strtotime($sale['sale_date'] . ' + ' . $customer['credit_days'] . ' days'));
                                        if (date('Y-m-d') > $dueDate) {
                                            $daysOverdue = (strtotime(date('Y-m-d')) - strtotime($dueDate)) / (60 * 60 * 24);
                                        }
                                    }
                                    ?>
                                    <tr class="<?php echo $rowClass; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($sale['bill_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatDate($sale['sale_date']); ?></td>
                                        <td><?php echo formatCurrency($sale['grand_total']); ?></td>
                                        <td><?php echo formatCurrency($sale['paid_amount']); ?></td>
                                        <td>
                                            <strong class="<?php echo $sale['balance_amount'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                                <?php echo formatCurrency($sale['balance_amount']); ?>
                                            </strong>
                                        </td>
                                        <td><?php echo $statusBadge; ?></td>
                                        <td>
                                            <?php if ($daysOverdue > 0): ?>
                                                <span class="badge bg-danger"><?php echo floor($daysOverdue); ?> days</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Transactions -->
        <div class="card payment-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill me-2"></i>Payment Transactions</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Payment Date</th>
                                <th>Bill Number</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Reference</th>
                                <th>Received By</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($payments)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        <i class="fas fa-money-bill fa-3x mb-3 opacity-25"></i>
                                        <p>No payments found</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($payments as $payment): ?>
                                    <tr>
                                        <td><?php echo formatDate($payment['payment_date']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($payment['bill_number']); ?></strong><br>
                                            <small class="text-muted">Total: <?php echo formatCurrency($payment['grand_total']); ?></small>
                                        </td>
                                        <td>
                                            <strong class="text-success"><?php echo formatCurrency($payment['amount']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo ucfirst($payment['payment_method']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($payment['reference_number']): ?>
                                                <small><?php echo htmlspecialchars($payment['reference_number']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($payment['username'] ?? 'System'); ?>
                                        </td>
                                        <td>
                                            <?php if ($payment['notes']): ?>
                                                <small><?php echo htmlspecialchars($payment['notes']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <button class="btn btn-primary" onclick="recordPayment()">
                <i class="fas fa-plus me-2"></i>Record Payment
            </button>
            <button class="btn btn-success" onclick="sendPaymentReminder()">
                <i class="fas fa-envelope me-2"></i>Send Reminder
            </button>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Print Report
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function recordPayment() {
            // This would open a payment recording modal or redirect to payment page
            alert('Payment recording functionality would be implemented here');
        }

        function sendPaymentReminder() {
            // This would send payment reminder email/SMS
            alert('Payment reminder functionality would be implemented here');
        }
    </script>
</body>
</html>
