<?php
/**
 * Metal Rates Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';

// Handle success message from redirect
if (isset($_GET['success'])) {
    $success = urldecode($_GET['success']);
}

// Handle form submissions
if ($_POST) {
    try {
        $form_action = $_POST['action'] ?? 'none';

        if ($form_action === 'update_rates') {
            $rates = $_POST['rates'] ?? [];
            $rate_date = $_POST['rate_date'] ?? date('Y-m-d');
            $updated_count = 0;

            foreach ($rates as $rate_data) {
                $metal_type = sanitizeInput($rate_data['metal_type']);
                $purity = sanitizeInput($rate_data['purity']);
                $rate_per_gram = floatval($rate_data['rate_per_gram']);
                
                if ($metal_type && $purity && $rate_per_gram > 0) {
                    // Check if rate exists for this date
                    $existing = $db->fetch("
                        SELECT id FROM metal_rates 
                        WHERE metal_type = ? AND purity = ? AND rate_date = ?
                    ", [$metal_type, $purity, $rate_date]);
                    
                    if ($existing) {
                        // Update existing rate
                        $db->query("
                            UPDATE metal_rates 
                            SET rate_per_gram = ?, is_active = 1 
                            WHERE id = ?
                        ", [$rate_per_gram, $existing['id']]);
                    } else {
                        // Insert new rate
                        $db->query("
                            INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, created_by)
                            VALUES (?, ?, ?, ?, 1)
                        ", [$metal_type, $purity, $rate_per_gram, $rate_date]);
                    }
                    $updated_count++;
                }
            }

            // Redirect with success message to prevent form resubmission
            $success_msg = urlencode("Metal rates updated successfully for " . formatDate($rate_date) . " ($updated_count rates updated)");
            header("Location: metal-rates.php?success=" . $success_msg . "&date=" . urlencode($rate_date));
            exit;
        } else {
            $error = "Invalid form submission. Please try again.";
        }
    } catch (Exception $e) {
        $error = "Error updating rates: " . $e->getMessage();
    }
}

// Get current rates
$current_date = $_GET['date'] ?? date('Y-m-d');
$current_rates = $db->fetchAll("
    SELECT * FROM metal_rates 
    WHERE rate_date = ? AND is_active = 1 
    ORDER BY metal_type, purity
", [$current_date]);

// Get rate history
$rate_history = $db->fetchAll("
    SELECT mr.*, u.full_name as created_by_name
    FROM metal_rates mr
    LEFT JOIN users u ON mr.created_by = u.id
    WHERE mr.is_active = 1
    ORDER BY mr.rate_date DESC, mr.metal_type, mr.purity
    LIMIT 50
");

// Organize current rates by metal type
$rates_by_metal = [];
foreach ($current_rates as $rate) {
    $rates_by_metal[$rate['metal_type']][$rate['purity']] = $rate['rate_per_gram'];
}

// Default metal types and purities
$metal_types = [
    'Gold' => ['24K', '22K', '18K'],
    'Silver' => ['999', '925'],
    'Platinum' => ['950']
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal Rates - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
    
    <style>
        .rate-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .rate-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        .rate-input {
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
        }
        .metal-gold { border-left: 4px solid #ffd700; }
        .metal-silver { border-left: 4px solid #c0c0c0; }
        .metal-platinum { border-left: 4px solid #e5e4e2; }
    </style>
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">Metal Rates</h2>
                <p class="text-muted mb-0">Manage daily gold, silver, and platinum rates</p>
            </div>
            <div class="d-flex gap-2">
                <div class="btn-group">
                    <button class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="export-metal-rates.php?format=csv"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                        <li><a class="dropdown-item" href="export-metal-rates.php?format=excel"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                        <li><a class="dropdown-item" href="export-metal-rates.php?format=pdf"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                    </ul>
                </div>
                <button class="btn btn-outline-info" onclick="showRateAnalytics()">
                    <i class="fas fa-chart-line me-2"></i>Analytics
                </button>
                <button class="btn btn-outline-warning" onclick="showBulkImport()">
                    <i class="fas fa-upload me-2"></i>Bulk Import
                </button>
                <button class="btn btn-outline-primary" onclick="loadTodayRates()">
                    <i class="fas fa-sync me-2"></i>Today's Rates
                </button>
                <button class="btn btn-success" onclick="copyPreviousRates()">
                    <i class="fas fa-copy me-2"></i>Copy Previous Day
                </button>
            </div>
        </div>

        <!-- Rate Update Form -->
        <form method="POST" id="ratesForm">
            <input type="hidden" name="action" value="update_rates">
            
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Update Rates</h5>
                    <div class="d-flex align-items-center">
                        <label for="rate_date" class="form-label me-2 mb-0">Date:</label>
                        <input type="date" class="form-control" name="rate_date" id="rate_date" 
                               value="<?php echo $current_date; ?>" onchange="loadRatesForDate(this.value)">
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($metal_types as $metal => $purities): ?>
                            <div class="col-lg-4 mb-4">
                                <div class="card rate-card metal-<?php echo strtolower($metal); ?>">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-coins me-2"></i>
                                            <?php echo $metal; ?> Rates
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($purities as $purity): ?>
                                            <div class="mb-3">
                                                <label class="form-label"><?php echo $purity; ?> (per gram)</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">₹</span>
                                                    <input type="number" class="form-control rate-input" 
                                                           name="rates[<?php echo $metal . '_' . $purity; ?>][rate_per_gram]"
                                                           value="<?php echo $rates_by_metal[$metal][$purity] ?? ''; ?>"
                                                           step="0.01" min="0" placeholder="0.00">
                                                    <input type="hidden" name="rates[<?php echo $metal . '_' . $purity; ?>][metal_type]" value="<?php echo $metal; ?>">
                                                    <input type="hidden" name="rates[<?php echo $metal . '_' . $purity; ?>][purity]" value="<?php echo $purity; ?>">
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>Update Rates
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Current Rates Display -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Current Rates - <?php echo formatDate($current_date); ?></h5>
            </div>
            <div class="card-body">
                <?php if (empty($current_rates)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-coins fa-3x mb-3 opacity-25"></i>
                        <p>No rates set for this date</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php 
                        $grouped_rates = [];
                        foreach ($current_rates as $rate) {
                            $grouped_rates[$rate['metal_type']][] = $rate;
                        }
                        ?>
                        
                        <?php foreach ($grouped_rates as $metal => $rates): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-uppercase"><?php echo $metal; ?></h6>
                                        <?php foreach ($rates as $rate): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="badge bg-secondary"><?php echo $rate['purity']; ?></span>
                                                <strong class="text-primary">₹<?php echo number_format($rate['rate_per_gram'], 2); ?></strong>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Rate History -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Rate History</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Metal</th>
                                <th>Purity</th>
                                <th>Rate per Gram</th>
                                <th>Updated By</th>
                                <th>Updated At</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($rate_history)): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4 text-muted">
                                        <i class="fas fa-history fa-3x mb-3 opacity-25"></i>
                                        <p>No rate history found</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($rate_history as $rate): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo formatDate($rate['rate_date']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $rate['metal_type'] === 'Gold' ? 'warning' : ($rate['metal_type'] === 'Silver' ? 'secondary' : 'info'); ?>">
                                                <?php echo $rate['metal_type']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <code><?php echo $rate['purity']; ?></code>
                                        </td>
                                        <td>
                                            <strong class="text-primary">₹<?php echo number_format($rate['rate_per_gram'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($rate['created_by_name'] ?: 'System'); ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo formatDateTime($rate['created_at']); ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </main>
    </div>
    </div>

    <!-- Rate Analytics Modal -->
    <div class="modal fade" id="rateAnalyticsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Metal Rate Analytics</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Rate Trends (Last 30 Days)</h6>
                            <canvas id="rateTrendChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h6>Rate Statistics</h6>
                            <div class="list-group" id="rateStatistics">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>Price Alerts</h6>
                            <div id="priceAlerts">
                                <!-- Dynamic alerts -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Market Insights</h6>
                            <div id="marketInsights">
                                <!-- Dynamic insights -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportAnalytics()">Export Report</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Import Modal -->
    <div class="modal fade" id="bulkImportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Import Rates</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Import Method</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="importMethod" id="importFile" value="file" checked>
                            <label class="form-check-label" for="importFile">Upload CSV File</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="importMethod" id="importAPI" value="api">
                            <label class="form-check-label" for="importAPI">Fetch from API</label>
                        </div>
                    </div>

                    <div id="fileImportSection">
                        <div class="mb-3">
                            <label for="rateFile" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" id="rateFile" accept=".csv">
                            <small class="text-muted">Format: Metal Type, Purity, Rate per Gram</small>
                        </div>
                        <div class="mb-3">
                            <a href="sample-rates.csv" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-download me-1"></i>Download Sample CSV
                            </a>
                        </div>
                    </div>

                    <div id="apiImportSection" style="display: none;">
                        <div class="mb-3">
                            <label for="apiSource" class="form-label">API Source</label>
                            <select class="form-select" id="apiSource">
                                <option value="">Select API Source</option>
                                <option value="goldapi">GoldAPI.io</option>
                                <option value="metals">Metals-API.com</option>
                                <option value="custom">Custom API</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="apiKey" class="form-label">API Key</label>
                            <input type="text" class="form-control" id="apiKey" placeholder="Enter API key">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="importDate" class="form-label">Import Date</label>
                        <input type="date" class="form-control" id="importDate" value="<?php echo date('Y-m-d'); ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processBulkImport()">Import Rates</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rate History Modal -->
    <div class="modal fade" id="rateHistoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Rate History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="rateHistoryContent">
                        <!-- Dynamic content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function loadTodayRates() {
            document.getElementById('rate_date').value = new Date().toISOString().split('T')[0];
            loadRatesForDate(document.getElementById('rate_date').value);
        }
        
        function loadRatesForDate(date) {
            window.location.href = `?date=${date}`;
        }
        
        function copyPreviousRates() {
            const currentDate = new Date(document.getElementById('rate_date').value);
            const previousDate = new Date(currentDate);
            previousDate.setDate(previousDate.getDate() - 1);
            
            const prevDateStr = previousDate.toISOString().split('T')[0];
            
            if (confirm(`Copy rates from ${prevDateStr}?`)) {
                // Fetch previous day rates via AJAX
                fetch(`get-rates.php?date=${prevDateStr}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Populate form with previous rates
                            data.rates.forEach(rate => {
                                const inputName = `rates[${rate.metal_type}_${rate.purity}][rate_per_gram]`;
                                const input = document.querySelector(`input[name="${inputName}"]`);
                                if (input) {
                                    input.value = rate.rate_per_gram;
                                }
                            });
                            
                            jewelleryApp.showNotification('Previous day rates copied successfully', 'success');
                        } else {
                            jewelleryApp.showNotification('No rates found for previous day', 'warning');
                        }
                    })
                    .catch(error => {
                        jewelleryApp.showNotification('Error copying rates', 'danger');
                    });
            }
        }
        
        // Auto-save functionality
        let autoSaveTimeout;
        document.querySelectorAll('.rate-input').forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    // Auto-save after 2 seconds of no input
                    if (this.value && parseFloat(this.value) > 0) {
                        jewelleryApp.showNotification('Rate updated', 'info');
                    }
                }, 2000);
            });
        });
        
        // Highlight changed rates
        document.querySelectorAll('.rate-input').forEach(input => {
            const originalValue = input.value;
            input.addEventListener('change', function() {
                if (this.value !== originalValue) {
                    this.classList.add('border-warning');
                    this.classList.add('bg-warning-subtle');
                } else {
                    this.classList.remove('border-warning');
                    this.classList.remove('bg-warning-subtle');
                }
            });
        });

        // Enhanced metal rates functions
        function showRateAnalytics() {
            const modal = new bootstrap.Modal(document.getElementById('rateAnalyticsModal'));
            modal.show();
            loadRateAnalytics();
        }

        function loadRateAnalytics() {
            // Load analytics data
            fetch('ajax/metal-rate-analytics.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAnalyticsDisplay(data);
                        drawRateTrendChart(data.trends);
                    }
                })
                .catch(error => {
                    console.error('Error loading analytics:', error);
                });
        }

        function updateAnalyticsDisplay(data) {
            // Update statistics
            const statisticsHtml = data.statistics.map(stat => `
                <div class="list-group-item d-flex justify-content-between">
                    <span>${stat.label}</span>
                    <strong>${stat.value}</strong>
                </div>
            `).join('');
            document.getElementById('rateStatistics').innerHTML = statisticsHtml;

            // Update price alerts
            const alertsHtml = data.alerts.map(alert => `
                <div class="alert alert-${alert.type} alert-sm">
                    <i class="fas fa-${alert.icon} me-2"></i>${alert.message}
                </div>
            `).join('');
            document.getElementById('priceAlerts').innerHTML = alertsHtml;

            // Update market insights
            const insightsHtml = data.insights.map(insight => `
                <div class="mb-2">
                    <strong>${insight.title}</strong><br>
                    <small class="text-muted">${insight.description}</small>
                </div>
            `).join('');
            document.getElementById('marketInsights').innerHTML = insightsHtml;
        }

        function drawRateTrendChart(trends) {
            // This would integrate with Chart.js to draw the trend chart
            console.log('Drawing rate trend chart with data:', trends);
        }

        function showBulkImport() {
            const modal = new bootstrap.Modal(document.getElementById('bulkImportModal'));
            modal.show();

            // Setup import method listeners
            document.querySelectorAll('input[name="importMethod"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const fileSection = document.getElementById('fileImportSection');
                    const apiSection = document.getElementById('apiImportSection');

                    if (this.value === 'file') {
                        fileSection.style.display = 'block';
                        apiSection.style.display = 'none';
                    } else {
                        fileSection.style.display = 'none';
                        apiSection.style.display = 'block';
                    }
                });
            });
        }

        function processBulkImport() {
            const importMethod = document.querySelector('input[name="importMethod"]:checked').value;
            const importDate = document.getElementById('importDate').value;

            if (importMethod === 'file') {
                const fileInput = document.getElementById('rateFile');
                if (!fileInput.files[0]) {
                    alert('Please select a CSV file');
                    return;
                }

                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('date', importDate);
                formData.append('action', 'bulk_import');

                fetch('process-bulk-import.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Successfully imported ${data.count} rates`);
                        location.reload();
                    } else {
                        alert('Import failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Import error: ' + error.message);
                });

            } else if (importMethod === 'api') {
                const apiSource = document.getElementById('apiSource').value;
                const apiKey = document.getElementById('apiKey').value;

                if (!apiSource || !apiKey) {
                    alert('Please select API source and enter API key');
                    return;
                }

                // Process API import
                fetch('process-api-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        source: apiSource,
                        apiKey: apiKey,
                        date: importDate
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Successfully imported rates from ${apiSource}`);
                        location.reload();
                    } else {
                        alert('API import failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('API import error: ' + error.message);
                });
            }
        }

        function showRateHistory(metalType, purity) {
            const modal = new bootstrap.Modal(document.getElementById('rateHistoryModal'));
            modal.show();

            // Load rate history
            document.getElementById('rateHistoryContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

            fetch(`ajax/rate-history.php?metal=${metalType}&purity=${purity}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('rateHistoryContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('rateHistoryContent').innerHTML = '<div class="alert alert-danger">Error loading history</div>';
                });
        }

        function exportAnalytics() {
            window.open('export-rate-analytics.php', '_blank');
        }

        // Real-time rate updates (WebSocket or polling)
        function startRealTimeUpdates() {
            // This would implement real-time rate updates
            setInterval(() => {
                // Check for rate updates every 5 minutes
                fetch('ajax/check-rate-updates.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.hasUpdates) {
                            showUpdateNotification();
                        }
                    })
                    .catch(error => {
                        console.error('Error checking for updates:', error);
                    });
            }, 300000); // 5 minutes
        }

        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>New rate updates available
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                <div class="mt-2">
                    <button class="btn btn-sm btn-primary" onclick="location.reload()">Refresh</button>
                </div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        // Initialize real-time updates
        document.addEventListener('DOMContentLoaded', function() {
            startRealTimeUpdates();
        });
    </script>
</body>
</html>
