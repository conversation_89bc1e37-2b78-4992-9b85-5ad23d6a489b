<?php
/**
 * System Diagnostics AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $db = getDB();
    
    // System Information
    $systemInfo = [
        'PHP Version' => phpversion(),
        'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'Operating System' => php_uname('s') . ' ' . php_uname('r'),
        'Memory Limit' => ini_get('memory_limit'),
        'Max Execution Time' => ini_get('max_execution_time') . ' seconds',
        'Upload Max Filesize' => ini_get('upload_max_filesize'),
        'Post Max Size' => ini_get('post_max_size'),
        'Timezone' => date_default_timezone_get(),
        'Current Time' => date('Y-m-d H:i:s')
    ];
    
    // Database Information
    $dbInfo = [];
    try {
        $dbVersion = $db->fetch("SELECT VERSION() as version")['version'];
        $dbInfo['Database Version'] = $dbVersion;
        
        $dbSize = $db->fetch("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ")['size_mb'];
        $dbInfo['Database Size'] = $dbSize . ' MB';
        
        $tableCount = $db->fetch("
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ")['count'];
        $dbInfo['Total Tables'] = $tableCount;
        
    } catch (Exception $e) {
        $dbInfo['Database Error'] = $e->getMessage();
    }
    
    // Application Statistics
    $appStats = [];
    try {
        $appStats['Total Products'] = $db->fetch("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'];
        $appStats['Total Categories'] = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'];
        $appStats['Total Customers'] = $db->fetch("SELECT COUNT(*) as count FROM customers WHERE is_active = 1")['count'];
        $appStats['Total Suppliers'] = $db->fetch("SELECT COUNT(*) as count FROM suppliers WHERE is_active = 1")['count'];
        $appStats['Total Sales'] = $db->fetch("SELECT COUNT(*) as count FROM sales WHERE is_cancelled = 0")['count'];
        $appStats['Total Users'] = $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'];
    } catch (Exception $e) {
        $appStats['Application Error'] = $e->getMessage();
    }
    
    // System Health Checks
    $healthChecks = [];
    
    // Check disk space
    $diskFree = disk_free_space('.');
    $diskTotal = disk_total_space('.');
    $diskUsedPercent = (($diskTotal - $diskFree) / $diskTotal) * 100;
    $healthChecks['Disk Space'] = [
        'status' => $diskUsedPercent > 90 ? 'danger' : ($diskUsedPercent > 75 ? 'warning' : 'success'),
        'message' => round($diskUsedPercent, 1) . '% used (' . formatBytes($diskFree) . ' free)',
        'icon' => 'fas fa-hdd'
    ];
    
    // Check memory usage
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = ini_get('memory_limit');
    $memoryLimitBytes = convertToBytes($memoryLimit);
    $memoryPercent = ($memoryUsage / $memoryLimitBytes) * 100;
    $healthChecks['Memory Usage'] = [
        'status' => $memoryPercent > 80 ? 'danger' : ($memoryPercent > 60 ? 'warning' : 'success'),
        'message' => round($memoryPercent, 1) . '% used (' . formatBytes($memoryUsage) . ' / ' . $memoryLimit . ')',
        'icon' => 'fas fa-memory'
    ];
    
    // Check database connection
    try {
        $db->fetch("SELECT 1");
        $healthChecks['Database Connection'] = [
            'status' => 'success',
            'message' => 'Connected successfully',
            'icon' => 'fas fa-database'
        ];
    } catch (Exception $e) {
        $healthChecks['Database Connection'] = [
            'status' => 'danger',
            'message' => 'Connection failed: ' . $e->getMessage(),
            'icon' => 'fas fa-database'
        ];
    }
    
    // Check required PHP extensions
    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl', 'curl'];
    $missingExtensions = [];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $missingExtensions[] = $ext;
        }
    }
    
    $healthChecks['PHP Extensions'] = [
        'status' => empty($missingExtensions) ? 'success' : 'danger',
        'message' => empty($missingExtensions) ? 'All required extensions loaded' : 'Missing: ' . implode(', ', $missingExtensions),
        'icon' => 'fab fa-php'
    ];
    
    // Check file permissions
    $writableDirs = ['uploads', 'cache', 'logs'];
    $permissionIssues = [];
    foreach ($writableDirs as $dir) {
        if (!is_writable($dir)) {
            $permissionIssues[] = $dir;
        }
    }
    
    $healthChecks['File Permissions'] = [
        'status' => empty($permissionIssues) ? 'success' : 'warning',
        'message' => empty($permissionIssues) ? 'All directories writable' : 'Not writable: ' . implode(', ', $permissionIssues),
        'icon' => 'fas fa-lock'
    ];
    
    // Generate HTML output
    echo "<div class='row'>";
    
    // System Health
    echo "<div class='col-12 mb-4'>";
    echo "<h6><i class='fas fa-heartbeat me-2'></i>System Health</h6>";
    echo "<div class='row'>";
    
    foreach ($healthChecks as $check => $data) {
        $badgeClass = $data['status'] === 'success' ? 'bg-success' : ($data['status'] === 'warning' ? 'bg-warning' : 'bg-danger');
        echo "<div class='col-md-6 mb-2'>";
        echo "<div class='d-flex align-items-center'>";
        echo "<i class='{$data['icon']} me-2'></i>";
        echo "<span class='me-2'><strong>$check:</strong></span>";
        echo "<span class='badge $badgeClass'>{$data['message']}</span>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // System Information
    echo "<div class='col-md-4 mb-4'>";
    echo "<h6><i class='fas fa-server me-2'></i>System Information</h6>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    foreach ($systemInfo as $key => $value) {
        echo "<tr><td><strong>$key:</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    // Database Information
    echo "<div class='col-md-4 mb-4'>";
    echo "<h6><i class='fas fa-database me-2'></i>Database Information</h6>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    foreach ($dbInfo as $key => $value) {
        echo "<tr><td><strong>$key:</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    // Application Statistics
    echo "<div class='col-md-4 mb-4'>";
    echo "<h6><i class='fas fa-chart-bar me-2'></i>Application Statistics</h6>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    foreach ($appStats as $key => $value) {
        echo "<tr><td><strong>$key:</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    // Helper functions
    function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    function convertToBytes($value) {
        $value = trim($value);
        $last = strtolower($value[strlen($value)-1]);
        $value = (int) $value;
        
        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }
        
        return $value;
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
