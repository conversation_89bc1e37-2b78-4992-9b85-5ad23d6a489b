<?php
/**
 * Sales Reports - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
$customer_id = $_GET['customer_id'] ?? '';
$user_id = $_GET['user_id'] ?? '';
$payment_status = $_GET['payment_status'] ?? '';

// Build WHERE clause
$where_conditions = ["s.is_cancelled = 0"];
$params = [];

if ($date_from) {
    $where_conditions[] = "s.sale_date >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "s.sale_date <= ?";
    $params[] = $date_to;
}

if ($customer_id) {
    $where_conditions[] = "s.customer_id = ?";
    $params[] = $customer_id;
}

if ($user_id) {
    $where_conditions[] = "s.created_by = ?";
    $params[] = $user_id;
}

if ($payment_status) {
    $where_conditions[] = "s.payment_status = ?";
    $params[] = $payment_status;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // Get sales data
    $sales = $db->fetchAll("
        SELECT s.*, c.customer_name, c.business_name, u.full_name as created_by_name,
               (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        WHERE $where_clause
        ORDER BY s.sale_date DESC, s.sale_time DESC
    ", $params);

    // Get summary statistics
    $summary = $db->fetch("
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(s.grand_total), 0) as total_amount,
            COALESCE(SUM(s.paid_amount), 0) as total_paid,
            COALESCE(SUM(s.balance_amount), 0) as total_outstanding,
            COALESCE(AVG(s.grand_total), 0) as average_sale
        FROM sales s
        WHERE $where_clause
    ", $params);

    // Get customers for filter
    $customers = $db->fetchAll("
        SELECT id, customer_name, business_name 
        FROM customers 
        WHERE is_active = 1 
        ORDER BY customer_name
    ");

    // Get users for filter
    $users = $db->fetchAll("
        SELECT id, full_name, username 
        FROM users 
        WHERE is_active = 1 
        ORDER BY full_name
    ");

} catch (Exception $e) {
    $error = "Error loading sales data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Reports - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Sales Reports</h2>
                    <p class="text-muted mb-0">Analyze sales performance and trends</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Customer</label>
                            <select class="form-select" name="customer_id">
                                <option value="">All Customers</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo $customer_id == $customer['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['customer_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Sales Person</label>
                            <select class="form-select" name="user_id">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $user_id == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Payment Status</label>
                            <select class="form-select" name="payment_status">
                                <option value="">All Status</option>
                                <option value="paid" <?php echo $payment_status == 'paid' ? 'selected' : ''; ?>>Paid</option>
                                <option value="partial" <?php echo $payment_status == 'partial' ? 'selected' : ''; ?>>Partial</option>
                                <option value="pending" <?php echo $payment_status == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                            <a href="sales.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $summary['total_sales']; ?></h3>
                            <small class="text-muted">Total Sales</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success">₹<?php echo number_format($summary['total_amount'], 2); ?></h3>
                            <small class="text-muted">Total Amount</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">₹<?php echo number_format($summary['total_paid'], 2); ?></h3>
                            <small class="text-muted">Total Paid</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">₹<?php echo number_format($summary['total_outstanding'], 2); ?></h3>
                            <small class="text-muted">Outstanding</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary">₹<?php echo number_format($summary['average_sale'], 2); ?></h3>
                            <small class="text-muted">Average Sale</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-dark"><?php echo $summary['total_outstanding'] > 0 ? round(($summary['total_paid'] / $summary['total_amount']) * 100, 1) : 100; ?>%</h3>
                            <small class="text-muted">Collection Rate</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Data -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Sales Details</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Bill No.</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Amount</th>
                                    <th>Paid</th>
                                    <th>Balance</th>
                                    <th>Status</th>
                                    <th>Sales Person</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($sales)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            No sales found for the selected criteria
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($sales as $sale): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($sale['bill_number']); ?></strong></td>
                                            <td><?php echo date('d/m/Y', strtotime($sale['sale_date'])); ?></td>
                                            <td>
                                                <?php if ($sale['customer_name']): ?>
                                                    <strong><?php echo htmlspecialchars($sale['customer_name']); ?></strong>
                                                    <?php if ($sale['business_name']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($sale['business_name']); ?></small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Walk-in Customer</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><span class="badge bg-info"><?php echo $sale['item_count']; ?></span></td>
                                            <td><strong>₹<?php echo number_format($sale['grand_total'], 2); ?></strong></td>
                                            <td>₹<?php echo number_format($sale['paid_amount'], 2); ?></td>
                                            <td>₹<?php echo number_format($sale['balance_amount'], 2); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                switch ($sale['payment_status']) {
                                                    case 'paid':
                                                        $statusClass = 'bg-success';
                                                        break;
                                                    case 'partial':
                                                        $statusClass = 'bg-warning';
                                                        break;
                                                    case 'pending':
                                                        $statusClass = 'bg-danger';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>">
                                                    <?php echo ucfirst($sale['payment_status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($sale['created_by_name'] ?? 'Unknown'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/app.js"></script>
    
    <script>
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open('export-sales-report.php?' + params.toString(), '_blank');
        }
    </script>
</body>
</html>
