<?php
/**
 * Fix Product Form Response Issue
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🔧 Fixing Product Form Response Issue</h2>";
    
    // Test 1: Check if form submission works
    echo "<h3>📋 Test 1: Form Submission Test</h3>";
    
    if ($_POST && isset($_POST['test_product'])) {
        try {
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Form Submission Working</h4>";
            echo "<p>POST data received successfully:</p>";
            echo "<pre>" . print_r($_POST, true) . "</pre>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Form Submission Error</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST' style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<h4>🧪 Test Form Submission</h4>";
        echo "<input type='hidden' name='test_product' value='1'>";
        echo "<input type='text' name='product_name' placeholder='Test Product Name' required style='margin: 5px; padding: 8px;'>";
        echo "<input type='number' name='tunch_percentage' placeholder='91.6' step='0.01' required style='margin: 5px; padding: 8px;'>";
        echo "<button type='submit' style='margin: 5px; padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 3px;'>Test Submit</button>";
        echo "</form>";
    }
    
    // Test 2: Check database connection and tables
    echo "<h3>🗄️ Test 2: Database Connection</h3>";
    
    try {
        $productCount = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
        $inventoryCount = $db->fetch("SELECT COUNT(*) as count FROM inventory")['count'];
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Database Connection Working</h4>";
        echo "<p><strong>Products:</strong> $productCount records</p>";
        echo "<p><strong>Inventory:</strong> $inventoryCount records</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Database Connection Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Test 3: Check if ProductCodeGenerator exists
    echo "<h3>🏷️ Test 3: Product Code Generator</h3>";
    
    if (file_exists('lib/ProductCodeGenerator.php')) {
        try {
            require_once 'lib/ProductCodeGenerator.php';
            $generator = new ProductCodeGenerator($db);
            $testCode = $generator->generateProductCode("Test Product", null, "Gold");
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Product Code Generator Working</h4>";
            echo "<p>Generated test code: <strong>$testCode</strong></p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Product Code Generator Error</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Product Code Generator Missing</h4>";
        echo "<p>File lib/ProductCodeGenerator.php not found</p>";
        echo "</div>";
    }
    
    // Test 4: Create enhanced product form with better feedback
    echo "<h3>➕ Test 4: Enhanced Product Form</h3>";
    
    if ($_POST && isset($_POST['create_test_product'])) {
        try {
            require_once 'lib/ProductCodeGenerator.php';
            
            $db->beginTransaction();
            
            // Generate product code
            $codeGenerator = new ProductCodeGenerator($db);
            $product_code = $codeGenerator->generateProductCode($_POST['product_name'], null, $_POST['metal_type']);
            
            // Prepare data
            $data = [
                'product_name' => $_POST['product_name'],
                'product_code' => $product_code,
                'description' => $_POST['description'] ?? '',
                'category_id' => null,
                'supplier_id' => null,
                'metal_type' => $_POST['metal_type'],
                'tunch_percentage' => floatval($_POST['tunch_percentage']),
                'gross_weight' => floatval($_POST['gross_weight']),
                'stone_weight' => floatval($_POST['stone_weight']),
                'net_weight' => floatval($_POST['gross_weight']) - floatval($_POST['stone_weight']),
                'making_charges_per_gram' => floatval($_POST['making_charges_per_gram'] ?? 0),
                'wastage_percentage' => floatval($_POST['wastage_percentage'] ?? 2.5),
                'hallmark_charges' => floatval($_POST['hallmark_charges'] ?? 0),
                'va_percentage' => floatval($_POST['va_percentage'] ?? 0),
                'base_weight' => 0,
                'stone_cost' => floatval($_POST['stone_cost'] ?? 0),
                'making_charges' => floatval($_POST['making_charges'] ?? 0),
                'hsn_code' => $_POST['hsn_code'] ?? '',
                'tax_rate' => floatval($_POST['tax_rate'] ?? 3.0),
                'scheme_applicable' => isset($_POST['scheme_applicable']) ? 1 : 0
            ];
            
            // Insert product
            $sql = "INSERT INTO products (product_name, product_code, description, category_id, supplier_id, metal_type, tunch_percentage, gross_weight, stone_weight, net_weight, making_charges_per_gram, wastage_percentage, hallmark_charges, va_percentage, base_weight, stone_cost, making_charges, hsn_code, tax_rate, scheme_applicable) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $db->execute($sql, array_values($data));
            $product_id = $db->lastInsertId();
            
            // Create inventory record
            $db->execute("INSERT INTO inventory (product_id, quantity_in_stock, minimum_stock_level) VALUES (?, 0, 5)", [$product_id]);
            
            $db->commit();
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>🎉 Product Created Successfully!</h4>";
            echo "<p><strong>Product ID:</strong> $product_id</p>";
            echo "<p><strong>Product Code:</strong> $product_code</p>";
            echo "<p><strong>Product Name:</strong> {$_POST['product_name']}</p>";
            echo "<p><strong>Metal Type:</strong> {$_POST['metal_type']}</p>";
            echo "<p><strong>Tunch Percentage:</strong> {$_POST['tunch_percentage']}%</p>";
            echo "<p><strong>Gross Weight:</strong> {$_POST['gross_weight']}g</p>";
            echo "<p><strong>Net Weight:</strong> " . (floatval($_POST['gross_weight']) - floatval($_POST['stone_weight'])) . "g</p>";
            echo "</div>";
            
            echo "<div style='text-align: center; margin: 20px 0;'>";
            echo "<a href='products.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>View All Products</a>";
            echo "<a href='products.php?action=add' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>Add Another Product</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            $db->rollback();
            
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Product Creation Failed</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        // Show enhanced form
        echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>";
        echo "<input type='hidden' name='create_test_product' value='1'>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Product Name *</label>";
        echo "<input type='text' name='product_name' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' placeholder='e.g., Gold Ring Classic'>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Metal Type *</label>";
        echo "<select name='metal_type' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
        echo "<option value=''>Select Metal</option>";
        echo "<option value='Gold'>Gold</option>";
        echo "<option value='Silver'>Silver</option>";
        echo "<option value='Platinum'>Platinum</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Tunch/Purity (%) *</label>";
        echo "<input type='number' name='tunch_percentage' step='0.01' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' placeholder='91.6' value='91.6'>";
        echo "<small style='color: #666;'>Common: 22K Gold=91.6%, 18K Gold=75%, 925 Silver=92.5%</small>";
        echo "</div>";
        
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;'>";
        echo "<div>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Gross Weight (g) *</label>";
        echo "<input type='number' name='gross_weight' step='0.001' required style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' placeholder='10.500'>";
        echo "</div>";
        echo "<div>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Stone Weight (g)</label>";
        echo "<input type='number' name='stone_weight' step='0.001' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' placeholder='1.200' value='0'>";
        echo "</div>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Description</label>";
        echo "<textarea name='description' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;' rows='3' placeholder='Product description'></textarea>";
        echo "</div>";
        
        echo "<div style='text-align: center;'>";
        echo "<button type='submit' style='background-color: #28a745; color: white; padding: 12px 25px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;'>Create Test Product</button>";
        echo "</div>";
        
        echo "</form>";
    }
    
    // Diagnosis Summary
    echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🔍 Diagnosis Summary</h3>";
    echo "<p>If you're experiencing 'no response' after saving products, it could be due to:</p>";
    echo "<ul>";
    echo "<li><strong>JavaScript Errors:</strong> Check browser console for errors</li>";
    echo "<li><strong>Form Validation:</strong> Required fields not filled properly</li>";
    echo "<li><strong>Database Errors:</strong> Connection or query issues</li>";
    echo "<li><strong>Missing Files:</strong> ProductCodeGenerator or other dependencies</li>";
    echo "<li><strong>Session Issues:</strong> Success messages not displaying</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='products.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Go to Products Page</a>";
    echo "<a href='products.php?action=add' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Try Add Product</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Error</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
