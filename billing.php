<?php
/**
 * Billing/New Sale - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$customer_id = $_GET['customer_id'] ?? null;

// Get customers for dropdown
$customers = $db->fetchAll("SELECT id, customer_name, business_name, customer_type FROM customers WHERE is_active = 1 ORDER BY customer_name");

// Get products for selection
$products = $db->fetchAll("
    SELECT p.*, c.category_name, i.quantity_in_stock 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    LEFT JOIN inventory i ON p.id = i.product_id 
    WHERE p.is_active = 1 
    ORDER BY p.product_name
");

// Get current metal rates
$metal_rates = $db->fetchAll("
    SELECT metal_type, purity, rate_per_gram 
    FROM metal_rates 
    WHERE is_active = 1 AND rate_date = CURDATE()
");

$rates_by_metal = [];
foreach ($metal_rates as $rate) {
    $rates_by_metal[$rate['metal_type']][$rate['purity']] = $rate['rate_per_gram'];
}

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'create_sale') {
    try {
        $db->beginTransaction();
        
        // Generate bill number
        $bill_number = generateBillNumber();
        
        // Sale data
        $sale_data = [
            'bill_number' => $bill_number,
            'customer_id' => $_POST['customer_id'] ?: null,
            'sale_date' => date('Y-m-d'),
            'sale_time' => date('H:i:s'),
            'subtotal' => floatval($_POST['subtotal']),
            'discount_type' => $_POST['discount_type'],
            'discount_value' => floatval($_POST['discount_value']),
            'discount_amount' => floatval($_POST['discount_amount']),
            'tax_amount' => floatval($_POST['tax_amount']),
            'grand_total' => floatval($_POST['grand_total']),
            'payment_method' => $_POST['payment_method'],
            'payment_status' => $_POST['payment_status'],
            'notes' => sanitizeInput($_POST['notes']),
            'created_by' => 1 // Current user ID
        ];
        
        // Insert sale
        $sql = "INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, subtotal, discount_type, discount_value, discount_amount, tax_amount, grand_total, payment_method, payment_status, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $db->query($sql, array_values($sale_data));
        $sale_id = $db->lastInsertId();
        
        // Insert sale items
        $items = json_decode($_POST['items'], true);
        foreach ($items as $item) {
            $item_data = [
                'sale_id' => $sale_id,
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'unit_weight' => $item['unit_weight'],
                'total_weight' => $item['total_weight'],
                'stone_cost' => $item['stone_cost'],
                'making_charges' => $item['making_charges'],
                'gold_rate' => $item['gold_rate'],
                'unit_price' => $item['unit_price'],
                'total_price' => $item['total_price'],
                'tax_rate' => $item['tax_rate'],
                'tax_amount' => $item['tax_amount'],
                'line_total' => $item['line_total']
            ];
            
            $sql = "INSERT INTO sale_items (sale_id, product_id, quantity, unit_weight, total_weight, stone_cost, making_charges, gold_rate, unit_price, total_price, tax_rate, tax_amount, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $db->query($sql, array_values($item_data));
            
            // Update inventory
            $db->query("UPDATE inventory SET quantity_in_stock = quantity_in_stock - ? WHERE product_id = ?", [$item['quantity'], $item['product_id']]);
            
            // Record stock movement
            $db->query("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, created_by) VALUES (?, 'out', ?, 'sale', ?, 1)", [$item['product_id'], $item['quantity'], $sale_id]);
        }
        
        $db->commit();
        
        // Redirect to sales page with success message
        header("Location: sales.php?success=Sale created successfully&bill_number=" . urlencode($bill_number));
        exit;
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "Error creating sale: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Sale - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
    
    <style>
        .product-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .product-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
        }
        .product-card.selected {
            border-color: var(--primary);
            background-color: rgba(102, 126, 234, 0.1);
        }
        .sale-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .calculation-panel {
            position: sticky;
            top: 20px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">New Sale</h2>
                <p class="text-muted mb-0">Create a new jewellery sale</p>
            </div>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="clearSale()">
                    <i class="fas fa-trash me-2"></i>Clear All
                </button>
                <a href="sales.php" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>View Sales
                </a>
            </div>
        </div>

        <form id="saleForm" method="POST">
            <input type="hidden" name="action" value="create_sale">
            <input type="hidden" name="items" id="itemsData">
            <input type="hidden" name="subtotal" id="subtotalInput">
            <input type="hidden" name="discount_amount" id="discountAmountInput">
            <input type="hidden" name="tax_amount" id="taxAmountInput">
            <input type="hidden" name="grand_total" id="grandTotalInput">

            <div class="row">
                <div class="col-lg-8">
                    <!-- Customer Selection -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Customer Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="customer_id" class="form-label">Select Customer</label>
                                    <select class="form-select" id="customer_id" name="customer_id">
                                        <option value="">Walk-in Customer</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>" 
                                                    <?php echo $customer_id == $customer['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['customer_name']); ?>
                                                <?php if ($customer['business_name']): ?>
                                                    (<?php echo htmlspecialchars($customer['business_name']); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <a href="customers.php?action=add" class="btn btn-outline-primary">
                                            <i class="fas fa-plus me-2"></i>Add New Customer
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Selection -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Select Products</h5>
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="productSearch" placeholder="Search products...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" id="productGrid">
                                <?php foreach ($products as $product): ?>
                                    <div class="col-md-6 col-lg-4 mb-3 product-item" 
                                         data-name="<?php echo strtolower($product['product_name']); ?>"
                                         data-code="<?php echo strtolower($product['product_code']); ?>">
                                        <div class="card product-card h-100" onclick="addProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                            <div class="card-body">
                                                <h6 class="card-title"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                                <p class="card-text">
                                                    <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small><br>
                                                    <?php if ($product['category_name']): ?>
                                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                                    <?php endif; ?>
                                                    <?php if ($product['metal_type']): ?>
                                                        <span class="badge bg-warning text-dark"><?php echo htmlspecialchars($product['metal_type'] . ' ' . $product['purity']); ?></span>
                                                    <?php endif; ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">Stock: <?php echo $product['quantity_in_stock'] ?? 0; ?></small>
                                                    <?php if ($product['net_weight'] > 0): ?>
                                                        <small class="text-muted"><?php echo formatWeight($product['net_weight']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Items -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Selected Items</h5>
                        </div>
                        <div class="card-body">
                            <div id="selectedItems">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-shopping-cart fa-3x mb-3 opacity-25"></i>
                                    <p>No items selected. Click on products above to add them to the sale.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Calculation Panel -->
                    <div class="card calculation-panel">
                        <div class="card-header">
                            <h5 class="mb-0">Sale Summary</h5>
                        </div>
                        <div class="card-body">
                            <!-- Totals -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Subtotal:</span>
                                    <strong id="subtotalDisplay">₹0.00</strong>
                                </div>
                            </div>

                            <!-- Discount -->
                            <div class="mb-3">
                                <label class="form-label">Discount</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="discount_type" id="discountType" style="max-width: 100px;">
                                        <option value="amount">₹</option>
                                        <option value="percentage">%</option>
                                    </select>
                                    <input type="number" class="form-control" name="discount_value" id="discountValue" 
                                           value="0" step="0.01" min="0" onchange="calculateTotals()">
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Discount Amount:</span>
                                    <span id="discountDisplay">₹0.00</span>
                                </div>
                            </div>

                            <!-- Tax -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Tax (GST):</span>
                                    <span id="taxDisplay">₹0.00</span>
                                </div>
                            </div>

                            <hr>

                            <!-- Grand Total -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between">
                                    <h5>Grand Total:</h5>
                                    <h5 class="text-primary" id="grandTotalDisplay">₹0.00</h5>
                                </div>
                            </div>

                            <!-- Payment Details -->
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" name="payment_method" id="payment_method" required>
                                    <option value="cash">Cash</option>
                                    <option value="card">Card</option>
                                    <option value="upi">UPI</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                    <option value="cheque">Cheque</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="payment_status" class="form-label">Payment Status</label>
                                <select class="form-select" name="payment_status" id="payment_status" required>
                                    <option value="paid">Paid</option>
                                    <option value="partial">Partial</option>
                                    <option value="pending">Pending</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" name="notes" id="notes" rows="3" placeholder="Additional notes..."></textarea>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="createSaleBtn" disabled>
                                    <i class="fas fa-save me-2"></i>Create Sale
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewBill()">
                                    <i class="fas fa-eye me-2"></i>Preview Bill
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        let selectedItems = [];
        let metalRates = <?php echo json_encode($rates_by_metal); ?>;
        
        // Product search functionality
        document.getElementById('productSearch').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const productItems = document.querySelectorAll('.product-item');
            
            productItems.forEach(item => {
                const name = item.dataset.name;
                const code = item.dataset.code;
                
                if (name.includes(searchTerm) || code.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
        
        // Add product to sale
        function addProduct(product) {
            // Check if product already exists
            const existingIndex = selectedItems.findIndex(item => item.product_id === product.id);
            
            if (existingIndex >= 0) {
                // Increase quantity
                selectedItems[existingIndex].quantity++;
            } else {
                // Add new item
                const item = {
                    product_id: product.id,
                    product_name: product.product_name,
                    product_code: product.product_code,
                    quantity: 1,
                    unit_weight: parseFloat(product.net_weight) || 0,
                    total_weight: parseFloat(product.net_weight) || 0,
                    stone_cost: parseFloat(product.stone_cost) || 0,
                    making_charges: parseFloat(product.making_charges) || 0,
                    gold_rate: getMetalRate(product.metal_type, product.purity),
                    unit_price: 0,
                    total_price: 0,
                    tax_rate: parseFloat(product.tax_rate) || 3,
                    tax_amount: 0,
                    line_total: 0
                };
                
                selectedItems.push(item);
            }
            
            updateSelectedItems();
            calculateTotals();
        }
        
        // Get metal rate
        function getMetalRate(metalType, purity) {
            if (metalRates[metalType] && metalRates[metalType][purity]) {
                return parseFloat(metalRates[metalType][purity]);
            }
            return 0;
        }
        
        // Update selected items display
        function updateSelectedItems() {
            const container = document.getElementById('selectedItems');
            
            if (selectedItems.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3 opacity-25"></i>
                        <p>No items selected. Click on products above to add them to the sale.</p>
                    </div>
                `;
                document.getElementById('createSaleBtn').disabled = true;
                return;
            }
            
            let html = '';
            selectedItems.forEach((item, index) => {
                // Calculate item totals
                const goldValue = item.unit_weight * item.gold_rate;
                item.unit_price = goldValue + item.stone_cost + item.making_charges;
                item.total_price = item.unit_price * item.quantity;
                item.total_weight = item.unit_weight * item.quantity;
                item.tax_amount = (item.total_price * item.tax_rate) / 100;
                item.line_total = item.total_price + item.tax_amount;
                
                html += `
                    <div class="sale-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${item.product_name}</h6>
                                <small class="text-muted">${item.product_code}</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        
                        <div class="row g-2">
                            <div class="col-md-3">
                                <label class="form-label small">Quantity</label>
                                <input type="number" class="form-control form-control-sm" value="${item.quantity}" 
                                       min="1" onchange="updateItemQuantity(${index}, this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">Weight (gm)</label>
                                <input type="number" class="form-control form-control-sm" value="${item.unit_weight}" 
                                       step="0.001" onchange="updateItemWeight(${index}, this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">Gold Rate</label>
                                <input type="number" class="form-control form-control-sm" value="${item.gold_rate}" 
                                       step="0.01" onchange="updateItemRate(${index}, this.value)">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">Making Charges</label>
                                <input type="number" class="form-control form-control-sm" value="${item.making_charges}" 
                                       step="0.01" onchange="updateItemMaking(${index}, this.value)">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <small class="text-muted">
                                Total Weight: ${item.total_weight.toFixed(3)}gm | 
                                Tax: ₹${item.tax_amount.toFixed(2)}
                            </small>
                            <strong class="text-primary">₹${item.line_total.toFixed(2)}</strong>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            document.getElementById('createSaleBtn').disabled = false;
        }
        
        // Update item functions
        function updateItemQuantity(index, quantity) {
            selectedItems[index].quantity = parseInt(quantity) || 1;
            updateSelectedItems();
            calculateTotals();
        }
        
        function updateItemWeight(index, weight) {
            selectedItems[index].unit_weight = parseFloat(weight) || 0;
            updateSelectedItems();
            calculateTotals();
        }
        
        function updateItemRate(index, rate) {
            selectedItems[index].gold_rate = parseFloat(rate) || 0;
            updateSelectedItems();
            calculateTotals();
        }
        
        function updateItemMaking(index, making) {
            selectedItems[index].making_charges = parseFloat(making) || 0;
            updateSelectedItems();
            calculateTotals();
        }
        
        function removeItem(index) {
            selectedItems.splice(index, 1);
            updateSelectedItems();
            calculateTotals();
        }
        
        // Calculate totals
        function calculateTotals() {
            let subtotal = 0;
            let totalTax = 0;
            
            selectedItems.forEach(item => {
                subtotal += item.total_price;
                totalTax += item.tax_amount;
            });
            
            // Calculate discount
            const discountType = document.getElementById('discountType').value;
            const discountValue = parseFloat(document.getElementById('discountValue').value) || 0;
            let discountAmount = 0;
            
            if (discountType === 'percentage') {
                discountAmount = (subtotal * discountValue) / 100;
            } else {
                discountAmount = discountValue;
            }
            
            const grandTotal = subtotal - discountAmount + totalTax;
            
            // Update displays
            document.getElementById('subtotalDisplay').textContent = `₹${subtotal.toFixed(2)}`;
            document.getElementById('discountDisplay').textContent = `₹${discountAmount.toFixed(2)}`;
            document.getElementById('taxDisplay').textContent = `₹${totalTax.toFixed(2)}`;
            document.getElementById('grandTotalDisplay').textContent = `₹${grandTotal.toFixed(2)}`;
            
            // Update hidden inputs
            document.getElementById('subtotalInput').value = subtotal.toFixed(2);
            document.getElementById('discountAmountInput').value = discountAmount.toFixed(2);
            document.getElementById('taxAmountInput').value = totalTax.toFixed(2);
            document.getElementById('grandTotalInput').value = grandTotal.toFixed(2);
            document.getElementById('itemsData').value = JSON.stringify(selectedItems);
        }
        
        function clearSale() {
            if (confirm('Are you sure you want to clear all items?')) {
                selectedItems = [];
                updateSelectedItems();
                calculateTotals();
            }
        }
        
        function previewBill() {
            if (selectedItems.length === 0) {
                alert('Please add items to the sale first.');
                return;
            }
            
            // Open preview in new window
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(generateBillPreview());
        }
        
        function generateBillPreview() {
            const customerSelect = document.getElementById('customer_id');
            const customerName = customerSelect.options[customerSelect.selectedIndex].text;
            
            let itemsHtml = '';
            selectedItems.forEach((item, index) => {
                itemsHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.product_name}<br><small>${item.product_code}</small></td>
                        <td>${item.quantity}</td>
                        <td>${item.total_weight.toFixed(3)}gm</td>
                        <td>₹${item.unit_price.toFixed(2)}</td>
                        <td>₹${item.line_total.toFixed(2)}</td>
                    </tr>
                `;
            });
            
            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Bill Preview</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                </head>
                <body class="p-4">
                    <div class="text-center mb-4">
                        <h2>${'<?php echo APP_NAME; ?>'}</h2>
                        <p>Bill Preview</p>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Customer:</strong> ${customerName}<br>
                        <strong>Date:</strong> ${new Date().toLocaleDateString()}
                    </div>
                    
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Product</th>
                                <th>Qty</th>
                                <th>Weight</th>
                                <th>Rate</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHtml}
                        </tbody>
                    </table>
                    
                    <div class="text-end">
                        <p><strong>Subtotal: ${document.getElementById('subtotalDisplay').textContent}</strong></p>
                        <p><strong>Discount: ${document.getElementById('discountDisplay').textContent}</strong></p>
                        <p><strong>Tax: ${document.getElementById('taxDisplay').textContent}</strong></p>
                        <h4><strong>Grand Total: ${document.getElementById('grandTotalDisplay').textContent}</strong></h4>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button onclick="window.print()" class="btn btn-primary">Print</button>
                        <button onclick="window.close()" class="btn btn-secondary">Close</button>
                    </div>
                </body>
                </html>
            `;
        }
        
        // Initialize discount calculation
        document.getElementById('discountType').addEventListener('change', calculateTotals);
        document.getElementById('discountValue').addEventListener('input', calculateTotals);
    </script>
</body>
</html>
