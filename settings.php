<?php
/**
 * Settings - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Handle form submissions
if ($_POST) {
    try {
        $settings = $_POST['settings'] ?? [];
        
        foreach ($settings as $key => $value) {
            $value = sanitizeInput($value);
            
            // Check if setting exists
            $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
            
            if ($existing) {
                $db->query("UPDATE settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            } else {
                $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)", [$key, $value]);
            }
        }
        
        $success = "Settings updated successfully!";
    } catch (Exception $e) {
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get all settings
$settings_data = $db->fetchAll("SELECT * FROM settings ORDER BY category, setting_key");

// Organize settings by category
$settings_by_category = [];
foreach ($settings_data as $setting) {
    $settings_by_category[$setting['category']][] = $setting;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">System Settings</h2>
                <p class="text-muted mb-0">Configure your jewellery management system</p>
            </div>
            <div class="d-flex gap-2">
                <div class="btn-group">
                    <button class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="export-settings.php?format=json"><i class="fas fa-file-code me-2"></i>JSON</a></li>
                        <li><a class="dropdown-item" href="export-settings.php?format=csv"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                        <li><a class="dropdown-item" href="export-settings.php?format=backup"><i class="fas fa-archive me-2"></i>Full Backup</a></li>
                    </ul>
                </div>
                <button type="button" class="btn btn-outline-info" onclick="showImportSettings()">
                    <i class="fas fa-upload me-2"></i>Import
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="showSystemDiagnostics()">
                    <i class="fas fa-stethoscope me-2"></i>Diagnostics
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>Reset to Defaults
                </button>
            </div>
        </div>

        <form method="POST">
            <div class="row">
                <div class="col-lg-8">
                    <?php foreach ($settings_by_category as $category => $settings): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-<?php echo getCategoryIcon($category); ?> me-2"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', $category)); ?> Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($settings as $setting): ?>
                                        <div class="col-md-6 mb-3">
                                            <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                                <?php echo ucfirst(str_replace('_', ' ', str_replace($category . '_', '', $setting['setting_key']))); ?>
                                            </label>
                                            
                                            <?php if ($setting['setting_type'] === 'boolean'): ?>
                                                <select class="form-select" name="settings[<?php echo $setting['setting_key']; ?>]" id="<?php echo $setting['setting_key']; ?>">
                                                    <option value="1" <?php echo $setting['setting_value'] == '1' ? 'selected' : ''; ?>>Yes</option>
                                                    <option value="0" <?php echo $setting['setting_value'] == '0' ? 'selected' : ''; ?>>No</option>
                                                </select>
                                            <?php elseif ($setting['setting_type'] === 'number'): ?>
                                                <input type="number" class="form-control" 
                                                       name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                       id="<?php echo $setting['setting_key']; ?>"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       step="0.01">
                                            <?php else: ?>
                                                <input type="text" class="form-control" 
                                                       name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                       id="<?php echo $setting['setting_key']; ?>"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                            <?php endif; ?>
                                            
                                            <?php if ($setting['description']): ?>
                                                <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- System Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Application:</strong></td>
                                    <td><?php echo APP_NAME; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Version:</strong></td>
                                    <td><?php echo APP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>PHP Version:</strong></td>
                                    <td><?php echo PHP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Database:</strong></td>
                                    <td>MySQL <?php echo $db->getAttribute(PDO::ATTR_SERVER_VERSION); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Timezone:</strong></td>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Server Time:</strong></td>
                                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="notification-settings.php" class="btn btn-outline-info">
                                    <i class="fas fa-bell me-2"></i>Notification Settings
                                </a>
                                <button type="button" class="btn btn-outline-primary" onclick="clearCache()">
                                    <i class="fas fa-broom me-2"></i>Clear Cache
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="testEmail()">
                                    <i class="fas fa-envelope me-2"></i>Test Email
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="testSMS()">
                                    <i class="fas fa-sms me-2"></i>Test SMS
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                    <i class="fas fa-database me-2"></i>Optimize Database
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="showMaintenanceMode()">
                                    <i class="fas fa-tools me-2"></i>Maintenance Mode
                                </button>
                                <a href="backup.php" class="btn btn-outline-success">
                                    <i class="fas fa-download me-2"></i>Backup System
                                </a>
                                <button type="button" class="btn btn-outline-secondary" onclick="showSystemLogs()">
                                    <i class="fas fa-file-alt me-2"></i>View Logs
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Security</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt me-2"></i>Security Status</h6>
                                <ul class="mb-0 small">
                                    <li>✅ Password encryption enabled</li>
                                    <li>✅ SQL injection protection active</li>
                                    <li>✅ Session security configured</li>
                                    <li>⚠️ SSL certificate recommended</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-danger" onclick="changeAdminPassword()">
                                    <i class="fas fa-key me-2"></i>Change Admin Password
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance Mode -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Maintenance</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                <label class="form-check-label" for="maintenanceMode">
                                    Maintenance Mode
                                </label>
                            </div>
                            <div class="form-text">Enable to prevent user access during updates</div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </main>
    </div>
    </div>

    <!-- Import Settings Modal -->
    <div class="modal fade" id="importSettingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Import Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Import Method</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="importMethod" id="importFile" value="file" checked>
                            <label class="form-check-label" for="importFile">Upload Configuration File</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="importMethod" id="importBackup" value="backup">
                            <label class="form-check-label" for="importBackup">Restore from Backup</label>
                        </div>
                    </div>

                    <div id="fileImportSection">
                        <div class="mb-3">
                            <label for="settingsFile" class="form-label">Select Configuration File</label>
                            <input type="file" class="form-control" id="settingsFile" accept=".json,.csv">
                            <small class="text-muted">Supported formats: JSON, CSV</small>
                        </div>
                    </div>

                    <div id="backupImportSection" style="display: none;">
                        <div class="mb-3">
                            <label for="backupFile" class="form-label">Select Backup File</label>
                            <input type="file" class="form-control" id="backupFile" accept=".zip,.sql">
                            <small class="text-muted">Supported formats: ZIP, SQL</small>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This will overwrite all current settings and data.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processImport()">Import</button>
                </div>
            </div>
        </div>
    </div>

    <!-- System Diagnostics Modal -->
    <div class="modal fade" id="systemDiagnosticsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">System Diagnostics</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="diagnosticsContent">
                        <!-- Dynamic content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportDiagnostics()">Export Report</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Mode Modal -->
    <div class="modal fade" id="maintenanceModeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Maintenance Mode</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Maintenance Status</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="maintenanceToggle">
                            <label class="form-check-label" for="maintenanceToggle">
                                Enable Maintenance Mode
                            </label>
                        </div>
                        <small class="text-muted">When enabled, only administrators can access the system</small>
                    </div>

                    <div class="mb-3">
                        <label for="maintenanceMessage" class="form-label">Maintenance Message</label>
                        <textarea class="form-control" id="maintenanceMessage" rows="3"
                                  placeholder="System is under maintenance. Please try again later."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="maintenanceEndTime" class="form-label">Expected End Time (Optional)</label>
                        <input type="datetime-local" class="form-control" id="maintenanceEndTime">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="toggleMaintenanceMode()">Apply</button>
                </div>
            </div>
        </div>
    </div>

    <!-- System Logs Modal -->
    <div class="modal fade" id="systemLogsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">System Logs</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="logType">
                                <option value="all">All Logs</option>
                                <option value="error">Error Logs</option>
                                <option value="access">Access Logs</option>
                                <option value="system">System Logs</option>
                                <option value="security">Security Logs</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="date" class="form-control" id="logDate" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="loadLogs()">
                                <i class="fas fa-search me-2"></i>Load Logs
                            </button>
                        </div>
                    </div>
                    <div id="logsContent" style="max-height: 400px; overflow-y: auto;">
                        <!-- Dynamic log content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">Clear Logs</button>
                    <button type="button" class="btn btn-primary" onclick="downloadLogs()">Download</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
                // Implementation would reset settings to defaults
                jewelleryApp.showNotification('Settings reset to defaults', 'success');
            }
        }
        
        function clearCache() {
            // Implementation would clear application cache
            jewelleryApp.showNotification('Cache cleared successfully', 'success');
        }
        
        function testEmail() {
            // Implementation would send a test email
            jewelleryApp.showNotification('Test email sent successfully', 'success');
        }
        
        function optimizeDatabase() {
            if (confirm('Optimize database tables? This may take a few minutes.')) {
                // Show progress
                const progressHtml = `
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>Optimizing database...
                        <div class="progress mt-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                        </div>
                    </div>
                `;

                // Implementation would optimize database
                fetch('ajax/optimize-database.php', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            jewelleryApp.showNotification('Database optimization completed', 'success');
                        } else {
                            jewelleryApp.showNotification('Database optimization failed: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        jewelleryApp.showNotification('Database optimization failed', 'error');
                    });
            }
        }

        function changeAdminPassword() {
            const newPassword = prompt('Enter new admin password:');
            if (newPassword && newPassword.length >= 6) {
                // Implementation would change admin password
                jewelleryApp.showNotification('Admin password changed successfully', 'success');
            } else if (newPassword) {
                jewelleryApp.showNotification('Password must be at least 6 characters long', 'warning');
            }
        }

        // Enhanced settings functions
        function showImportSettings() {
            const modal = new bootstrap.Modal(document.getElementById('importSettingsModal'));
            modal.show();

            // Setup import method listeners
            document.querySelectorAll('input[name="importMethod"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const fileSection = document.getElementById('fileImportSection');
                    const backupSection = document.getElementById('backupImportSection');

                    if (this.value === 'file') {
                        fileSection.style.display = 'block';
                        backupSection.style.display = 'none';
                    } else {
                        fileSection.style.display = 'none';
                        backupSection.style.display = 'block';
                    }
                });
            });
        }

        function processImport() {
            const importMethod = document.querySelector('input[name="importMethod"]:checked').value;

            if (importMethod === 'file') {
                const fileInput = document.getElementById('settingsFile');
                if (!fileInput.files[0]) {
                    alert('Please select a configuration file');
                    return;
                }

                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('action', 'import_settings');

                fetch('process-settings-import.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Settings imported successfully');
                        location.reload();
                    } else {
                        alert('Import failed: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Import error: ' + error.message);
                });

            } else if (importMethod === 'backup') {
                const fileInput = document.getElementById('backupFile');
                if (!fileInput.files[0]) {
                    alert('Please select a backup file');
                    return;
                }

                if (confirm('This will overwrite all current data. Are you sure?')) {
                    // Process backup restore
                    const formData = new FormData();
                    formData.append('file', fileInput.files[0]);
                    formData.append('action', 'restore_backup');

                    fetch('process-backup-restore.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Backup restored successfully');
                            location.reload();
                        } else {
                            alert('Restore failed: ' + data.error);
                        }
                    })
                    .catch(error => {
                        alert('Restore error: ' + error.message);
                    });
                }
            }
        }

        function showSystemDiagnostics() {
            const modal = new bootstrap.Modal(document.getElementById('systemDiagnosticsModal'));
            modal.show();

            // Load diagnostics
            document.getElementById('diagnosticsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Running diagnostics...</div>';

            fetch('ajax/system-diagnostics.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('diagnosticsContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('diagnosticsContent').innerHTML = '<div class="alert alert-danger">Error loading diagnostics</div>';
                });
        }

        function showMaintenanceMode() {
            const modal = new bootstrap.Modal(document.getElementById('maintenanceModeModal'));
            modal.show();

            // Load current maintenance status
            fetch('ajax/maintenance-status.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('maintenanceToggle').checked = data.enabled;
                    document.getElementById('maintenanceMessage').value = data.message || '';
                    document.getElementById('maintenanceEndTime').value = data.endTime || '';
                })
                .catch(error => {
                    console.error('Error loading maintenance status:', error);
                });
        }

        function toggleMaintenanceMode() {
            const enabled = document.getElementById('maintenanceToggle').checked;
            const message = document.getElementById('maintenanceMessage').value;
            const endTime = document.getElementById('maintenanceEndTime').value;

            fetch('ajax/toggle-maintenance.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    enabled: enabled,
                    message: message,
                    endTime: endTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(enabled ? 'Maintenance mode enabled' : 'Maintenance mode disabled');
                    bootstrap.Modal.getInstance(document.getElementById('maintenanceModeModal')).hide();
                } else {
                    alert('Failed to toggle maintenance mode: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function showSystemLogs() {
            const modal = new bootstrap.Modal(document.getElementById('systemLogsModal'));
            modal.show();
            loadLogs();
        }

        function loadLogs() {
            const logType = document.getElementById('logType').value;
            const logDate = document.getElementById('logDate').value;

            document.getElementById('logsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading logs...</div>';

            fetch(`ajax/system-logs.php?type=${logType}&date=${logDate}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('logsContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('logsContent').innerHTML = '<div class="alert alert-danger">Error loading logs</div>';
                });
        }

        function testSMS() {
            const phoneNumber = prompt('Enter phone number to test SMS:');
            if (phoneNumber) {
                fetch('ajax/test-sms.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phoneNumber })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        jewelleryApp.showNotification('Test SMS sent successfully', 'success');
                    } else {
                        jewelleryApp.showNotification('SMS test failed: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    jewelleryApp.showNotification('SMS test failed', 'error');
                });
            }
        }

        function exportDiagnostics() {
            window.open('export-diagnostics.php', '_blank');
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
                const logType = document.getElementById('logType').value;

                fetch('ajax/clear-logs.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ type: logType })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Logs cleared successfully');
                        loadLogs();
                    } else {
                        alert('Failed to clear logs: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error clearing logs: ' + error.message);
                });
            }
        }

        function downloadLogs() {
            const logType = document.getElementById('logType').value;
            const logDate = document.getElementById('logDate').value;
            window.open(`download-logs.php?type=${logType}&date=${logDate}`, '_blank');
        }
        
        // Auto-save settings on change
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.addEventListener('change', function() {
                // Visual feedback for changed settings
                this.classList.add('border-warning');
                setTimeout(() => {
                    this.classList.remove('border-warning');
                }, 2000);
            });
        });
    </script>
</body>
</html>

<?php
function getCategoryIcon($category) {
    $icons = [
        'company' => 'building',
        'rates' => 'coins',
        'tax' => 'percentage',
        'inventory' => 'boxes',
        'billing' => 'file-invoice',
        'general' => 'cog'
    ];
    
    return $icons[$category] ?? 'cog';
}
?>
