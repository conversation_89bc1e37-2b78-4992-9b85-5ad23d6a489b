<?php
/**
 * Settings - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Handle form submissions
if ($_POST) {
    try {
        $settings = $_POST['settings'] ?? [];
        
        foreach ($settings as $key => $value) {
            $value = sanitizeInput($value);
            
            // Check if setting exists
            $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
            
            if ($existing) {
                $db->query("UPDATE settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            } else {
                $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)", [$key, $value]);
            }
        }
        
        $success = "Settings updated successfully!";
    } catch (Exception $e) {
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get all settings
$settings_data = $db->fetchAll("SELECT * FROM settings ORDER BY category, setting_key");

// Organize settings by category
$settings_by_category = [];
foreach ($settings_data as $setting) {
    $settings_by_category[$setting['category']][] = $setting;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">System Settings</h2>
                <p class="text-muted mb-0">Configure your jewellery management system</p>
            </div>
            <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                <i class="fas fa-undo me-2"></i>Reset to Defaults
            </button>
        </div>

        <form method="POST">
            <div class="row">
                <div class="col-lg-8">
                    <?php foreach ($settings_by_category as $category => $settings): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-<?php echo getCategoryIcon($category); ?> me-2"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', $category)); ?> Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($settings as $setting): ?>
                                        <div class="col-md-6 mb-3">
                                            <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                                <?php echo ucfirst(str_replace('_', ' ', str_replace($category . '_', '', $setting['setting_key']))); ?>
                                            </label>
                                            
                                            <?php if ($setting['setting_type'] === 'boolean'): ?>
                                                <select class="form-select" name="settings[<?php echo $setting['setting_key']; ?>]" id="<?php echo $setting['setting_key']; ?>">
                                                    <option value="1" <?php echo $setting['setting_value'] == '1' ? 'selected' : ''; ?>>Yes</option>
                                                    <option value="0" <?php echo $setting['setting_value'] == '0' ? 'selected' : ''; ?>>No</option>
                                                </select>
                                            <?php elseif ($setting['setting_type'] === 'number'): ?>
                                                <input type="number" class="form-control" 
                                                       name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                       id="<?php echo $setting['setting_key']; ?>"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       step="0.01">
                                            <?php else: ?>
                                                <input type="text" class="form-control" 
                                                       name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                       id="<?php echo $setting['setting_key']; ?>"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                            <?php endif; ?>
                                            
                                            <?php if ($setting['description']): ?>
                                                <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- System Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Application:</strong></td>
                                    <td><?php echo APP_NAME; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Version:</strong></td>
                                    <td><?php echo APP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>PHP Version:</strong></td>
                                    <td><?php echo PHP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Database:</strong></td>
                                    <td>MySQL <?php echo $db->getAttribute(PDO::ATTR_SERVER_VERSION); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Timezone:</strong></td>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Server Time:</strong></td>
                                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary" onclick="clearCache()">
                                    <i class="fas fa-broom me-2"></i>Clear Cache
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="testEmail()">
                                    <i class="fas fa-envelope me-2"></i>Test Email
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                    <i class="fas fa-database me-2"></i>Optimize Database
                                </button>
                                <a href="backup.php" class="btn btn-outline-success">
                                    <i class="fas fa-download me-2"></i>Backup System
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Security</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt me-2"></i>Security Status</h6>
                                <ul class="mb-0 small">
                                    <li>✅ Password encryption enabled</li>
                                    <li>✅ SQL injection protection active</li>
                                    <li>✅ Session security configured</li>
                                    <li>⚠️ SSL certificate recommended</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-danger" onclick="changeAdminPassword()">
                                    <i class="fas fa-key me-2"></i>Change Admin Password
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance Mode -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Maintenance</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                <label class="form-check-label" for="maintenanceMode">
                                    Maintenance Mode
                                </label>
                            </div>
                            <div class="form-text">Enable to prevent user access during updates</div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
                // Implementation would reset settings to defaults
                jewelleryApp.showNotification('Settings reset to defaults', 'success');
            }
        }
        
        function clearCache() {
            // Implementation would clear application cache
            jewelleryApp.showNotification('Cache cleared successfully', 'success');
        }
        
        function testEmail() {
            // Implementation would send a test email
            jewelleryApp.showNotification('Test email sent successfully', 'success');
        }
        
        function optimizeDatabase() {
            if (confirm('Optimize database tables? This may take a few minutes.')) {
                // Implementation would optimize database
                jewelleryApp.showNotification('Database optimization started', 'info');
            }
        }
        
        function changeAdminPassword() {
            const newPassword = prompt('Enter new admin password:');
            if (newPassword && newPassword.length >= 6) {
                // Implementation would change admin password
                jewelleryApp.showNotification('Admin password changed successfully', 'success');
            } else if (newPassword) {
                jewelleryApp.showNotification('Password must be at least 6 characters long', 'warning');
            }
        }
        
        // Auto-save settings on change
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.addEventListener('change', function() {
                // Visual feedback for changed settings
                this.classList.add('border-warning');
                setTimeout(() => {
                    this.classList.remove('border-warning');
                }, 2000);
            });
        });
    </script>
</body>
</html>

<?php
function getCategoryIcon($category) {
    $icons = [
        'company' => 'building',
        'rates' => 'coins',
        'tax' => 'percentage',
        'inventory' => 'boxes',
        'billing' => 'file-invoice',
        'general' => 'cog'
    ];
    
    return $icons[$category] ?? 'cog';
}
?>
