<?php
/**
 * PRODUCTION-READY JEWELRY MANAGEMENT SYSTEM
 * Complete Business Solution with Correct Formula Implementation
 * 
 * Formula: Metal Value = Tunch Weight × Base Rate (Direct Multiplication)
 * Matches your handwritten calculation: 10.120g × 96% = 9.7159g × ₹10,112 = ₹98,240.10
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Security Headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

class ProductionJewelrySystem {
    private $pdo;
    private $currentUser;
    
    public function __construct() {
        $this->initDatabase();
        $this->checkAuthentication();
    }
    
    private function initDatabase() {
        try {
            // Production database setup
            $this->pdo = new PDO('sqlite:jewelry_production.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createProductionTables();
        } catch(PDOException $e) {
            die("Database Error: " . $e->getMessage());
        }
    }
    
    private function createProductionTables() {
        $tables = [
            // Users table
            'users' => "CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'staff',
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            
            // Enhanced Suppliers
            'suppliers' => "CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                gst_number TEXT,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            
            // Production Inventory
            'inventory' => "CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER,
                product_code TEXT UNIQUE,
                product_name TEXT NOT NULL,
                category TEXT DEFAULT 'other',
                description TEXT,
                
                -- Weight Information (as per your spreadsheet)
                current_wt REAL NOT NULL DEFAULT 0,
                without_stone_wt REAL NOT NULL DEFAULT 0,
                
                -- Cost Information
                with_stone_cost REAL NOT NULL DEFAULT 0,
                without_stone_cost REAL NOT NULL DEFAULT 0,
                
                -- 24K Information
                procured_in_24k REAL NOT NULL DEFAULT 0,
                with_stone_24k REAL NOT NULL DEFAULT 0,
                without_stone_24k REAL NOT NULL DEFAULT 0,
                weight_in_24k REAL NOT NULL DEFAULT 0,
                
                -- Pricing
                unit_price REAL NOT NULL DEFAULT 0,
                stone_price REAL NOT NULL DEFAULT 0,
                total_value REAL NOT NULL DEFAULT 0,
                
                -- Stock Management
                sold_value REAL NOT NULL DEFAULT 0,
                balance_in_stock REAL NOT NULL DEFAULT 0,
                min_stock_level REAL DEFAULT 0,
                
                -- Status
                status TEXT DEFAULT 'in_stock',
                location TEXT,
                
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )",
            
            // Customers
            'customers' => "CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                customer_type TEXT DEFAULT 'regular',
                total_purchases REAL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            
            // Production Billing with CORRECT formula
            'billing' => "CREATE TABLE IF NOT EXISTS billing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE,
                customer_id INTEGER,
                customer_name TEXT NOT NULL,
                location TEXT NOT NULL,
                
                -- Product Information
                product_name TEXT NOT NULL,
                inventory_id INTEGER,
                
                -- Weight Calculations
                gross_wt REAL NOT NULL,
                stone_wt REAL NOT NULL,
                net_wt REAL NOT NULL,
                
                -- CORRECT Formula Implementation
                tunch_percentage REAL NOT NULL,
                base_rate REAL NOT NULL,
                tunch_weight REAL NOT NULL,
                metal_value REAL NOT NULL, -- This is Tunch Weight × Base Rate
                
                -- Additional Charges
                making_charges REAL NOT NULL DEFAULT 0,
                stone_charges REAL NOT NULL DEFAULT 0,
                
                -- Final Calculation
                subtotal REAL NOT NULL, -- metal_value + making + stone
                discount_percentage REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_percentage REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_value REAL NOT NULL,
                
                -- Payment Information
                payment_status TEXT DEFAULT 'pending',
                payment_method TEXT,
                paid_amount REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                
                -- Audit
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (inventory_id) REFERENCES inventory (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )",
            
            // Stock Movements
            'stock_movements' => "CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                inventory_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                quantity REAL NOT NULL,
                reference_type TEXT, -- 'purchase', 'sale', 'adjustment'
                reference_id INTEGER,
                notes TEXT,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (inventory_id) REFERENCES inventory (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )"
        ];
        
        foreach ($tables as $name => $sql) {
            $this->pdo->exec($sql);
        }
        
        // Create default admin user
        $this->createDefaultAdmin();
        
        // Create sample data if empty
        $this->createSampleData();
    }
    
    private function createDefaultAdmin() {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("INSERT INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', $passwordHash, 'System Administrator', 'admin']);
        }
    }
    
    private function createSampleData() {
        // Check if we have suppliers
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM suppliers");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            // Add sample suppliers
            $suppliers = [
                ['Emerald Jewel Industry', 'Rajesh Kumar', '9876543210', '<EMAIL>', 'Mumbai, Maharashtra'],
                ['Nala Gold', 'Priya Sharma', '9876543211', '<EMAIL>', 'Delhi'],
                ['Surat Jewellers', 'Amit Patel', '9876543212', '<EMAIL>', 'Surat, Gujarat']
            ];
            
            foreach ($suppliers as $supplier) {
                $stmt = $this->pdo->prepare("INSERT INTO suppliers (name, contact_person, phone, email, address) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute($supplier);
            }
            
            // Add sample inventory items (matching your spreadsheet structure)
            $inventory = [
                [1, 'CH001', 'Coimbatore Chain', 'chain', 'Gold chain from Coimbatore', 10.120, 89.120, 129.120, 245.120, 111.195, 0.000, 10.160, 9.754, 110.260, 248.220, 0, 2.910, 3.388, 395.875, 10.120],
                [2, 'BG001', 'Bangles', 'bangles', 'Traditional gold bangles', 32.120, 0, 0, 0, 236.480, 0.000, 12.140, 31.478, 0, 0, 0, 0, 0, 0, 32.120],
                [3, 'ST001', 'Studs', 'earrings', 'Gold ear studs', 1.560, 2.010, 2.570, 0.560, 192.938, 1.500, 2.910, 3.388, 0, 0, 0, 1600, 0, 0, 1.560]
            ];
            
            foreach ($inventory as $item) {
                $stmt = $this->pdo->prepare("INSERT INTO inventory (
                    supplier_id, product_code, product_name, category, description,
                    current_wt, without_stone_wt, with_stone_cost, without_stone_cost,
                    procured_in_24k, sold_value, balance_in_stock, with_stone_24k,
                    without_stone_24k, weight_in_24k, unit_price, stone_price,
                    total_value, min_stock_level
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute($item);
            }
        }
    }
    
    private function checkAuthentication() {
        if (!isset($_SESSION['user_id']) && !$this->isLoginPage()) {
            header('Location: ?page=login');
            exit;
        }
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$_SESSION['user_id']]);
            $this->currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
    
    private function isLoginPage() {
        return isset($_GET['page']) && $_GET['page'] === 'login';
    }
    
    public function handleLogin() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ? AND is_active = 1");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                // Update last login
                $stmt = $this->pdo->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                header('Location: ?page=dashboard');
                exit;
            } else {
                return "Invalid username or password";
            }
        }
        return null;
    }
    
    public function handleLogout() {
        session_destroy();
        header('Location: ?page=login');
        exit;
    }

    // CORRECT BILLING CALCULATION (Production Implementation)
    public function calculateBilling($data) {
        $gross_wt = floatval($data['gross_wt']);
        $stone_wt = floatval($data['stone_wt']);
        $net_wt = $gross_wt - $stone_wt;
        $tunch_percentage = floatval($data['tunch_percentage']);
        $base_rate = floatval($data['base_rate']);
        $making_charges = floatval($data['making_charges'] ?? 0);
        $stone_charges = floatval($data['stone_charges'] ?? 0);

        // CORRECT FORMULA (matches your handwriting)
        $tunch_weight = $net_wt * ($tunch_percentage / 100);
        $metal_value = $tunch_weight * $base_rate; // DIRECT multiplication
        $subtotal = $metal_value + $making_charges + $stone_charges;

        // Apply discount and tax
        $discount_percentage = floatval($data['discount_percentage'] ?? 0);
        $discount_amount = $subtotal * ($discount_percentage / 100);
        $after_discount = $subtotal - $discount_amount;

        $tax_percentage = floatval($data['tax_percentage'] ?? 0);
        $tax_amount = $after_discount * ($tax_percentage / 100);
        $final_value = $after_discount + $tax_amount;

        return [
            'net_wt' => $net_wt,
            'tunch_weight' => $tunch_weight,
            'metal_value' => $metal_value,
            'subtotal' => $subtotal,
            'discount_amount' => $discount_amount,
            'tax_amount' => $tax_amount,
            'final_value' => $final_value
        ];
    }

    public function createBill($data) {
        try {
            $this->pdo->beginTransaction();

            // Calculate billing
            $calculations = $this->calculateBilling($data);

            // Generate invoice number
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM billing");
            $stmt->execute();
            $count = $stmt->fetchColumn() + 1;
            $invoice_number = 'INV-' . date('Y') . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);

            // Insert billing record
            $stmt = $this->pdo->prepare("INSERT INTO billing (
                invoice_number, customer_id, customer_name, location, product_name, inventory_id,
                gross_wt, stone_wt, net_wt, tunch_percentage, base_rate, tunch_weight, metal_value,
                making_charges, stone_charges, subtotal, discount_percentage, discount_amount,
                tax_percentage, tax_amount, final_value, payment_status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $invoice_number,
                $data['customer_id'] ?? null,
                $data['customer_name'],
                $data['location'],
                $data['product_name'],
                $data['inventory_id'] ?? null,
                $data['gross_wt'],
                $data['stone_wt'],
                $calculations['net_wt'],
                $data['tunch_percentage'],
                $data['base_rate'],
                $calculations['tunch_weight'],
                $calculations['metal_value'],
                $data['making_charges'] ?? 0,
                $data['stone_charges'] ?? 0,
                $calculations['subtotal'],
                $data['discount_percentage'] ?? 0,
                $calculations['discount_amount'],
                $data['tax_percentage'] ?? 0,
                $calculations['tax_amount'],
                $calculations['final_value'],
                'pending',
                $this->currentUser['id']
            ]);

            $bill_id = $this->pdo->lastInsertId();

            // Update inventory if linked
            if (!empty($data['inventory_id'])) {
                $stmt = $this->pdo->prepare("UPDATE inventory SET sold_value = sold_value + ?, balance_in_stock = balance_in_stock - ? WHERE id = ?");
                $stmt->execute([$calculations['final_value'], $calculations['net_wt'], $data['inventory_id']]);

                // Record stock movement
                $stmt = $this->pdo->prepare("INSERT INTO stock_movements (inventory_id, movement_type, quantity, reference_type, reference_id, created_by) VALUES (?, 'out', ?, 'sale', ?, ?)");
                $stmt->execute([$data['inventory_id'], $calculations['net_wt'], $bill_id, $this->currentUser['id']]);
            }

            $this->pdo->commit();
            return ['success' => true, 'invoice_number' => $invoice_number, 'bill_id' => $bill_id];

        } catch (Exception $e) {
            $this->pdo->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function addInventoryItem($data) {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO inventory (
                supplier_id, product_code, product_name, category, description,
                current_wt, without_stone_wt, with_stone_cost, without_stone_cost,
                procured_in_24k, with_stone_24k, without_stone_24k, weight_in_24k,
                unit_price, stone_price, total_value, balance_in_stock, min_stock_level
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            return $stmt->execute([
                $data['supplier_id'],
                $data['product_code'],
                $data['product_name'],
                $data['category'],
                $data['description'],
                $data['current_wt'],
                $data['without_stone_wt'],
                $data['with_stone_cost'],
                $data['without_stone_cost'],
                $data['procured_in_24k'],
                $data['with_stone_24k'],
                $data['without_stone_24k'],
                $data['weight_in_24k'],
                $data['unit_price'],
                $data['stone_price'],
                $data['total_value'],
                $data['balance_in_stock'],
                $data['min_stock_level'] ?? 0
            ]);
        } catch (Exception $e) {
            return false;
        }
    }

    public function getInventory($filters = []) {
        $sql = "SELECT i.*, s.name as supplier_name FROM inventory i
                LEFT JOIN suppliers s ON i.supplier_id = s.id";
        $params = [];

        if (!empty($filters['category'])) {
            $sql .= " WHERE i.category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['low_stock'])) {
            $where = !empty($params) ? " AND" : " WHERE";
            $sql .= "$where i.balance_in_stock <= i.min_stock_level";
        }

        $sql .= " ORDER BY i.created_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getBillingHistory($filters = []) {
        $sql = "SELECT * FROM billing";
        $params = [];

        if (!empty($filters['date_from'])) {
            $sql .= " WHERE DATE(created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where = !empty($params) ? " AND" : " WHERE";
            $sql .= "$where DATE(created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $sql .= " ORDER BY created_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getSuppliers() {
        $stmt = $this->pdo->prepare("SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDashboardStats() {
        $stats = [];

        // Total inventory value
        $stmt = $this->pdo->prepare("SELECT SUM(total_value) as total_inventory_value FROM inventory");
        $stmt->execute();
        $stats['total_inventory_value'] = $stmt->fetchColumn() ?: 0;

        // Today's sales
        $stmt = $this->pdo->prepare("SELECT SUM(final_value) as today_sales FROM billing WHERE DATE(created_at) = DATE('now')");
        $stmt->execute();
        $stats['today_sales'] = $stmt->fetchColumn() ?: 0;

        // This month's sales
        $stmt = $this->pdo->prepare("SELECT SUM(final_value) as month_sales FROM billing WHERE strftime('%Y-%m', created_at) = strftime('%Y-%m', 'now')");
        $stmt->execute();
        $stats['month_sales'] = $stmt->fetchColumn() ?: 0;

        // Low stock items
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as low_stock_count FROM inventory WHERE balance_in_stock <= min_stock_level AND min_stock_level > 0");
        $stmt->execute();
        $stats['low_stock_count'] = $stmt->fetchColumn() ?: 0;

        // Total items
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as total_items FROM inventory");
        $stmt->execute();
        $stats['total_items'] = $stmt->fetchColumn() ?: 0;

        return $stats;
    }
}

// Initialize the system
$system = new ProductionJewelrySystem();

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['login'])) {
        $error = $system->handleLogin();
    } elseif (isset($_POST['create_bill'])) {
        $result = $system->createBill($_POST);
        if ($result['success']) {
            $message = "✅ Bill created successfully! Invoice: " . $result['invoice_number'];
        } else {
            $error = "❌ Error creating bill: " . $result['error'];
        }
    } elseif (isset($_POST['add_inventory'])) {
        if ($system->addInventoryItem($_POST)) {
            $message = "✅ Inventory item added successfully!";
        } else {
            $error = "❌ Error adding inventory item.";
        }
    }
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    $system->handleLogout();
}

// Get current page
$page = $_GET['page'] ?? 'dashboard';

// Get data for current page
$data = [];
switch ($page) {
    case 'dashboard':
        $data['stats'] = $system->getDashboardStats();
        $data['recent_bills'] = $system->getBillingHistory(['limit' => 5]);
        $data['low_stock'] = $system->getInventory(['low_stock' => true, 'limit' => 5]);
        break;
    case 'billing':
        $data['inventory'] = $system->getInventory();
        $data['recent_bills'] = $system->getBillingHistory(['limit' => 10]);
        break;
    case 'inventory':
        $data['inventory'] = $system->getInventory();
        $data['suppliers'] = $system->getSuppliers();
        break;
    case 'reports':
        $data['stats'] = $system->getDashboardStats();
        $data['all_bills'] = $system->getBillingHistory();
        $data['all_inventory'] = $system->getInventory();
        break;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Jewelry Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .formula-display {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .calculation-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
        }
    </style>
</head>
<body>

<?php if ($page === 'login'): ?>
    <!-- LOGIN PAGE -->
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="login-card p-5" style="width: 400px;">
            <div class="text-center mb-4">
                <i class="fas fa-gem fa-3x text-primary mb-3"></i>
                <h2>Jewelry Management</h2>
                <p class="text-muted">Production System Login</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    Default: admin / admin123<br>
                    <strong>Formula:</strong> Metal Value = Tunch Weight × Base Rate
                </small>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- MAIN APPLICATION -->
    <div class="container-fluid">
        <div class="row">
            <!-- SIDEBAR -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-4">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-gem fa-2x mb-2"></i>
                        <h5>Jewelry System</h5>
                        <small>Production Ready</small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="?page=dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a class="nav-link <?php echo $page === 'billing' ? 'active' : ''; ?>" href="?page=billing">
                            <i class="fas fa-file-invoice-dollar me-2"></i> Billing
                        </a>
                        <a class="nav-link <?php echo $page === 'inventory' ? 'active' : ''; ?>" href="?page=inventory">
                            <i class="fas fa-boxes me-2"></i> Inventory
                        </a>
                        <a class="nav-link <?php echo $page === 'reports' ? 'active' : ''; ?>" href="?page=reports">
                            <i class="fas fa-chart-bar me-2"></i> Reports
                        </a>
                        <hr class="text-white-50">
                        <a class="nav-link" href="?action=logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- MAIN CONTENT -->
            <div class="col-md-10 main-content p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-gem text-primary"></i> <?php echo ucfirst($page); ?></h2>
                        <p class="text-muted mb-0">Welcome, <?php echo $_SESSION['username']; ?> | Role: <?php echo $_SESSION['role']; ?></p>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">
                            <?php echo date('l, F j, Y'); ?><br>
                            <strong>Correct Formula Applied</strong>
                        </small>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- PAGE CONTENT -->
                <?php include 'pages/' . $page . '.php'; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Production-ready JavaScript for billing calculations
    document.addEventListener('DOMContentLoaded', function() {
        // Billing calculation functionality
        if (document.getElementById('gross_wt')) {
            const grossWt = document.getElementById('gross_wt');
            const stoneWt = document.getElementById('stone_wt');
            const netWt = document.getElementById('net_wt');
            const tunchPercentage = document.getElementById('tunch_percentage');
            const baseRate = document.getElementById('base_rate');
            const makingCharges = document.getElementById('making_charges');
            const stoneCharges = document.getElementById('stone_charges');
            const discountPercentage = document.getElementById('discount_percentage');
            const taxPercentage = document.getElementById('tax_percentage');

            // Display elements
            const tunchWeightDisplay = document.getElementById('tunchWeightDisplay');
            const metalValueDisplay = document.getElementById('metalValueDisplay');
            const subtotalDisplay = document.getElementById('subtotalDisplay');
            const finalValueDisplay = document.getElementById('finalValueDisplay');
            const calculationDisplay = document.getElementById('calculationDisplay');

            function calculateNetWeight() {
                const gross = parseFloat(grossWt.value) || 0;
                const stone = parseFloat(stoneWt.value) || 0;
                const net = gross - stone;
                netWt.value = net.toFixed(3);
                calculateAll();
            }

            function calculateAll() {
                const net = parseFloat(netWt.value) || 0;
                const tunchPct = parseFloat(tunchPercentage.value) || 0;
                const rate = parseFloat(baseRate.value) || 0;
                const making = parseFloat(makingCharges.value) || 0;
                const stoneCharge = parseFloat(stoneCharges.value) || 0;
                const discount = parseFloat(discountPercentage.value) || 0;
                const tax = parseFloat(taxPercentage.value) || 0;

                if (net <= 0 || tunchPct <= 0 || rate <= 0) {
                    resetDisplays();
                    return;
                }

                // PRODUCTION FORMULA (matches your handwriting)
                const tunchWeight = net * (tunchPct / 100);
                const metalValue = tunchWeight * rate; // DIRECT multiplication
                const subtotal = metalValue + making + stoneCharge;
                const discountAmount = subtotal * (discount / 100);
                const afterDiscount = subtotal - discountAmount;
                const taxAmount = afterDiscount * (tax / 100);
                const finalValue = afterDiscount + taxAmount;

                // Update displays
                if (tunchWeightDisplay) tunchWeightDisplay.textContent = tunchWeight.toFixed(3) + 'g';
                if (metalValueDisplay) metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2});
                if (subtotalDisplay) subtotalDisplay.textContent = '₹' + subtotal.toLocaleString('en-IN', {minimumFractionDigits: 2});
                if (finalValueDisplay) finalValueDisplay.textContent = '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2});

                if (calculationDisplay) {
                    calculationDisplay.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ Production Formula Applied:</strong><br>
                            ${net.toFixed(3)}g × ${tunchPct}% = ${tunchWeight.toFixed(3)}g × ₹${rate.toLocaleString('en-IN')} = ₹${metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2})}
                            ${discount > 0 ? `<br><small>Discount: ${discount}% = ₹${discountAmount.toLocaleString('en-IN', {minimumFractionDigits: 2})}</small>` : ''}
                            ${tax > 0 ? `<br><small>Tax: ${tax}% = ₹${taxAmount.toLocaleString('en-IN', {minimumFractionDigits: 2})}</small>` : ''}
                        </div>
                    `;
                }
            }

            function resetDisplays() {
                if (tunchWeightDisplay) tunchWeightDisplay.textContent = '0.000g';
                if (metalValueDisplay) metalValueDisplay.textContent = '₹0.00';
                if (subtotalDisplay) subtotalDisplay.textContent = '₹0.00';
                if (finalValueDisplay) finalValueDisplay.textContent = '₹0.00';
                if (calculationDisplay) calculationDisplay.innerHTML = '<p class="text-muted">Enter values to see calculation</p>';
            }

            // Event listeners
            if (grossWt) grossWt.addEventListener('input', calculateNetWeight);
            if (stoneWt) stoneWt.addEventListener('input', calculateNetWeight);
            if (tunchPercentage) tunchPercentage.addEventListener('input', calculateAll);
            if (baseRate) baseRate.addEventListener('input', calculateAll);
            if (makingCharges) makingCharges.addEventListener('input', calculateAll);
            if (stoneCharges) stoneCharges.addEventListener('input', calculateAll);
            if (discountPercentage) discountPercentage.addEventListener('input', calculateAll);
            if (taxPercentage) taxPercentage.addEventListener('input', calculateAll);
        }

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    });
</script>
</body>
</html>
