<?php
/**
 * Suppliers Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$supplier_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'supplier_name' => sanitizeInput($_POST['supplier_name']),
                'contact_person' => sanitizeInput($_POST['contact_person']),
                'phone' => sanitizeInput($_POST['phone']),
                'email' => sanitizeInput($_POST['email']),
                'address' => sanitizeInput($_POST['address']),
                'city' => sanitizeInput($_POST['city']),
                'state' => sanitizeInput($_POST['state']),
                'pincode' => sanitizeInput($_POST['pincode']),
                'gst_number' => sanitizeInput($_POST['gst_number']),
                'pan_number' => sanitizeInput($_POST['pan_number']),
                'bank_name' => sanitizeInput($_POST['bank_name']),
                'account_number' => sanitizeInput($_POST['account_number']),
                'ifsc_code' => sanitizeInput($_POST['ifsc_code']),
                'credit_limit' => floatval($_POST['credit_limit']),
                'credit_days' => intval($_POST['credit_days']),
                'notes' => sanitizeInput($_POST['notes'])
            ];
            
            // Validate GST number if provided
            if ($data['gst_number'] && !validateGST($data['gst_number'])) {
                throw new Exception("Invalid GST number format");
            }
            
            // Validate email if provided
            if ($data['email'] && !validateEmail($data['email'])) {
                throw new Exception("Invalid email format");
            }
            
            // Validate phone if provided
            if ($data['phone'] && !validatePhone($data['phone'])) {
                throw new Exception("Invalid phone number format");
            }
            
            if ($action === 'add') {
                $sql = "INSERT INTO suppliers (supplier_name, contact_person, phone, email, address, city, state, pincode, gst_number, pan_number, bank_name, account_number, ifsc_code, credit_limit, credit_days, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "Supplier added successfully!";
            } else {
                $sql = "UPDATE suppliers SET supplier_name=?, contact_person=?, phone=?, email=?, address=?, city=?, state=?, pincode=?, gst_number=?, pan_number=?, bank_name=?, account_number=?, ifsc_code=?, credit_limit=?, credit_days=?, notes=? WHERE id=?";
                $params = array_values($data);
                $params[] = $supplier_id;
                $db->query($sql, $params);
                $success = "Supplier updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $supplier_id) {
            // Check if supplier has products
            $product_count = $db->fetch("SELECT COUNT(*) as count FROM products WHERE supplier_id = ?", [$supplier_id])['count'];
            
            if ($product_count > 0) {
                $error = "Cannot delete supplier. It has $product_count products assigned to it.";
            } else {
                $db->query("UPDATE suppliers SET is_active = 0 WHERE id = ?", [$supplier_id]);
                $success = "Supplier deleted successfully!";
            }
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get suppliers list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $city_filter = $_GET['city'] ?? '';
    
    $where_conditions = ["is_active = 1"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(supplier_name LIKE ? OR contact_person LIKE ? OR phone LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($city_filter) {
        $where_conditions[] = "city = ?";
        $params[] = $city_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $suppliers = $db->fetchAll("
        SELECT s.*, 
               (SELECT COUNT(*) FROM products WHERE supplier_id = s.id AND is_active = 1) as product_count
        FROM suppliers s
        WHERE $where_clause
        ORDER BY s.supplier_name
    ", $params);
    
    // Get cities for filter
    $cities = $db->fetchAll("SELECT DISTINCT city FROM suppliers WHERE is_active = 1 AND city IS NOT NULL AND city != '' ORDER BY city");
}

// Get single supplier for edit
if ($action === 'edit' && $supplier_id) {
    $supplier = $db->fetch("SELECT * FROM suppliers WHERE id = ? AND is_active = 1", [$supplier_id]);
    if (!$supplier) {
        $error = "Supplier not found!";
        $action = 'list';
    }
}

// Get supplier details for view
if ($action === 'view' && $supplier_id) {
    $supplier = $db->fetch("SELECT * FROM suppliers WHERE id = ? AND is_active = 1", [$supplier_id]);
    if (!$supplier) {
        $error = "Supplier not found!";
        $action = 'list';
    } else {
        // Get supplier's products
        $supplier_products = $db->fetchAll("
            SELECT p.*, c.category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.supplier_id = ? AND p.is_active = 1 
            ORDER BY p.product_name
        ", [$supplier_id]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suppliers - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Suppliers List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Suppliers</h2>
                    <p class="text-muted mb-0">Manage your jewellery suppliers</p>
                </div>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Supplier
                </a>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search by name, contact, or phone...">
                        </div>
                        <div class="col-md-3">
                            <label for="city" class="form-label">City</label>
                            <select class="form-select" id="city" name="city">
                                <option value="">All Cities</option>
                                <?php foreach ($cities as $city): ?>
                                    <option value="<?php echo htmlspecialchars($city['city']); ?>" 
                                            <?php echo $city_filter === $city['city'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($city['city']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="suppliers.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Suppliers Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Supplier</th>
                                    <th>Contact</th>
                                    <th>Location</th>
                                    <th>GST Number</th>
                                    <th>Products</th>
                                    <th>Credit Limit</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($suppliers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4 text-muted">
                                            <i class="fas fa-truck fa-3x mb-3 opacity-25"></i>
                                            <p>No suppliers found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($supplier['supplier_name']); ?></h6>
                                                    <?php if ($supplier['contact_person']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars($supplier['contact_person']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($supplier['phone']): ?>
                                                    <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($supplier['phone']); ?></div>
                                                <?php endif; ?>
                                                <?php if ($supplier['email']): ?>
                                                    <div><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($supplier['email']); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($supplier['city']): ?>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($supplier['city']); ?>
                                                        <?php if ($supplier['state']): ?>
                                                            , <?php echo htmlspecialchars($supplier['state']); ?>
                                                        <?php endif; ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($supplier['gst_number']): ?>
                                                    <code><?php echo htmlspecialchars($supplier['gst_number']); ?></code>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $supplier['product_count']; ?> products
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($supplier['credit_limit'] > 0): ?>
                                                    <?php echo formatCurrency($supplier['credit_limit']); ?>
                                                    <?php if ($supplier['credit_days'] > 0): ?>
                                                        <br><small class="text-muted"><?php echo $supplier['credit_days']; ?> days</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No credit</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=view&id=<?php echo $supplier['id']; ?>" 
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="?action=edit&id=<?php echo $supplier['id']; ?>" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($supplier['product_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteSupplier(<?php echo $supplier['id']; ?>)" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Supplier Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Supplier' : 'Edit Supplier'; ?></h2>
                    <p class="text-muted mb-0">Enter supplier details below</p>
                </div>
                <a href="suppliers.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Suppliers
                </a>
            </div>

            <form method="POST">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="supplier_name" class="form-label">Supplier Name *</label>
                                        <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                                               value="<?php echo htmlspecialchars($supplier['supplier_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_person" class="form-label">Contact Person</label>
                                        <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                               value="<?php echo htmlspecialchars($supplier['contact_person'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($supplier['phone'] ?? ''); ?>" 
                                               pattern="[6-9][0-9]{9}" placeholder="10-digit mobile number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($supplier['email'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($supplier['address'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="<?php echo htmlspecialchars($supplier['city'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="state" class="form-label">State</label>
                                        <input type="text" class="form-control" id="state" name="state" 
                                               value="<?php echo htmlspecialchars($supplier['state'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="pincode" class="form-label">Pincode</label>
                                        <input type="text" class="form-control" id="pincode" name="pincode" 
                                               value="<?php echo htmlspecialchars($supplier['pincode'] ?? ''); ?>" 
                                               pattern="[0-9]{6}" maxlength="6">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Business Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gst_number" class="form-label">GST Number</label>
                                        <input type="text" class="form-control" id="gst_number" name="gst_number" 
                                               value="<?php echo htmlspecialchars($supplier['gst_number'] ?? ''); ?>" 
                                               pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}" 
                                               maxlength="15" placeholder="22**********1Z5">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="pan_number" class="form-label">PAN Number</label>
                                        <input type="text" class="form-control" id="pan_number" name="pan_number" 
                                               value="<?php echo htmlspecialchars($supplier['pan_number'] ?? ''); ?>" 
                                               pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}" maxlength="10" placeholder="**********">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="credit_limit" class="form-label">Credit Limit (₹)</label>
                                        <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                               value="<?php echo $supplier['credit_limit'] ?? '0'; ?>" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="credit_days" class="form-label">Credit Days</label>
                                        <input type="number" class="form-control" id="credit_days" name="credit_days" 
                                               value="<?php echo $supplier['credit_days'] ?? '0'; ?>" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Banking Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="bank_name" class="form-label">Bank Name</label>
                                        <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                               value="<?php echo htmlspecialchars($supplier['bank_name'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="account_number" class="form-label">Account Number</label>
                                        <input type="text" class="form-control" id="account_number" name="account_number" 
                                               value="<?php echo htmlspecialchars($supplier['account_number'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="ifsc_code" class="form-label">IFSC Code</label>
                                        <input type="text" class="form-control" id="ifsc_code" name="ifsc_code" 
                                               value="<?php echo htmlspecialchars($supplier['ifsc_code'] ?? ''); ?>" 
                                               pattern="[A-Z]{4}0[A-Z0-9]{6}" maxlength="11" placeholder="ABCD0123456">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($supplier['notes'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="suppliers.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $action === 'add' ? 'Add Supplier' : 'Update Supplier'; ?>
                            </button>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Guidelines</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-2"></i>GST Number</h6>
                                    <p class="mb-0 small">Format: 22**********1Z5<br>
                                    First 2 digits: State code<br>
                                    Next 10 characters: PAN<br>
                                    Last 3: Check digits</p>
                                </div>
                                
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-phone me-2"></i>Phone Number</h6>
                                    <p class="mb-0 small">Enter 10-digit mobile number starting with 6, 7, 8, or 9</p>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-credit-card me-2"></i>Credit Terms</h6>
                                    <p class="mb-0 small">Set credit limit and payment terms for business transactions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        <?php elseif ($action === 'view'): ?>
            <!-- View Supplier Details -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo htmlspecialchars($supplier['supplier_name']); ?></h2>
                    <p class="text-muted mb-0">Supplier Details</p>
                </div>
                <div>
                    <a href="?action=edit&id=<?php echo $supplier['id']; ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>Edit Supplier
                    </a>
                    <a href="suppliers.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Suppliers
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Contact Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Contact Person:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['contact_person'] ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['phone'] ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['email'] ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>GST Number:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['gst_number'] ?: '-'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td><?php echo nl2br(htmlspecialchars($supplier['address'] ?: '-')); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>City:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['city'] ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>State:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['state'] ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Pincode:</strong></td>
                                            <td><?php echo htmlspecialchars($supplier['pincode'] ?: '-'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($supplier_products)): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Products from this Supplier</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Product Name</th>
                                                <th>Code</th>
                                                <th>Category</th>
                                                <th>Metal/Purity</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($supplier_products as $product): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                                                    <td><code><?php echo htmlspecialchars($product['product_code']); ?></code></td>
                                                    <td>
                                                        <span class="badge bg-secondary">
                                                            <?php echo htmlspecialchars($product['category_name'] ?: 'Uncategorized'); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($product['metal_type']): ?>
                                                            <span class="badge bg-warning text-dark">
                                                                <?php echo htmlspecialchars($product['metal_type'] . ' ' . $product['purity']); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Business Details</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>PAN Number:</strong></td>
                                    <td><?php echo htmlspecialchars($supplier['pan_number'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Credit Limit:</strong></td>
                                    <td><?php echo $supplier['credit_limit'] > 0 ? formatCurrency($supplier['credit_limit']) : '-'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Credit Days:</strong></td>
                                    <td><?php echo $supplier['credit_days'] > 0 ? $supplier['credit_days'] . ' days' : '-'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($supplier['bank_name'] || $supplier['account_number'] || $supplier['ifsc_code']): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Banking Details</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Bank Name:</strong></td>
                                        <td><?php echo htmlspecialchars($supplier['bank_name'] ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Account Number:</strong></td>
                                        <td><?php echo htmlspecialchars($supplier['account_number'] ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>IFSC Code:</strong></td>
                                        <td><?php echo htmlspecialchars($supplier['ifsc_code'] ?: '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($supplier['notes']): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Notes</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($supplier['notes'])); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteSupplier(id) {
            if (confirm('Are you sure you want to delete this supplier? This action cannot be undone.')) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }

        // Format input fields
        document.getElementById('gst_number')?.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });

        document.getElementById('pan_number')?.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });

        document.getElementById('ifsc_code')?.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
