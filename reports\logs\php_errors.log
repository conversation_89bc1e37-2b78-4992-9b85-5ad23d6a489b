[29-Jul-2025 18:30:13 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 150
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 150
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 195
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 195
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:30:13 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:30:21 Asia/Kolkata] Query failed: SQLSTATE[HY093]: Invalid parameter number SQL: 
        SELECT 
            cat.category_name,
            COUNT(DISTINCT s.id) as sales_count,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as category_revenue
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE s.sale_date BETWEEN ? AND s.is_cancelled = 0
        GROUP BY cat.id, cat.category_name
        ORDER BY category_revenue DESC
    
[29-Jul-2025 18:30:21 Asia/Kolkata] PHP Warning:  Undefined variable $grossProfit in C:\proj\v2\reports\financial.php on line 216
[29-Jul-2025 18:30:21 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 216
[29-Jul-2025 18:30:21 Asia/Kolkata] PHP Warning:  Undefined variable $profitMargin in C:\proj\v2\reports\financial.php on line 224
[29-Jul-2025 18:30:21 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 224
[29-Jul-2025 18:51:53 Asia/Kolkata] Query failed: SQLSTATE[HY093]: Invalid parameter number SQL: 
        SELECT 
            cat.category_name,
            COUNT(DISTINCT s.id) as sales_count,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as category_revenue
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE s.sale_date BETWEEN ? AND s.is_cancelled = 0
        GROUP BY cat.id, cat.category_name
        ORDER BY category_revenue DESC
    
[29-Jul-2025 18:51:53 Asia/Kolkata] PHP Warning:  Undefined variable $grossProfit in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 18:51:53 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 18:51:53 Asia/Kolkata] PHP Warning:  Undefined variable $profitMargin in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 18:51:53 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 18:51:56 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:51:56 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:52:53 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:52:53 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:54:18 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:54:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:54:30 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:54:30 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:55:18 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 153
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 198
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 206
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 214
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 222
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 230
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:55:18 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 238
[29-Jul-2025 18:57:45 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 158
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 158
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 243
[29-Jul-2025 18:57:45 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 243
[29-Jul-2025 18:57:46 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 158
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 158
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 203
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 211
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 219
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 227
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 235
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 243
[29-Jul-2025 18:57:46 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 243
[29-Jul-2025 19:00:23 Asia/Kolkata] Query failed: SQLSTATE[HY093]: Invalid parameter number SQL: 
        SELECT 
            cat.category_name,
            COUNT(DISTINCT s.id) as sales_count,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as category_revenue
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE s.sale_date BETWEEN ? AND s.is_cancelled = 0
        GROUP BY cat.id, cat.category_name
        ORDER BY category_revenue DESC
    
[29-Jul-2025 19:00:23 Asia/Kolkata] PHP Warning:  Undefined variable $grossProfit in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 19:00:23 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 19:00:23 Asia/Kolkata] PHP Warning:  Undefined variable $profitMargin in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 19:00:23 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 19:00:25 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 19:00:25 Asia/Kolkata] Inventory Report Error: Database query failed in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 160
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 160
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 205
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 205
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 229
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 229
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 237
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 237
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 245
[29-Jul-2025 19:00:25 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 245
[29-Jul-2025 19:00:30 Asia/Kolkata] Query failed: SQLSTATE[HY093]: Invalid parameter number SQL: 
        SELECT 
            cat.category_name,
            COUNT(DISTINCT s.id) as sales_count,
            COALESCE(SUM(si.quantity * si.unit_price), 0) as category_revenue
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE s.sale_date BETWEEN ? AND s.is_cancelled = 0
        GROUP BY cat.id, cat.category_name
        ORDER BY category_revenue DESC
    
[29-Jul-2025 19:00:30 Asia/Kolkata] PHP Warning:  Undefined variable $grossProfit in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 19:00:30 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 219
[29-Jul-2025 19:00:30 Asia/Kolkata] PHP Warning:  Undefined variable $profitMargin in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 19:00:30 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\financial.php on line 227
[29-Jul-2025 19:00:51 Asia/Kolkata] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.selling_price' in 'field list' SQL: 
        SELECT p.*, c.category_name, i.*,
               (i.quantity_in_stock * p.selling_price) as stock_value,
               CASE
                   WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                   WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                   ELSE 'Normal'
               END as stock_status_text
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.is_active = 1
        ORDER BY product_name ASC
    
[29-Jul-2025 19:00:51 Asia/Kolkata] Inventory Report Error: Database query failed in C:\proj\v2\config\database.php on line 81
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $categories in C:\proj\v2\reports\inventory.php on line 160
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  foreach() argument must be of type array|object, null given in C:\proj\v2\reports\inventory.php on line 160
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 205
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 205
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 213
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\proj\v2\reports\inventory.php on line 221
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 229
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 229
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 237
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 237
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Undefined variable $summary in C:\proj\v2\reports\inventory.php on line 245
[29-Jul-2025 19:00:51 Asia/Kolkata] PHP Warning:  Trying to access array offset on null in C:\proj\v2\reports\inventory.php on line 245
