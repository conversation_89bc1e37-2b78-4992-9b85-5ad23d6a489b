<?php
/**
 * Test Database Connection and Tables
 */

require_once 'config/database.php';

try {
    echo "<h2>Database Connection Test</h2>";
    
    $db = getDB();
    echo "✅ Database connection successful!<br><br>";
    
    // Test basic query
    echo "<h3>Testing Basic Query</h3>";
    $result = $db->fetch("SELECT 1 as test");
    echo "✅ Basic query works: " . $result['test'] . "<br><br>";
    
    // Check if tables exist
    echo "<h3>Checking Tables</h3>";
    $tables = ['products', 'categories', 'inventory', 'sales', 'customers', 'suppliers'];
    
    foreach ($tables as $table) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table");
            echo "✅ Table '$table' exists with {$count['count']} records<br>";
        } catch (Exception $e) {
            echo "❌ Table '$table' error: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><h3>Testing Inventory Query</h3>";
    
    // Test the exact query from inventory report
    try {
        $where_clause = "p.is_active = 1";
        $sort_by = "product_name";
        
        $inventory_query = "
            SELECT p.*, c.category_name, i.*,
                   (i.quantity_in_stock * p.selling_price) as stock_value,
                   CASE 
                       WHEN i.quantity_in_stock = 0 THEN 'Out of Stock'
                       WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'Low Stock'
                       WHEN i.quantity_in_stock > (i.minimum_stock_level * 3) AND i.minimum_stock_level > 0 THEN 'Overstock'
                       ELSE 'Normal'
                   END as stock_status_text
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN inventory i ON p.id = i.product_id
            WHERE " . $where_clause . "
            ORDER BY " . $sort_by . " ASC
            LIMIT 5
        ";
        
        $inventory = $db->fetchAll($inventory_query);
        echo "✅ Inventory query successful! Found " . count($inventory) . " products<br>";
        
        if (count($inventory) > 0) {
            echo "<pre>";
            print_r($inventory[0]);
            echo "</pre>";
        }
        
    } catch (Exception $e) {
        echo "❌ Inventory query failed: " . $e->getMessage() . "<br>";
        echo "Query: " . $inventory_query . "<br>";
    }
    
    echo "<br><h3>Testing Products Table Structure</h3>";
    try {
        $columns = $db->fetchAll("DESCRIBE products");
        echo "✅ Products table structure:<br>";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error getting products structure: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage();
}
?>
