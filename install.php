<?php
/**
 * Installation Script for Indian Jewellery Wholesale Management System v2.0
 * This script creates the database and initializes the system
 */

// Prevent running if already installed
if (file_exists('config/.installed')) {
    die('System is already installed. Delete config/.installed file to reinstall.');
}

$error = '';
$success = '';
$step = $_GET['step'] ?? 1;

if ($_POST) {
    if ($step == 1) {
        // Database connection test
        try {
            $host = $_POST['db_host'] ?? 'localhost';
            $dbname = $_POST['db_name'] ?? 'jewellery_wholesale_v2';
            $username = $_POST['db_user'] ?? 'root';
            $password = $_POST['db_pass'] ?? '';
            
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$dbname`");
            
            // Store database config
            $_SESSION['db_config'] = [
                'host' => $host,
                'dbname' => $dbname,
                'username' => $username,
                'password' => $password
            ];
            
            $success = 'Database connection successful!';
            $step = 2;
            
        } catch (PDOException $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Create tables and insert data
        try {
            session_start();
            $config = $_SESSION['db_config'];
            
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Read and execute schema
            $schema = file_get_contents('database/schema.sql');
            $statements = explode(';', $schema);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // Update database config file
            $configContent = "<?php
// Database Configuration
define('DB_HOST', '{$config['host']}');
define('DB_NAME', '{$config['dbname']}');
define('DB_USER', '{$config['username']}');
define('DB_PASS', '{$config['password']}');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Indian Jewellery Wholesale');
define('APP_VERSION', '2.0.0');
define('APP_URL', 'http://localhost:8000');

// Business Configuration
define('CURRENCY_SYMBOL', '₹');
define('CURRENCY_CODE', 'INR');
define('DEFAULT_TIMEZONE', 'Asia/Kolkata');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i:s');

// System Configuration
define('RECORDS_PER_PAGE', 25);
define('MAX_UPLOAD_SIZE', '5MB');
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);

// Security Configuration
define('SESSION_TIMEOUT', 3600);
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 6);

date_default_timezone_set(DEFAULT_TIMEZONE);
?>";
            
            file_put_contents('config/database.php', $configContent);
            
            // Create installation marker
            if (!is_dir('config')) {
                mkdir('config', 0755, true);
            }
            file_put_contents('config/.installed', date('Y-m-d H:i:s'));
            
            $success = 'Installation completed successfully!';
            $step = 3;
            
        } catch (Exception $e) {
            $error = 'Installation failed: ' . $e->getMessage();
        }
    }
}

if (!isset($_SESSION)) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Indian Jewellery Wholesale Management System v2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            background: #e9ecef;
            color: #6c757d;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-card">
        <div class="install-header">
            <h1><i class="fas fa-gem me-2"></i>Indian Jewellery Wholesale</h1>
            <p class="mb-0">Management System v2.0 Installation</p>
        </div>
        
        <div class="step-indicator">
            <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">1</div>
            <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">2</div>
            <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
        </div>
        
        <div class="p-4">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h4>Step 1: Database Configuration</h4>
                <p class="text-muted">Please provide your database connection details.</p>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">Database Host</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">Database Name</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" value="jewellery_wholesale_v2" required>
                        <div class="form-text">Database will be created if it doesn't exist.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_user" class="form-label">Database Username</label>
                        <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_pass" class="form-label">Database Password</label>
                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-database me-2"></i>Test Connection & Continue
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <h4>Step 2: Install Database</h4>
                <p class="text-muted">Ready to create database tables and insert initial data.</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This will create all necessary tables and insert default data including:
                    <ul class="mb-0 mt-2">
                        <li>Admin user (username: admin, password: admin123)</li>
                        <li>Product categories</li>
                        <li>Default settings</li>
                        <li>Current metal rates</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="step" value="2">
                    <button type="submit" class="btn btn-success btn-lg w-100">
                        <i class="fas fa-cog me-2"></i>Install Database
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h4>Step 3: Installation Complete!</h4>
                <p class="text-muted">Your Indian Jewellery Wholesale Management System is ready to use.</p>
                
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Installation Successful!</h5>
                    <p class="mb-2">Default admin credentials:</p>
                    <ul class="mb-0">
                        <li><strong>Username:</strong> admin</li>
                        <li><strong>Password:</strong> admin123</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-shield-alt me-2"></i>
                    <strong>Security Note:</strong> Please change the default admin password after logging in.
                </div>
                
                <div class="d-grid gap-2">
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Go to Dashboard
                    </a>
                    <a href="login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Login Page
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
