<?php
/**
 * Product Details Modal/Popup - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();

$db = getDB();
$product_id = $_GET['id'] ?? null;

if (!$product_id) {
    die('Product ID required');
}

// Get product details with related information
try {
    $product = $db->fetch("
        SELECT p.*, c.category_name, s.supplier_name, s.contact_person, s.phone as supplier_phone,
               i.quantity_in_stock, i.minimum_stock_level, i.cost_price, i.selling_price, i.location, i.rack_number
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.id = ? AND p.is_active = 1
    ", [$product_id]);

    if (!$product) {
        die('Product not found');
    }

    // Get recent sales for this product
    $recentSales = $db->fetchAll("
        SELECT si.quantity, si.unit_price, si.total_price, s.sale_date, s.bill_number, c.customer_name
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE si.product_id = ? AND s.is_cancelled = 0
        ORDER BY s.sale_date DESC
        LIMIT 5
    ", [$product_id]);

    // Get stock movements
    $stockMovements = $db->fetchAll("
        SELECT sm.*, u.username
        FROM stock_movements sm
        LEFT JOIN users u ON sm.created_by = u.id
        WHERE sm.product_id = ?
        ORDER BY sm.created_at DESC
        LIMIT 10
    ", [$product_id]);

} catch (Exception $e) {
    die('Error loading product details: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - <?php echo htmlspecialchars($product['product_name']); ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
        .detail-card { border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .detail-label { font-weight: 600; color: #495057; }
        .detail-value { color: #212529; }
        .stock-badge { font-size: 0.9em; }
        .movement-item { border-left: 3px solid #007bff; padding-left: 15px; margin-bottom: 15px; }
        .movement-in { border-left-color: #28a745; }
        .movement-out { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h3>
                <p class="text-muted mb-0">Product Code: <?php echo htmlspecialchars($product['product_code']); ?></p>
            </div>
            <button class="btn btn-outline-secondary" onclick="window.close()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>

        <div class="row">
            <!-- Product Information -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-gem me-2"></i>Product Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Category:</div>
                            <div class="col-sm-8 detail-value">
                                <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category_name'] ?? 'Uncategorized'); ?></span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Supplier:</div>
                            <div class="col-sm-8 detail-value">
                                <?php if ($product['supplier_name']): ?>
                                    <strong><?php echo htmlspecialchars($product['supplier_name']); ?></strong><br>
                                    <small class="text-muted">
                                        Contact: <?php echo htmlspecialchars($product['contact_person']); ?> 
                                        (<?php echo htmlspecialchars($product['supplier_phone']); ?>)
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">No supplier assigned</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Metal & Purity:</div>
                            <div class="col-sm-8 detail-value">
                                <?php if ($product['metal_type']): ?>
                                    <span class="badge bg-warning text-dark">
                                        <?php echo htmlspecialchars($product['metal_type'] . ' ' . $product['purity']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Weights:</div>
                            <div class="col-sm-8 detail-value">
                                <small>
                                    Base: <?php echo formatWeight($product['base_weight']); ?> | 
                                    Stone: <?php echo formatWeight($product['stone_weight']); ?> | 
                                    <strong>Net: <?php echo formatWeight($product['net_weight']); ?></strong>
                                </small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Costs:</div>
                            <div class="col-sm-8 detail-value">
                                <small>
                                    Stone: <?php echo formatCurrency($product['stone_cost']); ?> | 
                                    Making: <?php echo formatCurrency($product['making_charges']); ?>
                                </small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">HSN Code:</div>
                            <div class="col-sm-8 detail-value"><?php echo htmlspecialchars($product['hsn_code'] ?? 'Not specified'); ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Tax Rate:</div>
                            <div class="col-sm-8 detail-value"><?php echo $product['tax_rate']; ?>%</div>
                        </div>

                        <?php if ($product['description']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Description:</div>
                            <div class="col-sm-8 detail-value"><?php echo nl2br(htmlspecialchars($product['description'])); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Inventory Information -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>Inventory Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Current Stock:</div>
                            <div class="col-sm-8 detail-value">
                                <?php 
                                $stock = $product['quantity_in_stock'] ?? 0;
                                $min_stock = $product['minimum_stock_level'] ?? 0;
                                $stock_class = $stock <= $min_stock && $min_stock > 0 ? 'bg-danger' : 'bg-success';
                                ?>
                                <span class="badge <?php echo $stock_class; ?> stock-badge">
                                    <?php echo $stock; ?> units
                                </span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Minimum Level:</div>
                            <div class="col-sm-8 detail-value"><?php echo $min_stock; ?> units</div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Location:</div>
                            <div class="col-sm-8 detail-value">
                                <?php echo htmlspecialchars($product['location'] ?? 'Not specified'); ?>
                                <?php if ($product['rack_number']): ?>
                                    - Rack: <?php echo htmlspecialchars($product['rack_number']); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Cost Price:</div>
                            <div class="col-sm-8 detail-value"><?php echo formatCurrency($product['cost_price'] ?? 0); ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-4 detail-label">Selling Price:</div>
                            <div class="col-sm-8 detail-value"><?php echo formatCurrency($product['selling_price'] ?? 0); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Sales -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Recent Sales</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentSales)): ?>
                            <p class="text-muted text-center py-3">No recent sales found</p>
                        <?php else: ?>
                            <?php foreach ($recentSales as $sale): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                                    <div>
                                        <strong><?php echo htmlspecialchars($sale['bill_number']); ?></strong><br>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($sale['customer_name'] ?? 'Walk-in'); ?> - 
                                            <?php echo formatDate($sale['sale_date']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div><?php echo $sale['quantity']; ?> units</div>
                                        <small class="text-muted"><?php echo formatCurrency($sale['total_price']); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Stock Movements -->
            <div class="col-md-6 mb-4">
                <div class="card detail-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Stock Movements</h5>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        <?php if (empty($stockMovements)): ?>
                            <p class="text-muted text-center py-3">No stock movements found</p>
                        <?php else: ?>
                            <?php foreach ($stockMovements as $movement): ?>
                                <div class="movement-item movement-<?php echo $movement['movement_type']; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>
                                                <?php echo $movement['movement_type'] === 'in' ? '+' : '-'; ?>
                                                <?php echo $movement['quantity']; ?> units
                                            </strong>
                                            <span class="badge bg-<?php echo $movement['movement_type'] === 'in' ? 'success' : 'danger'; ?> ms-2">
                                                <?php echo ucfirst($movement['movement_type']); ?>
                                            </span>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo formatDateTime($movement['created_at']); ?>
                                        </small>
                                    </div>
                                    <?php if ($movement['notes']): ?>
                                        <small class="text-muted d-block mt-1">
                                            <?php echo htmlspecialchars($movement['notes']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <a href="products.php?action=edit&id=<?php echo $product['id']; ?>" class="btn btn-primary" target="_blank">
                <i class="fas fa-edit me-2"></i>Edit Product
            </a>
            <a href="inventory.php?product=<?php echo $product['id']; ?>" class="btn btn-success" target="_blank">
                <i class="fas fa-boxes me-2"></i>Manage Stock
            </a>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Print Details
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
