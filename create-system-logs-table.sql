-- Create System Logs Table for Indian Jewellery Wholesale Management System v2.0
-- This table is used for logging system activities, user actions, and security events

CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    log_type ENUM('error', 'access', 'system', 'security', 'info') DEFAULT 'info',
    level ENUM('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
    message TEXT NOT NULL,
    context JSON,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for better performance
    INDEX idx_log_type (log_type),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address)
);

-- Insert some sample log entries for demonstration
INSERT INTO system_logs (log_type, level, message, context, user_id, ip_address, created_at) VALUES
('access', 'info', 'User login successful', '{"username": "admin", "role": "admin"}', 1, '127.0.0.1', NOW()),
('system', 'info', 'Database backup completed', '{"size": "15.2MB", "duration": "2m 30s"}', NULL, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('security', 'warning', 'Multiple failed login attempts', '{"ip": "*************", "attempts": 3}', NULL, '*************', DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('system', 'info', 'Cache cleared by user', '{"cache_type": "all", "size_cleared": "5.8MB"}', 1, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 3 HOUR)),
('access', 'info', 'User logout', '{"username": "admin", "session_duration": "2h 15m"}', 1, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 4 HOUR)),
('error', 'warning', 'Low disk space warning', '{"available": "2.1GB", "threshold": "5GB"}', NULL, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 5 HOUR)),
('system', 'info', 'Scheduled task executed', '{"task": "inventory_sync", "status": "success"}', NULL, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 6 HOUR)),
('access', 'info', 'User login successful', '{"username": "manager", "role": "manager"}', 2, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('system', 'info', 'Database optimization completed', '{"tables_optimized": 12, "space_saved": "2.3MB"}', 1, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('security', 'info', 'Password changed', '{"username": "staff"}', 3, '127.0.0.1', DATE_SUB(NOW(), INTERVAL 2 DAY));

-- Create a view for easy log analysis
CREATE OR REPLACE VIEW log_summary AS
SELECT 
    log_type,
    level,
    COUNT(*) as count,
    MAX(created_at) as latest_entry
FROM system_logs
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY log_type, level
ORDER BY count DESC;

-- Create a procedure to clean old logs
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanOldLogs(IN days_to_keep INT)
BEGIN
    DELETE FROM system_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SELECT ROW_COUNT() as deleted_rows;
END //

DELIMITER ;

-- Success message
SELECT 'System logs table created successfully with sample data!' as message;

-- Show current log summary
SELECT 'Current log summary:' as info;
SELECT * FROM log_summary;
