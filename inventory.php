<?php
/**
 * Inventory Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$product_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'update_stock' && $product_id) {
            $quantity = intval($_POST['quantity']);
            $movement_type = $_POST['movement_type']; // 'in' or 'out'
            $notes = sanitizeInput($_POST['notes']);
            
            // Get current stock
            $current_stock = $db->fetch("SELECT quantity_in_stock FROM inventory WHERE product_id = ?", [$product_id]);
            
            if ($current_stock) {
                $new_quantity = $current_stock['quantity_in_stock'];
                
                if ($movement_type === 'in') {
                    $new_quantity += $quantity;
                } elseif ($movement_type === 'out') {
                    if ($quantity > $current_stock['quantity_in_stock']) {
                        throw new Exception("Cannot remove more stock than available");
                    }
                    $new_quantity -= $quantity;
                } elseif ($movement_type === 'adjustment') {
                    $new_quantity = $quantity;
                }
                
                // Update inventory
                $db->query("UPDATE inventory SET quantity_in_stock = ? WHERE product_id = ?", [$new_quantity, $product_id]);
                
                // Record stock movement
                $db->query("
                    INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, notes, created_by) 
                    VALUES (?, ?, ?, 'adjustment', ?, 1)
                ", [$product_id, $movement_type, $quantity, $notes]);
                
                $success = "Stock updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'update_levels' && $product_id) {
            $min_level = intval($_POST['minimum_stock_level']);
            $max_level = intval($_POST['maximum_stock_level']);
            $reorder_point = intval($_POST['reorder_point']);
            $location = sanitizeInput($_POST['location']);
            $rack_number = sanitizeInput($_POST['rack_number']);
            
            $db->query("
                UPDATE inventory 
                SET minimum_stock_level = ?, maximum_stock_level = ?, reorder_point = ?, location = ?, rack_number = ?
                WHERE product_id = ?
            ", [$min_level, $max_level, $reorder_point, $location, $rack_number, $product_id]);
            
            $success = "Stock levels updated successfully!";
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get inventory list with filters
if ($action === 'list') {
    $filter = $_GET['filter'] ?? '';
    $search = $_GET['search'] ?? '';
    
    $where_conditions = ["p.is_active = 1"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(p.product_name LIKE ? OR p.product_code LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($filter === 'low_stock') {
        $where_conditions[] = "i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0";
    } elseif ($filter === 'out_of_stock') {
        $where_conditions[] = "i.quantity_in_stock = 0";
    } elseif ($filter === 'overstocked') {
        $where_conditions[] = "i.quantity_in_stock > i.maximum_stock_level AND i.maximum_stock_level > 0";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $inventory = $db->fetchAll("
        SELECT p.*, c.category_name, i.*,
               CASE 
                   WHEN i.quantity_in_stock = 0 THEN 'out_of_stock'
                   WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 'low_stock'
                   WHEN i.quantity_in_stock > i.maximum_stock_level AND i.maximum_stock_level > 0 THEN 'overstocked'
                   ELSE 'normal'
               END as stock_status
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE $where_clause
        ORDER BY 
            CASE 
                WHEN i.quantity_in_stock = 0 THEN 1
                WHEN i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 THEN 2
                ELSE 3
            END,
            p.product_name
    ", $params);
}

// Get single product for stock update
if (($action === 'update_stock' || $action === 'update_levels') && $product_id) {
    $product = $db->fetch("
        SELECT p.*, c.category_name, i.*
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.id = ? AND p.is_active = 1
    ", [$product_id]);
    
    if (!$product) {
        $error = "Product not found!";
        $action = 'list';
    }
}

// Get stock movement history for a product
if ($action === 'history' && $product_id) {
    $product = $db->fetch("SELECT product_name, product_code FROM products WHERE id = ?", [$product_id]);
    $movements = $db->fetchAll("
        SELECT sm.*, u.full_name as created_by_name
        FROM stock_movements sm
        LEFT JOIN users u ON sm.created_by = u.id
        WHERE sm.product_id = ?
        ORDER BY sm.created_at DESC
        LIMIT 50
    ", [$product_id]);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['bulk_update_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['bulk_update_type'] === 'success' ? 'success' : ($_SESSION['bulk_update_type'] === 'warning' ? 'warning' : 'danger'); ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $_SESSION['bulk_update_type'] === 'success' ? 'check-circle' : ($_SESSION['bulk_update_type'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
                <?php echo $_SESSION['bulk_update_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php
            unset($_SESSION['bulk_update_message']);
            unset($_SESSION['bulk_update_type']);
            ?>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Inventory List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Stock Management</h2>
                    <p class="text-muted mb-0">Monitor and manage your inventory levels</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="export-inventory.php?format=csv"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                            <li><a class="dropdown-item" href="export-inventory.php?format=excel"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                            <li><a class="dropdown-item" href="export-inventory.php?format=pdf"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-success" onclick="showBulkStockUpdate()">
                        <i class="fas fa-edit me-2"></i>Bulk Update
                    </button>
                </div>
            </div>

            <!-- Stock Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Out of Stock</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $out_of_stock = $db->fetch("SELECT COUNT(*) as count FROM inventory i JOIN products p ON i.product_id = p.id WHERE i.quantity_in_stock = 0 AND p.is_active = 1")['count'];
                                        echo $out_of_stock;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-circle fa-2x text-danger"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Low Stock</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $low_stock = $db->fetch("SELECT COUNT(*) as count FROM inventory i JOIN products p ON i.product_id = p.id WHERE i.quantity_in_stock <= i.minimum_stock_level AND i.minimum_stock_level > 0 AND p.is_active = 1")['count'];
                                        echo $low_stock;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Well Stocked</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $well_stocked = $db->fetch("SELECT COUNT(*) as count FROM inventory i JOIN products p ON i.product_id = p.id WHERE i.quantity_in_stock > i.minimum_stock_level AND p.is_active = 1")['count'];
                                        echo $well_stocked;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Products</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $total_products = $db->fetch("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'];
                                        echo $total_products;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-boxes fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" 
                                   placeholder="Search by name or code...">
                        </div>
                        <div class="col-md-3">
                            <label for="filter" class="form-label">Stock Status</label>
                            <select class="form-select" id="filter" name="filter">
                                <option value="">All Products</option>
                                <option value="out_of_stock" <?php echo ($_GET['filter'] ?? '') === 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                                <option value="low_stock" <?php echo ($_GET['filter'] ?? '') === 'low_stock' ? 'selected' : ''; ?>>Low Stock</option>
                                <option value="overstocked" <?php echo ($_GET['filter'] ?? '') === 'overstocked' ? 'selected' : ''; ?>>Overstocked</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="inventory.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Stock Update Modal -->
            <div class="modal fade" id="bulkUpdateModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Bulk Stock Update</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="bulkUpdateForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Update Type</label>
                                        <select class="form-select" id="bulkUpdateType" required>
                                            <option value="">Select Update Type</option>
                                            <option value="add">Add Stock</option>
                                            <option value="subtract">Subtract Stock</option>
                                            <option value="set">Set Stock Level</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-control" id="bulkQuantity" min="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="bulkNotes" rows="2" placeholder="Reason for stock update..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Select Products</label>
                                    <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                        <?php foreach ($inventory as $item): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="<?php echo $item['id']; ?>" id="product_<?php echo $item['id']; ?>">
                                                <label class="form-check-label" for="product_<?php echo $item['id']; ?>">
                                                    <?php echo htmlspecialchars($item['product_name']); ?>
                                                    <small class="text-muted">(Current: <?php echo $item['quantity_in_stock']; ?>)</small>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="processBulkUpdate()">Update Stock</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Current Stock</th>
                                    <th>Min/Max Levels</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($inventory)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4 text-muted">
                                            <i class="fas fa-boxes fa-3x mb-3 opacity-25"></i>
                                            <p>No inventory records found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($inventory as $item): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($item['product_name']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($item['product_code']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($item['category_name'] ?? 'Uncategorized'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="fs-5"><?php echo $item['quantity_in_stock'] ?? 0; ?></strong>
                                                <small class="text-muted d-block">units</small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    Min: <?php echo $item['minimum_stock_level'] ?? 0; ?><br>
                                                    Max: <?php echo $item['maximum_stock_level'] ?? 0; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($item['location']): ?>
                                                    <span class="badge bg-info">
                                                        <?php echo htmlspecialchars($item['location']); ?>
                                                        <?php if ($item['rack_number']): ?>
                                                            - <?php echo htmlspecialchars($item['rack_number']); ?>
                                                        <?php endif; ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'out_of_stock' => 'bg-danger',
                                                    'low_stock' => 'bg-warning',
                                                    'overstocked' => 'bg-info',
                                                    'normal' => 'bg-success'
                                                ];
                                                $status_text = [
                                                    'out_of_stock' => 'Out of Stock',
                                                    'low_stock' => 'Low Stock',
                                                    'overstocked' => 'Overstocked',
                                                    'normal' => 'Normal'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $status_class[$item['stock_status']]; ?>">
                                                    <?php echo $status_text[$item['stock_status']]; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=update_stock&id=<?php echo $item['id']; ?>" 
                                                       class="btn btn-outline-primary" title="Update Stock">
                                                        <i class="fas fa-plus-minus"></i>
                                                    </a>
                                                    <a href="?action=update_levels&id=<?php echo $item['id']; ?>" 
                                                       class="btn btn-outline-info" title="Set Levels">
                                                        <i class="fas fa-sliders-h"></i>
                                                    </a>
                                                    <a href="?action=history&id=<?php echo $item['id']; ?>" 
                                                       class="btn btn-outline-secondary" title="View History">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'update_stock'): ?>
            <!-- Update Stock Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Update Stock</h2>
                    <p class="text-muted mb-0"><?php echo htmlspecialchars($product['product_name']); ?> (<?php echo htmlspecialchars($product['product_code']); ?>)</p>
                </div>
                <a href="inventory.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Inventory
                </a>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Current Stock Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <h3 class="text-primary"><?php echo $product['quantity_in_stock'] ?? 0; ?></h3>
                                    <small class="text-muted">Current Stock</small>
                                </div>
                                <div class="col-4">
                                    <h3 class="text-warning"><?php echo $product['minimum_stock_level'] ?? 0; ?></h3>
                                    <small class="text-muted">Min Level</small>
                                </div>
                                <div class="col-4">
                                    <h3 class="text-success"><?php echo $product['maximum_stock_level'] ?? 0; ?></h3>
                                    <small class="text-muted">Max Level</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Stock Movement</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="movement_type" class="form-label">Movement Type *</label>
                                    <select class="form-select" id="movement_type" name="movement_type" required>
                                        <option value="">Select Movement Type</option>
                                        <option value="in">Stock In (Add Stock)</option>
                                        <option value="out">Stock Out (Remove Stock)</option>
                                        <option value="adjustment">Adjustment (Set Exact Quantity)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity *</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           min="1" required>
                                    <div class="form-text" id="quantityHelp"></div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="Reason for stock movement..."></textarea>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="inventory.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Stock
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Movements</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $recent_movements = $db->fetchAll("
                                SELECT * FROM stock_movements 
                                WHERE product_id = ? 
                                ORDER BY created_at DESC 
                                LIMIT 5
                            ", [$product_id]);
                            ?>
                            
                            <?php if (empty($recent_movements)): ?>
                                <p class="text-muted text-center">No recent movements</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recent_movements as $movement): ?>
                                        <div class="list-group-item px-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-<?php echo $movement['movement_type'] === 'in' ? 'success' : ($movement['movement_type'] === 'out' ? 'danger' : 'info'); ?>">
                                                        <?php echo strtoupper($movement['movement_type']); ?>
                                                    </span>
                                                    <span class="ms-2"><?php echo $movement['quantity']; ?> units</span>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo formatDateTime($movement['created_at']); ?>
                                                </small>
                                            </div>
                                            <?php if ($movement['notes']): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars($movement['notes']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'update_levels'): ?>
            <!-- Update Stock Levels Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Update Stock Levels</h2>
                    <p class="text-muted mb-0"><?php echo htmlspecialchars($product['product_name']); ?> (<?php echo htmlspecialchars($product['product_code']); ?>)</p>
                </div>
                <a href="inventory.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Inventory
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Stock Level Configuration</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="minimum_stock_level" class="form-label">Minimum Stock Level</label>
                                        <input type="number" class="form-control" id="minimum_stock_level" name="minimum_stock_level" 
                                               value="<?php echo $product['minimum_stock_level'] ?? 0; ?>" min="0">
                                        <div class="form-text">Alert when stock falls below this level</div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="maximum_stock_level" class="form-label">Maximum Stock Level</label>
                                        <input type="number" class="form-control" id="maximum_stock_level" name="maximum_stock_level" 
                                               value="<?php echo $product['maximum_stock_level'] ?? 0; ?>" min="0">
                                        <div class="form-text">Maximum recommended stock</div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="reorder_point" class="form-label">Reorder Point</label>
                                        <input type="number" class="form-control" id="reorder_point" name="reorder_point" 
                                               value="<?php echo $product['reorder_point'] ?? 0; ?>" min="0">
                                        <div class="form-text">When to reorder stock</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="location" class="form-label">Storage Location</label>
                                        <input type="text" class="form-control" id="location" name="location" 
                                               value="<?php echo htmlspecialchars($product['location'] ?? ''); ?>" 
                                               placeholder="e.g., Warehouse A, Section 1">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="rack_number" class="form-label">Rack/Shelf Number</label>
                                        <input type="text" class="form-control" id="rack_number" name="rack_number" 
                                               value="<?php echo htmlspecialchars($product['rack_number'] ?? ''); ?>" 
                                               placeholder="e.g., R1-S3, Shelf A2">
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="inventory.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Levels
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Current Status</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-primary mb-3"><?php echo $product['quantity_in_stock'] ?? 0; ?></h2>
                            <p class="text-muted">Current Stock Level</p>
                            
                            <?php
                            $current = $product['quantity_in_stock'] ?? 0;
                            $min = $product['minimum_stock_level'] ?? 0;
                            $max = $product['maximum_stock_level'] ?? 0;
                            
                            if ($current == 0) {
                                $status = 'Out of Stock';
                                $status_class = 'danger';
                            } elseif ($min > 0 && $current <= $min) {
                                $status = 'Low Stock';
                                $status_class = 'warning';
                            } elseif ($max > 0 && $current > $max) {
                                $status = 'Overstocked';
                                $status_class = 'info';
                            } else {
                                $status = 'Normal';
                                $status_class = 'success';
                            }
                            ?>
                            
                            <span class="badge bg-<?php echo $status_class; ?> fs-6">
                                <?php echo $status; ?>
                            </span>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Stock Level Tips</h6>
                                <ul class="mb-0 small">
                                    <li>Set minimum level based on lead time</li>
                                    <li>Maximum level should consider storage capacity</li>
                                    <li>Reorder point = Min level + Safety stock</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'history'): ?>
            <!-- Stock Movement History -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Stock Movement History</h2>
                    <p class="text-muted mb-0"><?php echo htmlspecialchars($product['product_name']); ?> (<?php echo htmlspecialchars($product['product_code']); ?>)</p>
                </div>
                <a href="inventory.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Inventory
                </a>
            </div>

            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Movement Type</th>
                                    <th>Quantity</th>
                                    <th>Reference</th>
                                    <th>Notes</th>
                                    <th>Created By</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($movements)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4 text-muted">
                                            <i class="fas fa-history fa-3x mb-3 opacity-25"></i>
                                            <p>No movement history found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($movements as $movement): ?>
                                        <tr>
                                            <td>
                                                <?php echo formatDateTime($movement['created_at']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $movement['movement_type'] === 'in' ? 'success' : ($movement['movement_type'] === 'out' ? 'danger' : 'info'); ?>">
                                                    <?php echo strtoupper($movement['movement_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo $movement['quantity']; ?></strong> units
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo ucfirst($movement['reference_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($movement['notes'] ?: '-'); ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($movement['created_by_name'] ?: 'System'); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Update quantity help text based on movement type
        document.getElementById('movement_type')?.addEventListener('change', function() {
            const quantityHelp = document.getElementById('quantityHelp');
            const currentStock = <?php echo $product['quantity_in_stock'] ?? 0; ?>;
            
            switch(this.value) {
                case 'in':
                    quantityHelp.textContent = 'Enter quantity to add to current stock';
                    quantityHelp.className = 'form-text text-success';
                    break;
                case 'out':
                    quantityHelp.textContent = `Enter quantity to remove (max: ${currentStock})`;
                    quantityHelp.className = 'form-text text-warning';
                    document.getElementById('quantity').max = currentStock;
                    break;
                case 'adjustment':
                    quantityHelp.textContent = 'Enter the exact quantity to set';
                    quantityHelp.className = 'form-text text-info';
                    document.getElementById('quantity').removeAttribute('max');
                    break;
                default:
                    quantityHelp.textContent = '';
                    quantityHelp.className = 'form-text';
            }
        });

        // Bulk update functions
        function showBulkStockUpdate() {
            const modal = new bootstrap.Modal(document.getElementById('bulkUpdateModal'));
            modal.show();
        }

        function processBulkUpdate() {
            const updateType = document.getElementById('bulkUpdateType').value;
            const quantity = document.getElementById('bulkQuantity').value;
            const notes = document.getElementById('bulkNotes').value;
            const selectedProducts = [];

            document.querySelectorAll('#bulkUpdateModal input[type="checkbox"]:checked').forEach(checkbox => {
                selectedProducts.push(checkbox.value);
            });

            if (!updateType || !quantity || selectedProducts.length === 0) {
                alert('Please fill all required fields and select at least one product');
                return;
            }

            if (confirm(`Are you sure you want to ${updateType} ${quantity} units for ${selectedProducts.length} selected products?`)) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'bulk-stock-update.php';

                // Add form data
                const formData = {
                    update_type: updateType,
                    quantity: quantity,
                    notes: notes,
                    products: selectedProducts
                };

                for (const [key, value] of Object.entries(formData)) {
                    if (Array.isArray(value)) {
                        value.forEach(item => {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = key + '[]';
                            input.value = item;
                            form.appendChild(input);
                        });
                    } else {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = key;
                        input.value = value;
                        form.appendChild(input);
                    }
                }

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
