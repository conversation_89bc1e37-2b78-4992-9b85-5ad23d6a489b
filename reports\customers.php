<?php
/**
 * Customer Reports - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

// Get filter parameters
$customer_type = $_GET['customer_type'] ?? '';
$credit_status = $_GET['credit_status'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'customer_name';

// Build WHERE clause
$where_conditions = ["c.is_active = 1"];
$params = [];

if ($customer_type) {
    $where_conditions[] = "c.customer_type = ?";
    $params[] = $customer_type;
}

if ($credit_status) {
    switch ($credit_status) {
        case 'has_outstanding':
            $where_conditions[] = "outstanding_amount > 0";
            break;
        case 'credit_limit_exceeded':
            $where_conditions[] = "outstanding_amount > c.credit_limit AND c.credit_limit > 0";
            break;
        case 'no_outstanding':
            $where_conditions[] = "outstanding_amount = 0";
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// Validate sort_by parameter
$allowed_sorts = ['customer_name', 'total_purchases', 'outstanding_amount', 'last_purchase_date'];
if (!in_array($sort_by, $allowed_sorts)) {
    $sort_by = 'customer_name';
}

try {
    // Get customer data with calculations
    $customers = $db->fetchAll("
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount,
               CASE 
                   WHEN (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') > c.credit_limit AND c.credit_limit > 0 THEN 'Exceeded'
                   WHEN (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') > 0 THEN 'Outstanding'
                   ELSE 'Clear'
               END as credit_status_text
        FROM customers c
        WHERE $where_clause
        ORDER BY $sort_by ASC
    ", $params);

    // Get summary statistics
    $summary = $db->fetch("
        SELECT 
            COUNT(*) as total_customers,
            COUNT(CASE WHEN (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) > 0 THEN 1 END) as active_customers,
            COALESCE(SUM((SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0)), 0) as total_sales_value,
            COALESCE(SUM((SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid')), 0) as total_outstanding,
            COUNT(CASE WHEN (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') > c.credit_limit AND c.credit_limit > 0 THEN 1 END) as credit_exceeded_count
        FROM customers c
        WHERE $where_clause
    ", $params);

} catch (Exception $e) {
    $error = "Error loading customer data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Reports - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="content-area">
                <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Customer Reports</h2>
                    <p class="text-muted mb-0">Analyze customer performance and credit status</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Customer Type</label>
                            <select class="form-select" name="customer_type">
                                <option value="">All Types</option>
                                <option value="retail" <?php echo $customer_type == 'retail' ? 'selected' : ''; ?>>Retail</option>
                                <option value="wholesale" <?php echo $customer_type == 'wholesale' ? 'selected' : ''; ?>>Wholesale</option>
                                <option value="distributor" <?php echo $customer_type == 'distributor' ? 'selected' : ''; ?>>Distributor</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Credit Status</label>
                            <select class="form-select" name="credit_status">
                                <option value="">All Status</option>
                                <option value="has_outstanding" <?php echo $credit_status == 'has_outstanding' ? 'selected' : ''; ?>>Has Outstanding</option>
                                <option value="credit_limit_exceeded" <?php echo $credit_status == 'credit_limit_exceeded' ? 'selected' : ''; ?>>Credit Limit Exceeded</option>
                                <option value="no_outstanding" <?php echo $credit_status == 'no_outstanding' ? 'selected' : ''; ?>>No Outstanding</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Sort By</label>
                            <select class="form-select" name="sort_by">
                                <option value="customer_name" <?php echo $sort_by == 'customer_name' ? 'selected' : ''; ?>>Customer Name</option>
                                <option value="total_purchases" <?php echo $sort_by == 'total_purchases' ? 'selected' : ''; ?>>Total Purchases</option>
                                <option value="outstanding_amount" <?php echo $sort_by == 'outstanding_amount' ? 'selected' : ''; ?>>Outstanding Amount</option>
                                <option value="last_purchase_date" <?php echo $sort_by == 'last_purchase_date' ? 'selected' : ''; ?>>Last Purchase</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>Apply Filters
                                </button>
                                <a href="customers.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $summary['total_customers']; ?></h3>
                            <small class="text-muted">Total Customers</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo $summary['active_customers']; ?></h3>
                            <small class="text-muted">Active Customers</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">₹<?php echo number_format($summary['total_sales_value'], 2); ?></h3>
                            <small class="text-muted">Total Sales</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">₹<?php echo number_format($summary['total_outstanding'], 2); ?></h3>
                            <small class="text-muted">Outstanding</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-danger"><?php echo $summary['credit_exceeded_count']; ?></h3>
                            <small class="text-muted">Credit Exceeded</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary">₹<?php echo $summary['active_customers'] > 0 ? number_format($summary['total_sales_value'] / $summary['active_customers'], 2) : '0.00'; ?></h3>
                            <small class="text-muted">Avg. Purchase</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Data -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Customer Details</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Contact</th>
                                    <th>Sales Count</th>
                                    <th>Total Purchases</th>
                                    <th>Outstanding</th>
                                    <th>Credit Limit</th>
                                    <th>Status</th>
                                    <th>Last Purchase</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($customers)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            No customers found for the selected criteria
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($customers as $customer): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($customer['customer_name']); ?></strong>
                                                <?php if ($customer['business_name']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($customer['business_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><span class="badge bg-secondary"><?php echo ucfirst($customer['customer_type']); ?></span></td>
                                            <td>
                                                <?php if ($customer['phone']): ?>
                                                    <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($customer['phone']); ?>
                                                <?php endif; ?>
                                                <?php if ($customer['email']): ?>
                                                    <br><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($customer['email']); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><span class="badge bg-info"><?php echo $customer['sales_count']; ?></span></td>
                                            <td><strong>₹<?php echo number_format($customer['total_purchases'], 2); ?></strong></td>
                                            <td>
                                                <?php if ($customer['outstanding_amount'] > 0): ?>
                                                    <strong class="text-warning">₹<?php echo number_format($customer['outstanding_amount'], 2); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-success">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>₹<?php echo number_format($customer['credit_limit'], 2); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                switch ($customer['credit_status_text']) {
                                                    case 'Exceeded':
                                                        $statusClass = 'bg-danger';
                                                        break;
                                                    case 'Outstanding':
                                                        $statusClass = 'bg-warning';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-success';
                                                }
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>">
                                                    <?php echo $customer['credit_status_text']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($customer['last_purchase_date']): ?>
                                                    <?php echo date('d/m/Y', strtotime($customer['last_purchase_date'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/app.js"></script>
    
    <script>
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open('export-customer-report.php?' + params.toString(), '_blank');
        }
    </script>
</body>
</html>
