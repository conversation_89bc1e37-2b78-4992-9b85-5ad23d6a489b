<?php
/**
 * User Details AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $user_id = $_GET['id'] ?? null;
    
    if (!$user_id) {
        throw new Exception('User ID required');
    }
    
    $db = getDB();
    
    // Get user details
    $user = $db->fetch("
        SELECT u.*,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as total_sales,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE created_by = u.id AND is_cancelled = 0) as total_revenue,
               (SELECT COUNT(*) FROM sales WHERE created_by = u.id) as total_activities,
               u.last_login,
               0 as today_activities
        FROM users u
        WHERE u.id = ? AND u.is_active = 1
    ", [$user_id]);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Get recent sales
    $recentSales = $db->fetchAll("
        SELECT s.*, c.customer_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.created_by = ? AND s.is_cancelled = 0
        ORDER BY s.created_at DESC
        LIMIT 5
    ", [$user_id]);
    
    // Get recent activities (simplified without system_logs)
    $recentActivities = [];
    
    // Get monthly performance
    $monthlyPerformance = $db->fetchAll("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as sales_count,
            COALESCE(SUM(grand_total), 0) as revenue
        FROM sales
        WHERE created_by = ? AND is_cancelled = 0
        AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
    ", [$user_id]);
    
    // Generate HTML response
    echo "<div class='row'>";
    
    // User Information
    echo "<div class='col-md-6 mb-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h6 class='mb-0'><i class='fas fa-user me-2'></i>User Information</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='text-center mb-3'>";
    echo "<div class='avatar-circle mx-auto mb-2' style='width: 80px; height: 80px; font-size: 32px;'>";
    echo "<i class='fas fa-user'></i>";
    echo "</div>";
    echo "<h5>" . htmlspecialchars($user['full_name']) . "</h5>";
    echo "<span class='badge bg-" . ($user['role'] === 'admin' ? 'danger' : ($user['role'] === 'manager' ? 'warning' : 'primary')) . "'>";
    echo ucfirst($user['role']);
    echo "</span>";
    echo "</div>";
    
    echo "<div class='row mb-2'>";
    echo "<div class='col-sm-4'><strong>Username:</strong></div>";
    echo "<div class='col-sm-8'>" . htmlspecialchars($user['username']) . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-2'>";
    echo "<div class='col-sm-4'><strong>Email:</strong></div>";
    echo "<div class='col-sm-8'>" . htmlspecialchars($user['email']) . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-2'>";
    echo "<div class='col-sm-4'><strong>Phone:</strong></div>";
    echo "<div class='col-sm-8'>" . ($user['phone'] ? htmlspecialchars($user['phone']) : 'Not provided') . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-2'>";
    echo "<div class='col-sm-4'><strong>Joined:</strong></div>";
    echo "<div class='col-sm-8'>" . date('d/m/Y', strtotime($user['created_at'])) . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-2'>";
    echo "<div class='col-sm-4'><strong>Last Login:</strong></div>";
    echo "<div class='col-sm-8'>" . ($user['last_login'] ? date('d/m/Y H:i:s', strtotime($user['last_login'])) : 'Never') . "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Performance Statistics
    echo "<div class='col-md-6 mb-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h6 class='mb-0'><i class='fas fa-chart-bar me-2'></i>Performance Statistics</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='row text-center'>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-primary'>" . $user['total_sales'] . "</div>";
    echo "<small class='text-muted'>Total Sales</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-success'>₹" . number_format($user['total_revenue'], 2) . "</div>";
    echo "<small class='text-muted'>Total Revenue</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-info'>" . $user['total_activities'] . "</div>";
    echo "<small class='text-muted'>Total Activities</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-warning'>" . $user['today_activities'] . "</div>";
    echo "<small class='text-muted'>Today's Activities</small>";
    echo "</div>";
    echo "</div>";
    
    // Average performance
    if ($user['total_sales'] > 0) {
        $avgSaleValue = $user['total_revenue'] / $user['total_sales'];
        echo "<div class='alert alert-info'>";
        echo "<strong>Average Sale Value:</strong> ₹" . number_format($avgSaleValue, 2);
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    // Recent Sales
    if (!empty($recentSales)) {
        echo "<div class='row'>";
        echo "<div class='col-12 mb-4'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='fas fa-shopping-cart me-2'></i>Recent Sales</h6>";
        echo "</div>";
        echo "<div class='card-body p-0'>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-hover mb-0'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>Sale ID</th>";
        echo "<th>Customer</th>";
        echo "<th>Amount</th>";
        echo "<th>Date</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($recentSales as $sale) {
            echo "<tr>";
            echo "<td><strong>#" . $sale['id'] . "</strong></td>";
            echo "<td>" . ($sale['customer_name'] ? htmlspecialchars($sale['customer_name']) : 'Walk-in Customer') . "</td>";
            echo "<td>₹" . number_format($sale['grand_total'], 2) . "</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($sale['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Monthly Performance
    if (!empty($monthlyPerformance)) {
        echo "<div class='row'>";
        echo "<div class='col-md-6 mb-4'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='fas fa-calendar me-2'></i>Monthly Performance</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        foreach ($monthlyPerformance as $month) {
            $monthName = date('M Y', strtotime($month['month'] . '-01'));
            echo "<div class='d-flex justify-content-between align-items-center mb-2'>";
            echo "<div>";
            echo "<strong>$monthName</strong><br>";
            echo "<small class='text-muted'>" . $month['sales_count'] . " sales</small>";
            echo "</div>";
            echo "<div class='text-end'>";
            echo "<strong>₹" . number_format($month['revenue'], 2) . "</strong>";
            echo "</div>";
            echo "</div>";
            echo "<hr>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // Recent Activities
        echo "<div class='col-md-6 mb-4'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='fas fa-history me-2'></i>Recent Activities</h6>";
        echo "</div>";
        echo "<div class='card-body' style='max-height: 300px; overflow-y: auto;'>";
        
        if (!empty($recentActivities)) {
            foreach ($recentActivities as $activity) {
                $levelClass = $activity['level'] === 'error' ? 'danger' : ($activity['level'] === 'warning' ? 'warning' : 'info');
                echo "<div class='d-flex align-items-start mb-2'>";
                echo "<span class='badge bg-$levelClass me-2'>" . ucfirst($activity['log_type']) . "</span>";
                echo "<div class='flex-grow-1'>";
                echo "<div class='small'>" . htmlspecialchars($activity['message']) . "</div>";
                echo "<small class='text-muted'>" . date('d/m/Y H:i:s', strtotime($activity['created_at'])) . "</small>";
                echo "</div>";
                echo "</div>";
            }
        } else {
            echo "<p class='text-muted text-center'>No recent activities</p>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Action buttons
    echo "<div class='text-center mt-4'>";
    echo "<a href='users.php?action=edit&id=" . $user['id'] . "' class='btn btn-primary' target='_blank'>";
    echo "<i class='fas fa-edit me-2'></i>Edit User";
    echo "</a>";
    
    echo "<button class='btn btn-warning ms-2' onclick='resetUserPassword(" . $user['id'] . ", \"" . htmlspecialchars($user['username']) . "\")'>";
    echo "<i class='fas fa-key me-2'></i>Reset Password";
    echo "</button>";
    
    echo "<button class='btn btn-outline-secondary ms-2' onclick='window.print()'>";
    echo "<i class='fas fa-print me-2'></i>Print Details";
    echo "</button>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
