<?php
/**
 * Export Sales Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Check if specific sales are selected
    $selectedSales = $_POST['selected_sales'] ?? [];
    
    $whereClause = "s.is_cancelled = 0";
    $params = [];
    
    if (!empty($selectedSales)) {
        $placeholders = str_repeat('?,', count($selectedSales) - 1) . '?';
        $whereClause .= " AND s.id IN ($placeholders)";
        $params = $selectedSales;
    }

    // Get sales data with comprehensive information
    $sales = $db->fetchAll("
        SELECT s.*, c.customer_name, c.business_name, c.phone as customer_phone,
               u.username as created_by_name,
               (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count,
               (SELECT GROUP_CONCAT(p.product_name SEPARATOR ', ') 
                FROM sale_items si 
                JOIN products p ON si.product_id = p.id 
                WHERE si.sale_id = s.id) as products,
               COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0) as paid_amount,
               (s.grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = s.id), 0)) as balance_amount
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        WHERE $whereClause
        ORDER BY s.sale_date DESC, s.sale_time DESC
    ", $params);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'sales_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Sale ID', 'Bill Number', 'Sale Date', 'Sale Time', 'Customer Name', 'Business Name',
            'Customer Phone', 'Item Count', 'Products', 'Subtotal (₹)', 'Discount Type', 'Discount Value',
            'Discount Amount (₹)', 'Tax Amount (₹)', 'Grand Total (₹)', 'Payment Method', 'Payment Status',
            'Paid Amount (₹)', 'Balance Amount (₹)', 'Created By', 'Created At'
        ];

        fputcsv($output, $headers);

        foreach ($sales as $sale) {
            $row = [
                $sale['id'],
                $sale['bill_number'],
                date('d/m/Y', strtotime($sale['sale_date'])),
                $sale['sale_time'],
                $sale['customer_name'] ?? 'Walk-in Customer',
                $sale['business_name'] ?? '',
                $sale['customer_phone'] ?? '',
                $sale['item_count'],
                $sale['products'] ?? '',
                $sale['subtotal'],
                $sale['discount_type'],
                $sale['discount_value'],
                $sale['discount_amount'],
                $sale['tax_amount'],
                $sale['grand_total'],
                $sale['payment_method'],
                $sale['payment_status'],
                $sale['paid_amount'],
                $sale['balance_amount'],
                $sale['created_by_name'] ?? 'System',
                date('d/m/Y H:i:s', strtotime($sale['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'sales_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $totalSales = array_sum(array_column($sales, 'grand_total'));
        $totalPaid = array_sum(array_column($sales, 'paid_amount'));
        $totalOutstanding = array_sum(array_column($sales, 'balance_amount'));
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Sales Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; }
                .paid { color: #28a745; }
                .pending { color: #ffc107; }
                .overdue { color: #dc3545; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Sales Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>
            
            <div class='summary'>
                <h3>Summary</h3>
                <p><strong>Total Sales:</strong> " . count($sales) . " transactions</p>
                <p><strong>Total Amount:</strong> ₹" . number_format($totalSales, 2) . "</p>
                <p><strong>Total Paid:</strong> ₹" . number_format($totalPaid, 2) . "</p>
                <p><strong>Outstanding:</strong> ₹" . number_format($totalOutstanding, 2) . "</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Bill Number</th>
                        <th>Date</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Total Amount</th>
                        <th>Payment Status</th>
                        <th>Balance</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($sales as $sale) {
            $statusClass = '';
            switch ($sale['payment_status']) {
                case 'paid': $statusClass = 'paid'; break;
                case 'pending': $statusClass = 'pending'; break;
                case 'overdue': $statusClass = 'overdue'; break;
            }
            
            echo "<tr>
                    <td><strong>" . htmlspecialchars($sale['bill_number']) . "</strong></td>
                    <td>" . date('d/m/Y', strtotime($sale['sale_date'])) . "</td>
                    <td>
                        " . htmlspecialchars($sale['customer_name'] ?? 'Walk-in') . "
                        " . ($sale['business_name'] ? '<br><small>' . htmlspecialchars($sale['business_name']) . '</small>' : '') . "
                    </td>
                    <td>" . $sale['item_count'] . "</td>
                    <td>₹" . number_format($sale['grand_total'], 2) . "</td>
                    <td class='$statusClass'>" . ucfirst($sale['payment_status']) . "</td>
                    <td class='" . ($sale['balance_amount'] > 0 ? 'overdue' : 'paid') . "'>₹" . number_format($sale['balance_amount'], 2) . "</td>
                  </tr>";
        }
        
        echo "</tbody>
            </table>
            
            <script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'sales_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Sales Report</title>
        </head>
        <body>
            <table border='1'>
                <tr>
                    <th>Sale ID</th>
                    <th>Bill Number</th>
                    <th>Sale Date</th>
                    <th>Customer Name</th>
                    <th>Business Name</th>
                    <th>Item Count</th>
                    <th>Subtotal</th>
                    <th>Discount Amount</th>
                    <th>Tax Amount</th>
                    <th>Grand Total</th>
                    <th>Payment Method</th>
                    <th>Payment Status</th>
                    <th>Paid Amount</th>
                    <th>Balance Amount</th>
                </tr>";

        foreach ($sales as $sale) {
            echo "<tr>
                    <td>" . $sale['id'] . "</td>
                    <td>" . htmlspecialchars($sale['bill_number']) . "</td>
                    <td>" . date('d/m/Y', strtotime($sale['sale_date'])) . "</td>
                    <td>" . htmlspecialchars($sale['customer_name'] ?? 'Walk-in') . "</td>
                    <td>" . htmlspecialchars($sale['business_name'] ?? '') . "</td>
                    <td>" . $sale['item_count'] . "</td>
                    <td>" . $sale['subtotal'] . "</td>
                    <td>" . $sale['discount_amount'] . "</td>
                    <td>" . $sale['tax_amount'] . "</td>
                    <td>" . $sale['grand_total'] . "</td>
                    <td>" . ucfirst($sale['payment_method']) . "</td>
                    <td>" . ucfirst($sale['payment_status']) . "</td>
                    <td>" . $sale['paid_amount'] . "</td>
                    <td>" . $sale['balance_amount'] . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting sales: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='sales.php'>← Back to Sales</a></p>";
    echo "</div>";
}
?>
