<?php
/**
 * Comprehensive Page Testing Script
 * Tests all pages for database connectivity and functionality
 */

require_once 'config/database.php';

// Suppress output buffering for real-time results
ob_implicit_flush(true);
ob_end_flush();

echo "<html><head><title>System Test Results</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
h1, h2 { color: #333; }
.summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
</style></head><body>";

echo "<h1>🔧 Indian Jewellery Wholesale Management System v2.0</h1>";
echo "<h2>📋 Comprehensive System Test</h2>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function testResult($test, $status, $message, $details = '') {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    if ($status === 'success') $passedTests++;
    
    $testResults[] = [
        'test' => $test,
        'status' => $status,
        'message' => $message,
        'details' => $details
    ];
    
    $class = $status;
    echo "<div class='test-result $class'>";
    echo "<strong>$test:</strong> $message";
    if ($details) echo "<br><small>$details</small>";
    echo "</div>";
    flush();
}

// Test 1: Database Connection
echo "<h3>🗄️ Database Connectivity Tests</h3>";
try {
    $db = getDB();
    testResult("Database Connection", "success", "✅ Connected successfully");
    
    // Test basic queries
    $tables = $db->fetchAll("SHOW TABLES");
    testResult("Database Tables", "success", "✅ Found " . count($tables) . " tables", implode(', ', array_column($tables, array_keys($tables[0])[0])));
    
} catch (Exception $e) {
    testResult("Database Connection", "error", "❌ Connection failed", $e->getMessage());
}

// Test 2: Core Data Verification
echo "<h3>📊 Data Verification Tests</h3>";
try {
    $userCount = $db->fetch("SELECT COUNT(*) as count FROM users")['count'];
    $categoryCount = $db->fetch("SELECT COUNT(*) as count FROM categories")['count'];
    $settingsCount = $db->fetch("SELECT COUNT(*) as count FROM settings")['count'];
    $ratesCount = $db->fetch("SELECT COUNT(*) as count FROM metal_rates")['count'];
    
    testResult("Users Table", $userCount > 0 ? "success" : "warning", 
               $userCount > 0 ? "✅ $userCount users found" : "⚠️ No users found");
    testResult("Categories Table", $categoryCount > 0 ? "success" : "warning", 
               $categoryCount > 0 ? "✅ $categoryCount categories found" : "⚠️ No categories found");
    testResult("Settings Table", $settingsCount > 0 ? "success" : "warning", 
               $settingsCount > 0 ? "✅ $settingsCount settings found" : "⚠️ No settings found");
    testResult("Metal Rates Table", $ratesCount > 0 ? "success" : "warning", 
               $ratesCount > 0 ? "✅ $ratesCount rates found" : "⚠️ No rates found");
    
} catch (Exception $e) {
    testResult("Data Verification", "error", "❌ Data verification failed", $e->getMessage());
}

// Test 3: Page Functionality Tests
echo "<h3>🌐 Page Functionality Tests</h3>";

$pages = [
    'index.php' => 'Dashboard',
    'login.php' => 'Login Page',
    'products.php' => 'Products Management',
    'categories.php' => 'Categories Management',
    'inventory.php' => 'Inventory Management',
    'suppliers.php' => 'Suppliers Management',
    'customers.php' => 'Customers Management',
    'billing.php' => 'Billing/New Sale',
    'sales.php' => 'Sales History',
    'metal-rates.php' => 'Metal Rates',
    'settings.php' => 'System Settings',
    'users.php' => 'User Management',
    'backup.php' => 'Backup & Restore'
];

foreach ($pages as $page => $title) {
    try {
        // Capture any output/errors from the page
        ob_start();
        $errorBefore = error_get_last();
        
        // Include the page to test for PHP errors
        $pageContent = file_get_contents($page);
        
        if ($pageContent === false) {
            testResult($title, "error", "❌ File not found or not readable");
            continue;
        }
        
        // Check for basic PHP syntax by parsing
        if (strpos($pageContent, '<?php') !== false) {
            // Test if the page can be parsed without fatal errors
            $tempFile = tempnam(sys_get_temp_dir(), 'page_test_');
            file_put_contents($tempFile, $pageContent);
            
            $output = shell_exec("php -l $tempFile 2>&1");
            unlink($tempFile);
            
            if (strpos($output, 'No syntax errors') !== false) {
                testResult($title, "success", "✅ PHP syntax valid");
            } else {
                testResult($title, "error", "❌ PHP syntax error", $output);
                continue;
            }
        }
        
        // Check for database queries in the page
        $hasDbQueries = (strpos($pageContent, '$db->') !== false || 
                        strpos($pageContent, 'getDB()') !== false ||
                        strpos($pageContent, 'fetchAll') !== false ||
                        strpos($pageContent, 'fetch(') !== false);
        
        if ($hasDbQueries) {
            testResult($title, "info", "ℹ️ Contains database operations");
        }
        
        // Check for required includes
        $hasDbInclude = (strpos($pageContent, "require_once 'config/database.php'") !== false ||
                        strpos($pageContent, 'include') !== false);
        
        if ($hasDbInclude) {
            testResult($title, "success", "✅ Database configuration included");
        } else {
            testResult($title, "warning", "⚠️ No database include found");
        }
        
        ob_end_clean();
        
    } catch (Exception $e) {
        testResult($title, "error", "❌ Test failed", $e->getMessage());
        ob_end_clean();
    }
}

// Test 4: Specific Database Operations
echo "<h3>🔍 Database Operations Tests</h3>";

try {
    // Test SELECT operations
    $products = $db->fetchAll("SELECT COUNT(*) as count FROM products");
    testResult("Products Query", "success", "✅ Products table accessible");
    
    $customers = $db->fetchAll("SELECT COUNT(*) as count FROM customers");
    testResult("Customers Query", "success", "✅ Customers table accessible");
    
    $sales = $db->fetchAll("SELECT COUNT(*) as count FROM sales");
    testResult("Sales Query", "success", "✅ Sales table accessible");
    
    $inventory = $db->fetchAll("SELECT COUNT(*) as count FROM inventory");
    testResult("Inventory Query", "success", "✅ Inventory table accessible");
    
    // Test JOIN operations
    $joinTest = $db->fetchAll("
        SELECT p.product_name, c.category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LIMIT 1
    ");
    testResult("JOIN Operations", "success", "✅ Complex queries working");
    
} catch (Exception $e) {
    testResult("Database Operations", "error", "❌ Database operations failed", $e->getMessage());
}

// Test 5: Authentication System
echo "<h3>🔐 Authentication System Tests</h3>";

try {
    // Test user authentication functions
    $adminUser = $db->fetch("SELECT * FROM users WHERE username = 'admin'");
    if ($adminUser) {
        testResult("Admin User", "success", "✅ Default admin user exists");
        
        // Test password verification
        if (password_verify('admin123', $adminUser['password_hash'])) {
            testResult("Password Hash", "success", "✅ Password hashing working correctly");
        } else {
            testResult("Password Hash", "error", "❌ Password verification failed");
        }
    } else {
        testResult("Admin User", "error", "❌ Default admin user not found");
    }
    
} catch (Exception $e) {
    testResult("Authentication System", "error", "❌ Authentication test failed", $e->getMessage());
}

// Test 6: Business Logic Functions
echo "<h3>💼 Business Logic Tests</h3>";

try {
    // Test utility functions
    $currency = formatCurrency(1234.56);
    testResult("Currency Formatting", "success", "✅ Currency format: $currency");
    
    $weight = formatWeight(12.345);
    testResult("Weight Formatting", "success", "✅ Weight format: $weight");
    
    $billNumber = generateBillNumber();
    testResult("Bill Number Generation", "success", "✅ Generated: $billNumber");
    
    // Test validation functions
    $validEmail = validateEmail('<EMAIL>');
    $invalidEmail = validateEmail('invalid-email');
    testResult("Email Validation", $validEmail && !$invalidEmail ? "success" : "error", 
               $validEmail && !$invalidEmail ? "✅ Email validation working" : "❌ Email validation failed");
    
    $validPhone = validatePhone('9876543210');
    $invalidPhone = validatePhone('123456');
    testResult("Phone Validation", $validPhone && !$invalidPhone ? "success" : "error",
               $validPhone && !$invalidPhone ? "✅ Phone validation working" : "❌ Phone validation failed");
    
} catch (Exception $e) {
    testResult("Business Logic", "error", "❌ Business logic test failed", $e->getMessage());
}

// Summary
echo "<div class='summary'>";
echo "<h2>📋 Test Summary</h2>";
echo "<p><strong>Total Tests:</strong> $totalTests</p>";
echo "<p><strong>Passed:</strong> $passedTests</p>";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>";

if ($passedTests == $totalTests) {
    echo "<div class='test-result success'>";
    echo "<h3>🎉 ALL TESTS PASSED!</h3>";
    echo "<p>Your Indian Jewellery Wholesale Management System v2.0 is fully functional and ready for production use.</p>";
    echo "</div>";
} else {
    echo "<div class='test-result warning'>";
    echo "<h3>⚠️ SOME TESTS FAILED</h3>";
    echo "<p>Please review the failed tests above and fix any issues before using the system in production.</p>";
    echo "</div>";
}

echo "<h3>🚀 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>📊 Dashboard</a></li>";
echo "<li><a href='login.php'>🔑 Login</a></li>";
echo "<li><a href='products.php'>💎 Products</a></li>";
echo "<li><a href='customers.php'>👥 Customers</a></li>";
echo "<li><a href='billing.php'>🛒 New Sale</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
