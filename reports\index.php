<?php
/**
 * Reports Dashboard - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();

try {
    // Get quick stats for dashboard
    $stats = $db->fetch("
        SELECT 
            (SELECT COUNT(*) FROM sales WHERE is_cancelled = 0 AND sale_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as monthly_sales,
            (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE is_cancelled = 0 AND sale_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as monthly_revenue,
            (SELECT COUNT(*) FROM customers WHERE is_active = 1) as total_customers,
            (SELECT COUNT(*) FROM products WHERE is_active = 1) as total_products,
            (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE is_cancelled = 0 AND payment_status != 'paid') as total_outstanding
    ");
} catch (Exception $e) {
    $error = "Error loading dashboard data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="content-area">
                <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Reports Dashboard</h2>
                    <p class="text-muted mb-0">Access all business reports and analytics</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="scheduleReport()">
                        <i class="fas fa-clock me-2"></i>Schedule Reports
                    </button>
                    <button class="btn btn-primary" onclick="customReport()">
                        <i class="fas fa-chart-bar me-2"></i>Custom Report
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $stats['monthly_sales'] ?? 0; ?></h3>
                            <small class="text-muted">Monthly Sales</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success">₹<?php echo number_format($stats['monthly_revenue'] ?? 0, 2); ?></h3>
                            <small class="text-muted">Monthly Revenue</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo $stats['total_customers'] ?? 0; ?></h3>
                            <small class="text-muted">Total Customers</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary"><?php echo $stats['total_products'] ?? 0; ?></h3>
                            <small class="text-muted">Total Products</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">₹<?php echo number_format($stats['total_outstanding'] ?? 0, 2); ?></h3>
                            <small class="text-muted">Outstanding</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-dark"><?php echo date('M Y'); ?></h3>
                            <small class="text-muted">Current Period</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="row">
                <!-- Sales Reports -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Sales Reports</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Analyze sales performance, trends, and customer behavior</p>
                            <div class="d-grid gap-2">
                                <a href="sales.php" class="btn btn-outline-primary">
                                    <i class="fas fa-shopping-cart me-2"></i>Sales Summary Report
                                </a>
                                <a href="sales.php?report_type=daily" class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-day me-2"></i>Daily Sales Report
                                </a>
                                <a href="sales.php?report_type=monthly" class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-alt me-2"></i>Monthly Sales Report
                                </a>
                                <a href="sales.php?report_type=customer_wise" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-2"></i>Customer-wise Sales
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Reports -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Financial Reports</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Monitor revenue, profit margins, and financial health</p>
                            <div class="d-grid gap-2">
                                <a href="financial.php" class="btn btn-outline-success">
                                    <i class="fas fa-chart-pie me-2"></i>Financial Summary
                                </a>
                                <a href="financial.php?report_type=profit_loss" class="btn btn-outline-success">
                                    <i class="fas fa-balance-scale me-2"></i>Profit & Loss Statement
                                </a>
                                <a href="financial.php?report_type=cash_flow" class="btn btn-outline-success">
                                    <i class="fas fa-money-bill-wave me-2"></i>Cash Flow Report
                                </a>
                                <a href="financial.php?report_type=outstanding" class="btn btn-outline-success">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Outstanding Payments
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Reports -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>Inventory Reports</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Track stock levels, inventory value, and product performance</p>
                            <div class="d-grid gap-2">
                                <a href="inventory.php" class="btn btn-outline-info">
                                    <i class="fas fa-warehouse me-2"></i>Inventory Summary
                                </a>
                                <a href="inventory.php?stock_status=low_stock" class="btn btn-outline-info">
                                    <i class="fas fa-exclamation-circle me-2"></i>Low Stock Alert
                                </a>
                                <a href="inventory.php?stock_status=out_of_stock" class="btn btn-outline-info">
                                    <i class="fas fa-times-circle me-2"></i>Out of Stock Items
                                </a>
                                <a href="inventory.php?report_type=valuation" class="btn btn-outline-info">
                                    <i class="fas fa-calculator me-2"></i>Inventory Valuation
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Reports -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-user-friends me-2"></i>Customer Reports</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Analyze customer behavior, credit status, and loyalty</p>
                            <div class="d-grid gap-2">
                                <a href="customers.php" class="btn btn-outline-warning">
                                    <i class="fas fa-address-book me-2"></i>Customer Summary
                                </a>
                                <a href="customers.php?credit_status=has_outstanding" class="btn btn-outline-warning">
                                    <i class="fas fa-credit-card me-2"></i>Outstanding Customers
                                </a>
                                <a href="customers.php?sort_by=total_purchases" class="btn btn-outline-warning">
                                    <i class="fas fa-trophy me-2"></i>Top Customers
                                </a>
                                <a href="customers.php?report_type=loyalty" class="btn btn-outline-warning">
                                    <i class="fas fa-heart me-2"></i>Customer Loyalty
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="generateDailyReport()">
                                <i class="fas fa-calendar-day me-2"></i>Today's Report
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="generateWeeklyReport()">
                                <i class="fas fa-calendar-week me-2"></i>Weekly Report
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info w-100 mb-2" onclick="generateMonthlyReport()">
                                <i class="fas fa-calendar-alt me-2"></i>Monthly Report
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100 mb-2" onclick="exportAllReports()">
                                <i class="fas fa-download me-2"></i>Export All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/app.js"></script>
    
    <script>
        function generateDailyReport() {
            const today = new Date().toISOString().split('T')[0];
            window.open(`sales.php?date_from=${today}&date_to=${today}`, '_blank');
        }

        function generateWeeklyReport() {
            const today = new Date();
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const dateFrom = weekAgo.toISOString().split('T')[0];
            const dateTo = today.toISOString().split('T')[0];
            window.open(`sales.php?date_from=${dateFrom}&date_to=${dateTo}`, '_blank');
        }

        function generateMonthlyReport() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const dateFrom = firstDay.toISOString().split('T')[0];
            const dateTo = today.toISOString().split('T')[0];
            window.open(`financial.php?date_from=${dateFrom}&date_to=${dateTo}`, '_blank');
        }

        function scheduleReport() {
            alert('Report scheduling functionality would be implemented here');
        }

        function customReport() {
            alert('Custom report builder would be implemented here');
        }

        function exportAllReports() {
            alert('Bulk export functionality would be implemented here');
        }
    </script>
</body>
</html>
