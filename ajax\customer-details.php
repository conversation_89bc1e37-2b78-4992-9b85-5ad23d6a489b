<?php
/**
 * Customer Details AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $customer_id = $_GET['id'] ?? null;
    
    if (!$customer_id) {
        throw new Exception('Customer ID required');
    }
    
    $db = getDB();
    
    // Get customer details with comprehensive information
    $customer = $db->fetch("
        SELECT c.*,
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(balance_amount), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE c.id = ? AND c.is_active = 1
    ", [$customer_id]);
    
    if (!$customer) {
        throw new Exception('Customer not found');
    }
    
    // Format the response
    echo json_encode([
        'success' => true,
        'customer' => [
            'id' => $customer['id'],
            'customer_name' => $customer['customer_name'],
            'business_name' => $customer['business_name'],
            'customer_type' => ucfirst($customer['customer_type']),
            'phone' => $customer['phone'],
            'email' => $customer['email'],
            'address' => $customer['address'],
            'city' => $customer['city'],
            'state' => $customer['state'],
            'pincode' => $customer['pincode'],
            'gst_number' => $customer['gst_number'],
            'credit_limit' => $customer['credit_limit'],
            'credit_days' => $customer['credit_days'],
            'discount_percentage' => $customer['discount_percentage'],
            'sales_count' => $customer['sales_count'],
            'total_purchases' => $customer['total_purchases'],
            'last_purchase_date' => $customer['last_purchase_date'] ? date('d/m/Y', strtotime($customer['last_purchase_date'])) : null,
            'outstanding_amount' => $customer['outstanding_amount']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
