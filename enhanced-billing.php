<?php
/**
 * Enhanced Billing with Tunch Calculator Integration
 * Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';
require_once 'lib/TunchCalculator.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$calculator = new TunchCalculator($db);

// Get customers for dropdown
$customers = $db->fetchAll("SELECT id, customer_name, business_name, phone, gst_number FROM customers WHERE is_active = 1 ORDER BY customer_name");

// Get products with Indian jewellery data
$products = $db->fetchAll("
    SELECT p.*, c.category_name, i.quantity_in_stock, i.selling_price,
           p.gross_weight, p.stone_weight, p.net_weight, p.tunch_percentage,
           p.making_charges_per_gram, p.wastage_percentage, p.hallmark_charges
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    LEFT JOIN inventory i ON p.id = i.product_id 
    WHERE p.is_active = 1 AND p.gross_weight > 0
    ORDER BY p.product_name
");

// Get current daily rates
$dailyRates = $db->fetchAll("
    SELECT metal_type, purity, base_rate, tunch_rate 
    FROM daily_rates 
    WHERE rate_date = CURDATE() AND is_active = 1 
    ORDER BY metal_type, purity
");

$ratesByMetal = [];
foreach ($dailyRates as $rate) {
    $ratesByMetal[$rate['metal_type']][$rate['purity']] = [
        'base_rate' => $rate['base_rate'],
        'tunch_rate' => $rate['tunch_rate']
    ];
}

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'create_sale') {
    try {
        $db->beginTransaction();
        
        // Generate bill number
        $bill_number = generateBillNumber();
        
        // Calculate totals
        $items = json_decode($_POST['items'], true);
        $subtotal = 0;
        $total_metal_value = 0;
        $total_making_charges = 0;
        $total_stone_charges = 0;
        $total_wastage = 0;
        
        // Process each item with Tunch Calculator
        $processed_items = [];
        foreach ($items as $item) {
            // Get product details
            $product = $db->fetch("
                SELECT * FROM products 
                WHERE id = ?
            ", [$item['product_id']]);
            
            // Get current rate for this product
            $rate = $calculator->getDailyRate($product['metal_type'], $product['purity']);
            if (!$rate) {
                throw new Exception("Daily rate not found for {$product['metal_type']} {$product['purity']}");
            }
            
            // Calculate using Tunch formula
            $calculation = $calculator->calculateTunch(
                $product['gross_weight'] * $item['quantity'],
                $product['stone_weight'] * $item['quantity'],
                $product['tunch_percentage'],
                $rate['base_rate'],
                $product['making_charges_per_gram'] * $product['net_weight'] * $item['quantity'],
                floatval($item['stone_charges'] ?? 0),
                $product['wastage_percentage']
            );
            
            // Store calculation details
            $processed_item = [
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'gross_weight' => $calculation['gross_weight'],
                'stone_weight' => $calculation['stone_weight'],
                'net_weight' => $calculation['net_weight'],
                'tunch_weight' => $calculation['tunch_weight'],
                'tunch_rate' => $calculation['tunch_rate'],
                'metal_value' => $calculation['metal_value'],
                'making_charges' => $calculation['making_charges'],
                'stone_charges' => $calculation['stone_charges'],
                'wastage_amount' => $calculation['wastage_amount'],
                'unit_price' => $calculation['final_value'] / $item['quantity'],
                'total_price' => $calculation['final_value']
            ];
            
            $processed_items[] = $processed_item;
            
            // Add to totals
            $subtotal += $calculation['final_value'];
            $total_metal_value += $calculation['metal_value'];
            $total_making_charges += $calculation['making_charges'];
            $total_stone_charges += $calculation['stone_charges'];
            $total_wastage += $calculation['wastage_amount'];
        }
        
        // Calculate discount and tax
        $discount_amount = 0;
        if ($_POST['discount_type'] === 'percentage') {
            $discount_amount = $subtotal * (floatval($_POST['discount_value']) / 100);
        } else {
            $discount_amount = floatval($_POST['discount_value']);
        }
        
        $tax_amount = ($subtotal - $discount_amount) * (floatval($_POST['tax_rate']) / 100);
        $grand_total = $subtotal - $discount_amount + $tax_amount;
        
        // Insert sale
        $sale_data = [
            'bill_number' => $bill_number,
            'customer_id' => $_POST['customer_id'] ?: null,
            'sale_date' => date('Y-m-d'),
            'sale_time' => date('H:i:s'),
            'total_items' => count($processed_items),
            'subtotal' => $subtotal,
            'discount_type' => $_POST['discount_type'],
            'discount_value' => floatval($_POST['discount_value']),
            'discount_amount' => $discount_amount,
            'tax_rate' => floatval($_POST['tax_rate']),
            'tax_amount' => $tax_amount,
            'grand_total' => $grand_total,
            'paid_amount' => floatval($_POST['paid_amount']),
            'balance_amount' => $grand_total - floatval($_POST['paid_amount']),
            'payment_method' => $_POST['payment_method'],
            'payment_status' => $_POST['payment_status'],
            'notes' => sanitizeInput($_POST['notes']),
            'created_by' => 1 // Current user ID
        ];
        
        $sql = "INSERT INTO sales (bill_number, customer_id, sale_date, sale_time, total_items, subtotal, discount_type, discount_value, discount_amount, tax_rate, tax_amount, grand_total, paid_amount, balance_amount, payment_method, payment_status, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $db->execute($sql, array_values($sale_data));
        $sale_id = $db->lastInsertId();
        
        // Insert sale items with Tunch calculations
        foreach ($processed_items as $item) {
            $item_data = [
                'sale_id' => $sale_id,
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'gross_weight' => $item['gross_weight'],
                'stone_weight' => $item['stone_weight'],
                'net_weight' => $item['net_weight'],
                'tunch_weight' => $item['tunch_weight'],
                'tunch_rate' => $item['tunch_rate'],
                'metal_value' => $item['metal_value'],
                'stone_charges' => $item['stone_charges'],
                'making_charges' => $item['making_charges'],
                'wastage_amount' => $item['wastage_amount'],
                'unit_price' => $item['unit_price'],
                'total_price' => $item['total_price']
            ];
            
            $sql = "INSERT INTO sale_items (sale_id, product_id, quantity, gross_weight, stone_weight, net_weight, tunch_weight, tunch_rate, metal_value, stone_charges, making_charges, wastage_amount, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $db->execute($sql, array_values($item_data));
            $sale_item_id = $db->lastInsertId();
            
            // Save calculation to audit trail
            $calculator->saveCalculation($sale_item_id, [
                'gross_weight' => $item['gross_weight'],
                'stone_weight' => $item['stone_weight'],
                'net_weight' => $item['net_weight'],
                'tunch_percentage' => $item['tunch_weight'] / $item['net_weight'] * 100,
                'tunch_weight' => $item['tunch_weight'],
                'base_rate' => $item['tunch_rate'] / ($item['tunch_weight'] / $item['net_weight']),
                'tunch_rate' => $item['tunch_rate'],
                'metal_value' => $item['metal_value'],
                'making_charges' => $item['making_charges'],
                'stone_charges' => $item['stone_charges'],
                'wastage_amount' => $item['wastage_amount'],
                'final_value' => $item['total_price']
            ]);
            
            // Update inventory
            $db->execute("UPDATE inventory SET quantity_in_stock = quantity_in_stock - ? WHERE product_id = ?", [$item['quantity'], $item['product_id']]);
            
            // Record stock movement
            $db->execute("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, created_by) VALUES (?, 'out', ?, 'sale', ?, 1)", [$item['product_id'], $item['quantity'], $sale_id]);
        }
        
        $db->commit();
        
        // Redirect to sales page with success message
        header("Location: sales.php?success=Sale created successfully with Tunch calculations&bill_number=" . urlencode($bill_number));
        exit;
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "Error creating sale: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Billing - Indian Jewellery System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .rates-display {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .rates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .rate-item {
            background: white;
            padding: 8px;
            border-radius: 3px;
            text-align: center;
            font-size: 12px;
        }
        .products-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s;
        }
        .product-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 5px rgba(0,123,255,0.2);
        }
        .product-card.selected {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .cart-summary {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        #selected-items {
            margin-top: 20px;
        }
        .item-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
            gap: 10px;
            align-items: center;
            padding: 10px;
            background: white;
            margin-bottom: 5px;
            border-radius: 3px;
        }
        .item-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏺 Enhanced Billing System</h1>
            <p>Indian Jewellery Wholesale with Tunch Calculator Integration</p>
        </div>

        <?php if (isset($error)): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <!-- Current Daily Rates -->
        <div class="rates-display">
            <h3>📈 Today's Metal Rates</h3>
            <div class="rates-grid">
                <?php foreach ($dailyRates as $rate): ?>
                <div class="rate-item">
                    <strong><?php echo $rate['metal_type']; ?> <?php echo $rate['purity']; ?></strong><br>
                    Base: ₹<?php echo number_format($rate['base_rate'], 0); ?><br>
                    Tunch: ₹<?php echo number_format($rate['tunch_rate'], 0); ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <form method="POST" id="billing-form">
            <input type="hidden" name="action" value="create_sale">
            <input type="hidden" name="items" id="items-json">

            <div class="form-grid">
                <!-- Customer Information -->
                <div>
                    <h3>👤 Customer Information</h3>
                    <div class="form-group">
                        <label for="customer_id">Customer:</label>
                        <select name="customer_id" id="customer_id">
                            <option value="">Walk-in Customer</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['id']; ?>">
                                <?php echo htmlspecialchars($customer['customer_name']); ?>
                                <?php if ($customer['business_name']): ?>
                                    - <?php echo htmlspecialchars($customer['business_name']); ?>
                                <?php endif; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Payment Information -->
                <div>
                    <h3>💰 Payment Information</h3>
                    <div class="form-group">
                        <label for="payment_method">Payment Method:</label>
                        <select name="payment_method" id="payment_method" required>
                            <option value="cash">Cash</option>
                            <option value="card">Card</option>
                            <option value="upi">UPI</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="cheque">Cheque</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="payment_status">Payment Status:</label>
                        <select name="payment_status" id="payment_status" required>
                            <option value="paid">Paid</option>
                            <option value="partial">Partial</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Product Selection -->
            <div class="products-section">
                <h3>💎 Select Jewellery Items</h3>
                <p>Click on products to add them to the bill. Tunch calculations will be applied automatically.</p>
                
                <div class="product-grid">
                    <?php foreach ($products as $product): ?>
                    <div class="product-card" onclick="addProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                        <h4><?php echo htmlspecialchars($product['product_name']); ?></h4>
                        <p><strong>Code:</strong> <?php echo $product['product_code']; ?></p>
                        <p><strong>Metal:</strong> <?php echo $product['metal_type']; ?> <?php echo $product['purity']; ?></p>
                        <p><strong>Weight:</strong> <?php echo $product['gross_weight']; ?>g (Net: <?php echo $product['net_weight']; ?>g)</p>
                        <p><strong>Tunch:</strong> <?php echo $product['tunch_percentage']; ?>%</p>
                        <p><strong>Stock:</strong> <?php echo $product['quantity_in_stock']; ?> pcs</p>
                        <p><strong>Price:</strong> ₹<?php echo number_format($product['selling_price'], 2); ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Selected Items -->
            <div id="selected-items">
                <h3>🛒 Selected Items</h3>
                <div class="item-row item-header">
                    <div>Product</div>
                    <div>Quantity</div>
                    <div>Weight</div>
                    <div>Rate</div>
                    <div>Amount</div>
                    <div>Action</div>
                </div>
                <div id="items-list"></div>
            </div>

            <!-- Billing Summary -->
            <div class="cart-summary">
                <div class="form-grid">
                    <div>
                        <h4>💰 Discount & Tax</h4>
                        <div class="form-group">
                            <label for="discount_type">Discount Type:</label>
                            <select name="discount_type" id="discount_type">
                                <option value="amount">Amount (₹)</option>
                                <option value="percentage">Percentage (%)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="discount_value">Discount Value:</label>
                            <input type="number" name="discount_value" id="discount_value" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label for="tax_rate">Tax Rate (%):</label>
                            <input type="number" name="tax_rate" id="tax_rate" step="0.01" value="3">
                        </div>
                    </div>
                    
                    <div>
                        <h4>💳 Payment Details</h4>
                        <div class="form-group">
                            <label for="paid_amount">Paid Amount:</label>
                            <input type="number" name="paid_amount" id="paid_amount" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="notes">Notes:</label>
                            <textarea name="notes" id="notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div id="bill-totals" style="background: white; padding: 15px; border-radius: 5px; margin-top: 15px;">
                    <h4>📊 Bill Summary</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <p><strong>Subtotal:</strong> <span id="subtotal">₹0.00</span></p>
                            <p><strong>Discount:</strong> <span id="discount-amount">₹0.00</span></p>
                            <p><strong>Tax:</strong> <span id="tax-amount">₹0.00</span></p>
                        </div>
                        <div>
                            <p style="font-size: 18px; color: #28a745;"><strong>Grand Total:</strong> <span id="grand-total">₹0.00</span></p>
                            <p><strong>Balance:</strong> <span id="balance-amount">₹0.00</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-success" id="create-bill-btn" disabled>
                    🧾 Create Bill with Tunch Calculations
                </button>
                <a href="sales.php" class="btn btn-primary">📋 View Sales</a>
            </div>
        </form>
    </div>

    <script>
        let selectedItems = [];
        let rates = <?php echo json_encode($ratesByMetal); ?>;

        function addProduct(product) {
            // Check if already added
            const existingIndex = selectedItems.findIndex(item => item.product_id === product.id);
            
            if (existingIndex >= 0) {
                selectedItems[existingIndex].quantity++;
            } else {
                selectedItems.push({
                    product_id: product.id,
                    product_name: product.product_name,
                    product_code: product.product_code,
                    metal_type: product.metal_type,
                    purity: product.purity,
                    gross_weight: parseFloat(product.gross_weight),
                    stone_weight: parseFloat(product.stone_weight),
                    net_weight: parseFloat(product.net_weight),
                    tunch_percentage: parseFloat(product.tunch_percentage),
                    making_charges_per_gram: parseFloat(product.making_charges_per_gram),
                    wastage_percentage: parseFloat(product.wastage_percentage),
                    quantity: 1,
                    stone_charges: 0,
                    unit_price: parseFloat(product.selling_price),
                    total_price: parseFloat(product.selling_price)
                });
            }
            
            updateItemsDisplay();
            updateTotals();
        }

        function removeItem(index) {
            selectedItems.splice(index, 1);
            updateItemsDisplay();
            updateTotals();
        }

        function updateQuantity(index, quantity) {
            if (quantity > 0) {
                selectedItems[index].quantity = parseInt(quantity);
                selectedItems[index].total_price = selectedItems[index].unit_price * quantity;
                updateTotals();
            }
        }

        function updateItemsDisplay() {
            const itemsList = document.getElementById('items-list');
            itemsList.innerHTML = '';
            
            selectedItems.forEach((item, index) => {
                const row = document.createElement('div');
                row.className = 'item-row';
                row.innerHTML = `
                    <div>
                        <strong>${item.product_name}</strong><br>
                        <small>${item.product_code} - ${item.metal_type} ${item.purity}</small><br>
                        <small>Gross: ${item.gross_weight}g, Net: ${item.net_weight}g, Tunch: ${item.tunch_percentage}%</small>
                    </div>
                    <div>
                        <input type="number" value="${item.quantity}" min="1" 
                               onchange="updateQuantity(${index}, this.value)" 
                               style="width: 60px; padding: 5px;">
                    </div>
                    <div>${(item.gross_weight * item.quantity).toFixed(3)}g</div>
                    <div>₹${item.unit_price.toLocaleString()}</div>
                    <div><strong>₹${item.total_price.toLocaleString()}</strong></div>
                    <div>
                        <button type="button" onclick="removeItem(${index})" 
                                style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                            Remove
                        </button>
                    </div>
                `;
                itemsList.appendChild(row);
            });
            
            document.getElementById('create-bill-btn').disabled = selectedItems.length === 0;
        }

        function updateTotals() {
            const subtotal = selectedItems.reduce((sum, item) => sum + item.total_price, 0);
            
            const discountType = document.getElementById('discount_type').value;
            const discountValue = parseFloat(document.getElementById('discount_value').value) || 0;
            
            let discountAmount = 0;
            if (discountType === 'percentage') {
                discountAmount = subtotal * (discountValue / 100);
            } else {
                discountAmount = discountValue;
            }
            
            const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
            const taxAmount = (subtotal - discountAmount) * (taxRate / 100);
            const grandTotal = subtotal - discountAmount + taxAmount;
            
            const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
            const balanceAmount = grandTotal - paidAmount;
            
            document.getElementById('subtotal').textContent = '₹' + subtotal.toLocaleString();
            document.getElementById('discount-amount').textContent = '₹' + discountAmount.toLocaleString();
            document.getElementById('tax-amount').textContent = '₹' + taxAmount.toLocaleString();
            document.getElementById('grand-total').textContent = '₹' + grandTotal.toLocaleString();
            document.getElementById('balance-amount').textContent = '₹' + balanceAmount.toLocaleString();
            
            // Update items JSON
            document.getElementById('items-json').value = JSON.stringify(selectedItems);
        }

        // Event listeners
        document.getElementById('discount_type').addEventListener('change', updateTotals);
        document.getElementById('discount_value').addEventListener('input', updateTotals);
        document.getElementById('tax_rate').addEventListener('input', updateTotals);
        document.getElementById('paid_amount').addEventListener('input', updateTotals);

        // Initialize
        updateItemsDisplay();
        updateTotals();
    </script>
</body>
</html>
