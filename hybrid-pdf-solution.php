<?php
/**
 * Hybrid PDF Solution - TCPDF + jsPDF Comparison and Implementation
 */

require_once 'config/database.php';

// Check available solutions
$tcpdfAvailable = file_exists('vendor/tecnickcom/tcpdf/tcpdf.php');
$enhancedPDFAvailable = file_exists('lib/EnhancedPDF.php');

try {
    $db = getDB();
    $sales = $db->fetchAll("SELECT id, bill_number, sale_date, grand_total FROM sales ORDER BY id DESC LIMIT 3");
} catch (Exception $e) {
    $sales = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hybrid PDF Solution - TCPDF vs jsPDF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .solution-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        .tcpdf-card { border-color: #28a745; background: #d4edda; }
        .jspdf-card { border-color: #007bff; background: #d1ecf1; }
        .recommended { border-color: #ffc107; background: #fff3cd; }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            border: none;
            cursor: pointer;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-info { background: #17a2b8; color: white; }
        .demo-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .pros { color: #28a745; }
        .cons { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background: #f8f9fa; font-weight: bold; }
    </style>
    <!-- Include jsPDF for demonstration -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <h1>🔧 Hybrid PDF Solution: TCPDF vs jsPDF</h1>
    <p>Comprehensive comparison and implementation of both PDF generation methods.</p>

    <!-- Quick Recommendation -->
    <div class="solution-card recommended">
        <h2>🏆 Recommendation for Jewellery Wholesale System</h2>
        <p><strong>Primary:</strong> TCPDF (Server-side) for business-critical invoices and bills</p>
        <p><strong>Secondary:</strong> jsPDF (Client-side) for quick previews and user-generated reports</p>
        <p><strong>Why:</strong> Security, reliability, and professional quality are essential for business documents.</p>
    </div>

    <!-- Side-by-side Comparison -->
    <div class="comparison-grid">
        <!-- TCPDF Card -->
        <div class="solution-card tcpdf-card">
            <h3>🖥️ TCPDF (Server-Side)</h3>
            <p><strong>Status:</strong> <?php echo $tcpdfAvailable ? '✅ Available' : '❌ Not Installed'; ?></p>
            
            <h4 class="pros">✅ Advantages:</h4>
            <ul>
                <li>Professional PDF quality</li>
                <li>Server-side security</li>
                <li>Database integration</li>
                <li>Reliable across all devices</li>
                <li>Advanced features (headers, footers)</li>
                <li>Optimized file sizes</li>
            </ul>
            
            <h4 class="cons">❌ Disadvantages:</h4>
            <ul>
                <li>Requires server setup</li>
                <li>PHP library installation needed</li>
                <li>Server processing load</li>
            </ul>

            <?php if ($tcpdfAvailable): ?>
            <a href="test-tcpdf.php?id=<?php echo $sales[0]['id'] ?? 1; ?>" target="_blank" class="btn btn-success">Test TCPDF</a>
            <?php else: ?>
            <a href="install-tcpdf.php" class="btn btn-warning">Install TCPDF</a>
            <?php endif; ?>
        </div>

        <!-- jsPDF Card -->
        <div class="solution-card jspdf-card">
            <h3>🌐 jsPDF (Client-Side)</h3>
            <p><strong>Status:</strong> ✅ Available (JavaScript)</p>
            
            <h4 class="pros">✅ Advantages:</h4>
            <ul>
                <li>No server setup required</li>
                <li>Instant client-side generation</li>
                <li>Easy JavaScript implementation</li>
                <li>No server processing load</li>
                <li>Interactive PDF creation</li>
            </ul>
            
            <h4 class="cons">❌ Disadvantages:</h4>
            <ul>
                <li>Limited PDF features</li>
                <li>Browser dependency</li>
                <li>Security concerns</li>
                <li>Larger file sizes</li>
                <li>Basic formatting only</li>
            </ul>

            <button onclick="generateJsPDF()" class="btn btn-primary">Test jsPDF</button>
        </div>
    </div>

    <!-- Feature Comparison Table -->
    <h2>📊 Detailed Feature Comparison</h2>
    <table>
        <thead>
            <tr>
                <th>Feature</th>
                <th>TCPDF</th>
                <th>jsPDF</th>
                <th>Winner</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><strong>PDF Quality</strong></td>
                <td class="pros">Excellent (Vector)</td>
                <td>Good (Basic)</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>Security</strong></td>
                <td class="pros">High (Server-side)</td>
                <td class="cons">Low (Client-side)</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>Setup Complexity</strong></td>
                <td class="cons">Moderate</td>
                <td class="pros">Easy</td>
                <td class="pros">jsPDF</td>
            </tr>
            <tr>
                <td><strong>Performance</strong></td>
                <td class="pros">Fast (Server)</td>
                <td>Moderate (Browser)</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>Reliability</strong></td>
                <td class="pros">Very High</td>
                <td class="cons">Browser Dependent</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>File Size</strong></td>
                <td class="pros">Optimized</td>
                <td class="cons">Larger</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>Advanced Features</strong></td>
                <td class="pros">Full Support</td>
                <td class="cons">Limited</td>
                <td class="pros">TCPDF</td>
            </tr>
            <tr>
                <td><strong>Database Integration</strong></td>
                <td class="pros">Direct Access</td>
                <td class="cons">Via AJAX</td>
                <td class="pros">TCPDF</td>
            </tr>
        </tbody>
    </table>

    <!-- Live Demo Area -->
    <div class="demo-area">
        <h2>🧪 Live Demo Comparison</h2>
        <p>Test both PDF generation methods with your sales data:</p>
        
        <?php if (count($sales) > 0): ?>
        <h3>Available Sales for Testing:</h3>
        <table>
            <tr>
                <th>Bill Number</th>
                <th>Date</th>
                <th>Amount</th>
                <th>TCPDF Test</th>
                <th>jsPDF Test</th>
            </tr>
            <?php foreach ($sales as $sale): ?>
            <tr>
                <td><?php echo htmlspecialchars($sale['bill_number']); ?></td>
                <td><?php echo date('d/m/Y', strtotime($sale['sale_date'])); ?></td>
                <td>₹<?php echo number_format($sale['grand_total'], 2); ?></td>
                <td>
                    <?php if ($tcpdfAvailable): ?>
                    <a href="test-tcpdf.php?id=<?php echo $sale['id']; ?>" target="_blank" class="btn btn-success">TCPDF</a>
                    <?php else: ?>
                    <span style="color: #999;">Not Available</span>
                    <?php endif; ?>
                </td>
                <td>
                    <button onclick="generateJsPDFForSale(<?php echo $sale['id']; ?>, '<?php echo htmlspecialchars($sale['bill_number']); ?>', '<?php echo date('d/m/Y', strtotime($sale['sale_date'])); ?>', <?php echo $sale['grand_total']; ?>)" class="btn btn-primary">jsPDF</button>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
        <?php else: ?>
        <p>No sales data available. <a href="billing.php">Create a sale</a> to test PDF generation.</p>
        <?php endif; ?>
    </div>

    <!-- Implementation Recommendation -->
    <div class="solution-card recommended">
        <h2>🎯 Implementation Strategy</h2>
        <h3>Recommended Approach:</h3>
        <ol>
            <li><strong>Primary:</strong> Use TCPDF for all business-critical documents (invoices, bills, reports)</li>
            <li><strong>Secondary:</strong> Use jsPDF for quick previews and user-generated content</li>
            <li><strong>Fallback:</strong> Enhanced HTML method for maximum compatibility</li>
        </ol>
        
        <h3>Why This Hybrid Approach?</h3>
        <ul>
            <li>✅ <strong>Security:</strong> Sensitive business data stays on server</li>
            <li>✅ <strong>Quality:</strong> Professional PDFs for customer-facing documents</li>
            <li>✅ <strong>Flexibility:</strong> Quick client-side generation when needed</li>
            <li>✅ <strong>Reliability:</strong> Multiple fallback options</li>
        </ul>

        <div style="text-align: center; margin: 20px 0;">
            <?php if (!$tcpdfAvailable): ?>
            <a href="install-tcpdf.php" class="btn btn-warning" style="font-size: 16px; padding: 15px 25px;">Install TCPDF (Recommended)</a>
            <?php endif; ?>
            <a href="test-pdf-generation.php" class="btn btn-info" style="font-size: 16px; padding: 15px 25px;">Test All Methods</a>
        </div>
    </div>

    <script>
        // jsPDF Demo Functions
        function generateJsPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Add content
            doc.setFontSize(20);
            doc.text('Sample Bill - jsPDF Demo', 20, 30);
            
            doc.setFontSize(12);
            doc.text('Indian Jewellery Wholesale System', 20, 50);
            doc.text('Bill Number: DEMO-001', 20, 60);
            doc.text('Date: ' + new Date().toLocaleDateString(), 20, 70);
            
            // Add a simple table
            doc.text('Items:', 20, 90);
            doc.text('1. Gold Ring - ₹15,000', 30, 100);
            doc.text('2. Silver Necklace - ₹5,000', 30, 110);
            
            doc.text('Total: ₹20,000', 20, 130);
            
            doc.text('Generated with jsPDF (Client-side)', 20, 150);
            
            // Save the PDF
            doc.save('jspdf-demo.pdf');
        }
        
        function generateJsPDFForSale(saleId, billNumber, date, amount) {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Add content
            doc.setFontSize(20);
            doc.text('Bill - ' + billNumber, 20, 30);
            
            doc.setFontSize(12);
            doc.text('Indian Jewellery Wholesale System', 20, 50);
            doc.text('Bill Number: ' + billNumber, 20, 60);
            doc.text('Date: ' + date, 20, 70);
            doc.text('Amount: ₹' + amount.toLocaleString(), 20, 80);
            
            doc.text('Note: This is a basic jsPDF demo.', 20, 100);
            doc.text('For full bill details, use TCPDF version.', 20, 110);
            
            doc.text('Generated with jsPDF (Client-side)', 20, 130);
            
            // Save the PDF
            doc.save('bill-' + billNumber + '-jspdf.pdf');
        }
    </script>
</body>
</html>
