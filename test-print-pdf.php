<?php
/**
 * Test Print and PDF Functionality
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>Testing Print and PDF Functionality</h2>";
    
    // Check if we have any sales
    $sales = $db->fetchAll("SELECT id, bill_number, sale_date, grand_total FROM sales ORDER BY id DESC LIMIT 5");
    
    if (count($sales) == 0) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>No Sales Found</h4>";
        echo "<p>No sales records found in the database. You need to create a sale first to test the print functionality.</p>";
        echo "<p><a href='billing.php' class='btn' style='background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Create New Sale</a></p>";
        echo "</div>";
    } else {
        echo "<h3>Available Sales for Testing</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Sale ID</th>";
        echo "<th style='padding: 10px;'>Bill Number</th>";
        echo "<th style='padding: 10px;'>Date</th>";
        echo "<th style='padding: 10px;'>Amount</th>";
        echo "<th style='padding: 10px;'>Actions</th>";
        echo "</tr>";
        
        foreach ($sales as $sale) {
            echo "<tr>";
            echo "<td style='padding: 10px; text-align: center;'>{$sale['id']}</td>";
            echo "<td style='padding: 10px;'>{$sale['bill_number']}</td>";
            echo "<td style='padding: 10px;'>" . date('d/m/Y', strtotime($sale['sale_date'])) . "</td>";
            echo "<td style='padding: 10px; text-align: right;'>₹" . number_format($sale['grand_total'], 2) . "</td>";
            echo "<td style='padding: 10px; text-align: center;'>";
            echo "<a href='print-bill.php?id={$sale['id']}' target='_blank' style='background-color: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>Print Bill</a> ";
            echo "<a href='generate-pdf.php?type=sale&id={$sale['id']}' target='_blank' style='background-color: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>Generate PDF</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ Test Instructions</h4>";
        echo "<ol>";
        echo "<li><strong>Print Bill:</strong> Click 'Print Bill' to open the printable bill view in a new tab</li>";
        echo "<li><strong>Generate PDF:</strong> Click 'Generate PDF' to open the PDF-ready view (use browser's Print > Save as PDF)</li>";
        echo "<li><strong>From Sales Page:</strong> Go to <a href='sales.php'>Sales Page</a> and use the print/PDF buttons there</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    // Test the files exist
    echo "<h3>File Status Check</h3>";
    $files_to_check = [
        'print-bill.php' => 'Print bill functionality',
        'generate-pdf.php' => 'PDF generation functionality'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 10px;'>File</th><th style='padding: 10px;'>Status</th><th style='padding: 10px;'>Description</th></tr>";
    
    foreach ($files_to_check as $file => $description) {
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>$file</strong></td>";
        if (file_exists($file)) {
            echo "<td style='padding: 10px; color: green;'>✅ EXISTS</td>";
        } else {
            echo "<td style='padding: 10px; color: red;'>❌ MISSING</td>";
        }
        echo "<td style='padding: 10px;'>$description</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // JavaScript functions test
    echo "<h3>JavaScript Functions Test</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p>Test the JavaScript functions used in the sales page:</p>";
    
    if (count($sales) > 0) {
        $test_sale_id = $sales[0]['id'];
        echo "<button onclick='printBill($test_sale_id)' style='background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; margin: 5px; cursor: pointer;'>Test Print Function</button>";
        echo "<button onclick='generatePDF($test_sale_id)' style='background-color: #28a745; color: white; padding: 10px 15px; border: none; border-radius: 4px; margin: 5px; cursor: pointer;'>Test PDF Function</button>";
    }
    echo "</div>";
    
    echo "<script>";
    echo "function printBill(saleId) {";
    echo "    window.open('print-bill.php?id=' + saleId, '_blank');";
    echo "}";
    echo "function generatePDF(saleId) {";
    echo "    window.open('generate-pdf.php?type=sale&id=' + saleId, '_blank');";
    echo "}";
    echo "</script>";
    
    // Additional notes
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📝 Implementation Notes</h4>";
    echo "<ul>";
    echo "<li><strong>Print Bill:</strong> Opens a printer-friendly HTML page with print styles</li>";
    echo "<li><strong>PDF Generation:</strong> Currently uses browser's built-in PDF generation (Print > Save as PDF)</li>";
    echo "<li><strong>For Production:</strong> Consider implementing a proper PDF library like TCPDF, mPDF, or DomPDF for better PDF control</li>";
    echo "<li><strong>Customization:</strong> Edit the print-bill.php and generate-pdf.php files to customize the bill layout</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
