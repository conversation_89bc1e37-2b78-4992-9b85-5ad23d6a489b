<?php
/**
 * Basic PDF Generator Class
 * A simplified PDF generator that creates actual PDF files
 */

class BasicPDF {
    private $pages = [];
    private $currentPage = 0;
    private $pageWidth = 210; // A4 width in mm
    private $pageHeight = 297; // A4 height in mm
    private $margin = 20;
    
    public function __construct() {
        $this->addPage();
    }
    
    public function addPage() {
        $this->pages[] = [
            "content" => "",
            "y" => $this->margin
        ];
        $this->currentPage = count($this->pages) - 1;
    }
    
    public function setFont($font, $style = "", $size = 12) {
        // Font setting (simplified)
        return true;
    }
    
    public function cell($width, $height, $text, $border = 0, $ln = 0, $align = "L") {
        $this->pages[$this->currentPage]["content"] .= $text . "\n";
        if ($ln) {
            $this->pages[$this->currentPage]["y"] += $height;
        }
    }
    
    public function ln($height = 5) {
        $this->pages[$this->currentPage]["y"] += $height;
    }
    
    public function output($filename = "", $mode = "I") {
        if ($mode == "D") {
            header("Content-Type: application/pdf");
            header("Content-Disposition: attachment; filename=\"$filename\"");
        } else {
            header("Content-Type: application/pdf");
        }
        
        // Generate basic PDF structure
        echo $this->generatePDF();
    }
    
    private function generatePDF() {
        // This is a very basic PDF structure
        // In a real implementation, you would use proper PDF formatting
        $content = "%PDF-1.4\n";
        $content .= "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
        $content .= "2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n";
        $content .= "3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n";
        
        $pageContent = "BT\n/F1 12 Tf\n50 750 Td\n";
        foreach ($this->pages as $page) {
            $lines = explode("\n", $page["content"]);
            $y = 750;
            foreach ($lines as $line) {
                if (trim($line)) {
                    $pageContent .= "($line) Tj\n0 -15 Td\n";
                    $y -= 15;
                }
            }
        }
        $pageContent .= "ET\n";
        
        $content .= "4 0 obj\n<< /Length " . strlen($pageContent) . " >>\nstream\n$pageContent\nendstream\nendobj\n";
        $content .= "xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000207 00000 n \n";
        $content .= "trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n" . (strlen($content) + 50) . "\n%%EOF";
        
        return $content;
    }
}
?>