<?php
/**
 * Product Code Generator for Indian Jewellery
 */

class ProductCodeGenerator {
    private $db;
    
    public function __construct($database = null) {
        $this->db = $database ?: getDB();
    }
    
    /**
     * Generate unique product code based on product type and category
     */
    public function generateProductCode($productName, $categoryId = null, $metalType = null) {
        // Determine product type prefix
        $prefix = $this->getProductPrefix($productName, $metalType);
        
        // Get next sequence number for this prefix
        $sequence = $this->getNextSequence($prefix);
        
        // Generate code: PREFIX + 3-digit sequence
        $code = $prefix . str_pad($sequence, 3, "0", STR_PAD_LEFT);
        
        // Ensure uniqueness
        while ($this->codeExists($code)) {
            $sequence++;
            $code = $prefix . str_pad($sequence, 3, "0", STR_PAD_LEFT);
        }
        
        return $code;
    }
    
    /**
     * Get product prefix based on name and metal type
     */
    private function getProductPrefix($productName, $metalType = null) {
        $name = strtolower($productName);
        $metal = strtolower($metalType ?: "");
        
        // Gold products
        if (strpos($metal, "gold") !== false || strpos($name, "gold") !== false) {
            if (strpos($name, "ring") !== false) return "GR";
            if (strpos($name, "necklace") !== false || strpos($name, "chain") !== false) return "GN";
            if (strpos($name, "earring") !== false) return "GE";
            if (strpos($name, "bracelet") !== false) return "GB";
            if (strpos($name, "pendant") !== false) return "GP";
            if (strpos($name, "bangle") !== false) return "GBG";
            return "GJ"; // General Gold Jewellery
        }
        
        // Silver products
        if (strpos($metal, "silver") !== false || strpos($name, "silver") !== false) {
            if (strpos($name, "ring") !== false) return "SR";
            if (strpos($name, "necklace") !== false || strpos($name, "chain") !== false) return "SN";
            if (strpos($name, "earring") !== false) return "SE";
            if (strpos($name, "bracelet") !== false) return "SB";
            if (strpos($name, "pendant") !== false) return "SP";
            if (strpos($name, "bangle") !== false) return "SBG";
            return "SJ"; // General Silver Jewellery
        }
        
        // Diamond/Platinum products
        if (strpos($name, "diamond") !== false) return "DJ";
        if (strpos($name, "platinum") !== false) return "PJ";
        
        // Default
        return "JW"; // Jewellery Wholesale
    }
    
    /**
     * Get next sequence number for prefix
     */
    private function getNextSequence($prefix) {
        try {
            $result = $this->db->fetch("
                SELECT MAX(CAST(SUBSTRING(product_code, 3) AS UNSIGNED)) as max_seq 
                FROM products 
                WHERE product_code LIKE ?
            ", [$prefix . "%"]);
            
            return ($result["max_seq"] ?? 0) + 1;
        } catch (Exception $e) {
            return 1;
        }
    }
    
    /**
     * Check if product code already exists
     */
    private function codeExists($code) {
        try {
            $result = $this->db->fetch("SELECT id FROM products WHERE product_code = ?", [$code]);
            return $result !== null;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Update all existing products with proper codes
     */
    public function updateAllProductCodes() {
        try {
            $products = $this->db->fetchAll("
                SELECT id, product_name, metal_type, category_id 
                FROM products 
                ORDER BY id
            ");
            
            $updated = 0;
            foreach ($products as $product) {
                $newCode = $this->generateProductCode(
                    $product["product_name"], 
                    $product["category_id"], 
                    $product["metal_type"]
                );
                
                $this->db->execute("UPDATE products SET product_code = ? WHERE id = ?", 
                    [$newCode, $product["id"]]);
                $updated++;
            }
            
            return $updated;
        } catch (Exception $e) {
            throw new Exception("Error updating product codes: " . $e->getMessage());
        }
    }
}
?>