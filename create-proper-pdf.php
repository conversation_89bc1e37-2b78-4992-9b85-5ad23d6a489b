<?php
/**
 * Create Proper PDF Generator
 */

echo "<h2>Setting Up Proper PDF Generation</h2>";

// Create lib directory if it doesn't exist
if (!is_dir('lib')) {
    mkdir('lib', 0755, true);
    echo "✅ Created lib directory<br>";
}

// Create a basic FPDF implementation (simplified)
$fpdfContent = '<?php
/**
 * Basic PDF Generator Class
 * A simplified PDF generator that creates actual PDF files
 */

class BasicPDF {
    private $pages = [];
    private $currentPage = 0;
    private $pageWidth = 210; // A4 width in mm
    private $pageHeight = 297; // A4 height in mm
    private $margin = 20;
    
    public function __construct() {
        $this->addPage();
    }
    
    public function addPage() {
        $this->pages[] = [
            "content" => "",
            "y" => $this->margin
        ];
        $this->currentPage = count($this->pages) - 1;
    }
    
    public function setFont($font, $style = "", $size = 12) {
        // Font setting (simplified)
        return true;
    }
    
    public function cell($width, $height, $text, $border = 0, $ln = 0, $align = "L") {
        $this->pages[$this->currentPage]["content"] .= $text . "\\n";
        if ($ln) {
            $this->pages[$this->currentPage]["y"] += $height;
        }
    }
    
    public function ln($height = 5) {
        $this->pages[$this->currentPage]["y"] += $height;
    }
    
    public function output($filename = "", $mode = "I") {
        if ($mode == "D") {
            header("Content-Type: application/pdf");
            header("Content-Disposition: attachment; filename=\"$filename\"");
        } else {
            header("Content-Type: application/pdf");
        }
        
        // Generate basic PDF structure
        echo $this->generatePDF();
    }
    
    private function generatePDF() {
        // This is a very basic PDF structure
        // In a real implementation, you would use proper PDF formatting
        $content = "%PDF-1.4\\n";
        $content .= "1 0 obj\\n<< /Type /Catalog /Pages 2 0 R >>\\nendobj\\n";
        $content .= "2 0 obj\\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\\nendobj\\n";
        $content .= "3 0 obj\\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\\nendobj\\n";
        
        $pageContent = "BT\\n/F1 12 Tf\\n50 750 Td\\n";
        foreach ($this->pages as $page) {
            $lines = explode("\\n", $page["content"]);
            $y = 750;
            foreach ($lines as $line) {
                if (trim($line)) {
                    $pageContent .= "($line) Tj\\n0 -15 Td\\n";
                    $y -= 15;
                }
            }
        }
        $pageContent .= "ET\\n";
        
        $content .= "4 0 obj\\n<< /Length " . strlen($pageContent) . " >>\\nstream\\n$pageContent\\nendstream\\nendobj\\n";
        $content .= "xref\\n0 5\\n0000000000 65535 f \\n0000000009 00000 n \\n0000000058 00000 n \\n0000000115 00000 n \\n0000000207 00000 n \\n";
        $content .= "trailer\\n<< /Size 5 /Root 1 0 R >>\\nstartxref\\n" . (strlen($content) + 50) . "\\n%%EOF";
        
        return $content;
    }
}
?>';

file_put_contents('lib/BasicPDF.php', $fpdfContent);
echo "✅ Created BasicPDF class<br>";

// Now create an enhanced PDF generator using TCPDF (if available) or fallback
$enhancedPdfContent = '<?php
/**
 * Enhanced PDF Generator
 * Uses TCPDF if available, otherwise falls back to HTML2PDF
 */

// Try to include TCPDF if it exists
$tcpdfPath = __DIR__ . "/../vendor/tecnickcom/tcpdf/tcpdf.php";
$tcpdfAvailable = file_exists($tcpdfPath);

if ($tcpdfAvailable) {
    require_once $tcpdfPath;
}

class EnhancedPDF {
    private $pdf;
    private $useTCPDF;
    
    public function __construct($orientation = "P", $unit = "mm", $format = "A4") {
        global $tcpdfAvailable;
        
        $this->useTCPDF = $tcpdfAvailable;
        
        if ($this->useTCPDF) {
            $this->pdf = new TCPDF($orientation, $unit, $format);
            $this->pdf->SetCreator("Indian Jewellery Wholesale System");
            $this->pdf->SetAuthor("System Generated");
            $this->pdf->SetTitle("Bill/Invoice");
            $this->pdf->SetMargins(15, 15, 15);
            $this->pdf->SetAutoPageBreak(TRUE, 15);
        }
    }
    
    public function addPage() {
        if ($this->useTCPDF) {
            $this->pdf->AddPage();
        }
    }
    
    public function setFont($family, $style = "", $size = 12) {
        if ($this->useTCPDF) {
            $this->pdf->SetFont($family, $style, $size);
        }
    }
    
    public function writeHTML($html) {
        if ($this->useTCPDF) {
            $this->pdf->writeHTML($html, true, false, true, false, "");
        }
    }
    
    public function output($filename = "document.pdf", $mode = "D") {
        if ($this->useTCPDF) {
            $this->pdf->Output($filename, $mode);
        } else {
            // Fallback to enhanced HTML method
            $this->outputHTML($filename, $mode);
        }
    }
    
    private function outputHTML($filename, $mode) {
        if ($mode == "D") {
            header("Content-Type: application/pdf");
            header("Content-Disposition: attachment; filename=\"$filename\"");
        }
        
        // Enhanced HTML with better PDF styling
        echo "<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <title>PDF Document</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .pdf-container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .totals-table {
            width: 300px;
            margin-left: auto;
            margin-top: 20px;
        }
        .totals-table td {
            border: none;
            padding: 5px 10px;
        }
        .grand-total {
            font-weight: bold;
            border-top: 2px solid #333;
        }
    </style>
</head>
<body>
    <div class=\"pdf-container\">
        <!-- Content will be inserted here -->
    </div>
    <script>
        // Auto-print for PDF generation
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>";
    }
    
    public function isUsingTCPDF() {
        return $this->useTCPDF;
    }
}
?>';

file_put_contents('lib/EnhancedPDF.php', $enhancedPdfContent);
echo "✅ Created EnhancedPDF class<br>";

echo "<h3>Testing PDF Libraries</h3>";

// Test if TCPDF is available
$tcpdfPath = 'vendor/tecnickcom/tcpdf/tcpdf.php';
if (file_exists($tcpdfPath)) {
    echo "✅ TCPDF is available<br>";
} else {
    echo "❌ TCPDF not found - using fallback method<br>";
}

// Create installation instructions
echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📋 Installation Options</h3>";
echo "<h4>Option 1: Install TCPDF via Composer (Recommended)</h4>";
echo "<code style='background-color: #f8f9fa; padding: 10px; display: block; border-radius: 3px; margin: 10px 0;'>composer require tecnickcom/tcpdf</code>";

echo "<h4>Option 2: Manual TCPDF Installation</h4>";
echo "<ol>";
echo "<li>Download TCPDF from <a href='https://tcpdf.org/'>https://tcpdf.org/</a></li>";
echo "<li>Extract to <code>vendor/tecnickcom/tcpdf/</code></li>";
echo "<li>Ensure <code>tcpdf.php</code> is at <code>vendor/tecnickcom/tcpdf/tcpdf.php</code></li>";
echo "</ol>";

echo "<h4>Option 3: Use Current Enhanced HTML Method</h4>";
echo "<p>The enhanced HTML method provides better PDF output than the basic browser print.</p>";
echo "</div>";

echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>✅ Next Steps</h3>";
echo "<ol>";
echo "<li><a href='update-pdf-generator.php'>Update PDF Generator Files</a></li>";
echo "<li><a href='test-pdf-generation.php'>Test New PDF Generation</a></li>";
echo "<li><a href='sales.php'>Test from Sales Page</a></li>";
echo "</ol>";
echo "</div>";
?>
