<?php
/**
 * Fast Add Product - Simplified Product Creation
 */

require_once 'config/database.php';

startSession();

$db = getDB();
$success = '';
$error = '';

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'add_product') {
    try {
        // Simple product code generation
        $prefix = '';
        $metal = strtolower($_POST['metal_type']);
        $name = strtolower($_POST['product_name']);
        
        if (strpos($metal, 'gold') !== false) {
            if (strpos($name, 'ring') !== false) $prefix = 'GR';
            elseif (strpos($name, 'necklace') !== false) $prefix = 'GN';
            elseif (strpos($name, 'earring') !== false) $prefix = 'GE';
            else $prefix = 'GJ';
        } elseif (strpos($metal, 'silver') !== false) {
            if (strpos($name, 'ring') !== false) $prefix = 'SR';
            elseif (strpos($name, 'bracelet') !== false) $prefix = 'SB';
            else $prefix = 'SJ';
        } else {
            $prefix = 'JW';
        }
        
        // Get next number
        $lastProduct = $db->fetch("SELECT product_code FROM products WHERE product_code LIKE ? ORDER BY id DESC LIMIT 1", [$prefix . '%']);
        $nextNum = 1;
        if ($lastProduct) {
            $lastNum = intval(substr($lastProduct['product_code'], strlen($prefix)));
            $nextNum = $lastNum + 1;
        }
        
        $product_code = $prefix . str_pad($nextNum, 3, '0', STR_PAD_LEFT);
        
        // Calculate net weight
        $gross_weight = floatval($_POST['gross_weight']);
        $stone_weight = floatval($_POST['stone_weight']);
        $net_weight = $gross_weight - $stone_weight;
        
        // Insert product
        $sql = "INSERT INTO products (product_name, product_code, description, metal_type, tunch_percentage, gross_weight, stone_weight, net_weight, making_charges_per_gram, wastage_percentage, hallmark_charges, va_percentage, stone_cost, making_charges, hsn_code, tax_rate, scheme_applicable, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $_POST['product_name'],
            $product_code,
            $_POST['description'] ?? '',
            $_POST['metal_type'],
            floatval($_POST['tunch_percentage']),
            $gross_weight,
            $stone_weight,
            $net_weight,
            floatval($_POST['making_charges_per_gram'] ?? 0),
            floatval($_POST['wastage_percentage'] ?? 2.5),
            floatval($_POST['hallmark_charges'] ?? 0),
            floatval($_POST['va_percentage'] ?? 0),
            floatval($_POST['stone_cost'] ?? 0),
            floatval($_POST['making_charges'] ?? 0),
            $_POST['hsn_code'] ?? '',
            floatval($_POST['tax_rate'] ?? 3.0),
            isset($_POST['scheme_applicable']) ? 1 : 0
        ];
        
        $db->execute($sql, $params);
        $product_id = $db->lastInsertId();
        
        // Create inventory record
        $db->execute("INSERT INTO inventory (product_id, quantity_in_stock, minimum_stock_level, created_at) VALUES (?, 0, 5, NOW())", [$product_id]);
        
        $success = "🎉 Product created successfully! Product Code: <strong>$product_code</strong> | Product ID: <strong>$product_id</strong>";
        
    } catch (Exception $e) {
        $error = "Error creating product: " . $e->getMessage();
    }
}

// Get categories and suppliers for dropdowns
$categories = $db->fetchAll("SELECT id, category_name FROM categories WHERE is_active = 1 ORDER BY category_name");
$suppliers = $db->fetchAll("SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fast Add Product - Indian Jewellery System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; margin: 20px auto; }
        .card { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .form-label { font-weight: 600; color: #333; }
        .required { color: #dc3545; }
        .help-text { font-size: 0.875rem; color: #6c757d; }
        .btn-primary { background-color: #007bff; border-color: #007bff; }
        .alert { border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    Fast Add Product - Indian Jewellery
                </h4>
            </div>
            
            <div class="card-body">
                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div class="text-center mb-3">
                    <a href="products.php" class="btn btn-success me-2">
                        <i class="fas fa-list me-1"></i>View All Products
                    </a>
                    <a href="fast-add-product.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add Another Product
                    </a>
                </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <form method="POST" id="productForm">
                    <input type="hidden" name="action" value="add_product">
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-info-circle me-2"></i>Basic Information
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="product_name" class="form-label">
                                Product Name <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="product_name" name="product_name" required>
                            <div class="help-text">e.g., Gold Ring Classic, Silver Necklace Set</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="description" name="description">
                            <div class="help-text">Brief product description</div>
                        </div>
                    </div>
                    
                    <!-- Metal & Purity -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-gem me-2"></i>Metal & Purity
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="metal_type" class="form-label">
                                Metal Type <span class="required">*</span>
                            </label>
                            <select class="form-select" id="metal_type" name="metal_type" required>
                                <option value="">Select Metal</option>
                                <option value="Gold">Gold</option>
                                <option value="Silver">Silver</option>
                                <option value="Platinum">Platinum</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tunch_percentage" class="form-label">
                                Tunch/Purity (%) <span class="required">*</span>
                            </label>
                            <input type="number" class="form-control" id="tunch_percentage" name="tunch_percentage" 
                                   step="0.01" value="91.6" required>
                            <div class="help-text">22K Gold=91.6%, 18K Gold=75%, 925 Silver=92.5%</div>
                        </div>
                    </div>
                    
                    <!-- Weight Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-balance-scale me-2"></i>Weight Information
                            </h5>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="gross_weight" class="form-label">
                                Gross Weight (g) <span class="required">*</span>
                            </label>
                            <input type="number" class="form-control" id="gross_weight" name="gross_weight" 
                                   step="0.001" required>
                            <div class="help-text">Total weight including stones</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="stone_weight" class="form-label">Stone Weight (g)</label>
                            <input type="number" class="form-control" id="stone_weight" name="stone_weight" 
                                   step="0.001" value="0">
                            <div class="help-text">Weight of stones/gems only</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="net_weight" class="form-label">Net Weight (g)</label>
                            <input type="number" class="form-control" id="net_weight" name="net_weight" 
                                   step="0.001" readonly style="background-color: #e9ecef;">
                            <div class="help-text">Auto-calculated (Gross - Stone)</div>
                        </div>
                    </div>
                    
                    <!-- Charges & Pricing -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-rupee-sign me-2"></i>Charges & Pricing
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="making_charges_per_gram" class="form-label">Making Charges (₹/g)</label>
                            <input type="number" class="form-control" id="making_charges_per_gram" name="making_charges_per_gram" 
                                   step="0.01" value="0">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="wastage_percentage" class="form-label">Wastage (%)</label>
                            <input type="number" class="form-control" id="wastage_percentage" name="wastage_percentage" 
                                   step="0.1" value="2.5">
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary btn-lg me-3" id="submitBtn">
                                <span id="submitText">
                                    <i class="fas fa-save me-2"></i>Create Product
                                </span>
                                <span id="loadingText" style="display: none;">
                                    <i class="fas fa-spinner fa-spin me-2"></i>Creating Product...
                                </span>
                            </button>
                            <a href="products.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Panel -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Quick Help
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Product Codes</h6>
                        <small>Auto-generated based on product type:<br>
                        GR001 = Gold Ring, SB001 = Silver Bracelet</small>
                    </div>
                    <div class="col-md-6">
                        <h6>Common Purities</h6>
                        <small>22K Gold = 91.6%<br>
                        18K Gold = 75.0%<br>
                        925 Silver = 92.5%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const grossWeight = document.getElementById('gross_weight');
            const stoneWeight = document.getElementById('stone_weight');
            const netWeight = document.getElementById('net_weight');
            const form = document.getElementById('productForm');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingText = document.getElementById('loadingText');
            
            function calculateNetWeight() {
                const gross = parseFloat(grossWeight.value) || 0;
                const stone = parseFloat(stoneWeight.value) || 0;
                netWeight.value = (gross - stone).toFixed(3);
            }
            
            function handleSubmit(e) {
                submitBtn.disabled = true;
                submitText.style.display = 'none';
                loadingText.style.display = 'inline';
            }
            
            grossWeight.addEventListener('input', calculateNetWeight);
            stoneWeight.addEventListener('input', calculateNetWeight);
            form.addEventListener('submit', handleSubmit);
            
            calculateNetWeight();
        });
    </script>
</body>
</html>
