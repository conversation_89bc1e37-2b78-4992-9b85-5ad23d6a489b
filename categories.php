<?php
/**
 * Categories Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$category_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'category_name' => sanitizeInput($_POST['category_name']),
                'description' => sanitizeInput($_POST['description']),
                'parent_id' => $_POST['parent_id'] ?: null,
                'sort_order' => intval($_POST['sort_order'])
            ];
            
            if ($action === 'add') {
                $sql = "INSERT INTO categories (category_name, description, parent_id, sort_order) VALUES (?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "Category added successfully!";
            } else {
                $sql = "UPDATE categories SET category_name=?, description=?, parent_id=?, sort_order=? WHERE id=?";
                $params = array_values($data);
                $params[] = $category_id;
                $db->query($sql, $params);
                $success = "Category updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $category_id) {
            // Check if category has products
            $product_count = $db->fetch("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$category_id])['count'];
            
            if ($product_count > 0) {
                $error = "Cannot delete category. It has $product_count products assigned to it.";
            } else {
                $db->query("UPDATE categories SET is_active = 0 WHERE id = ?", [$category_id]);
                $success = "Category deleted successfully!";
            }
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get categories list
if ($action === 'list') {
    $categories = $db->fetchAll("
        SELECT c.*, 
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.sort_order, c.category_name
    ");
}

// Get single category for edit
if ($action === 'edit' && $category_id) {
    $category = $db->fetch("SELECT * FROM categories WHERE id = ? AND is_active = 1", [$category_id]);
    if (!$category) {
        $error = "Category not found!";
        $action = 'list';
    }
}

// Get parent categories for dropdown
$parent_categories = $db->fetchAll("SELECT id, category_name FROM categories WHERE is_active = 1 AND parent_id IS NULL ORDER BY category_name");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Categories List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Product Categories</h2>
                    <p class="text-muted mb-0">Organize your jewellery products</p>
                </div>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Category
                </a>
            </div>

            <!-- Categories Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Category Name</th>
                                    <th>Parent Category</th>
                                    <th>Description</th>
                                    <th>Products</th>
                                    <th>Sort Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($categories)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4 text-muted">
                                            <i class="fas fa-tags fa-3x mb-3 opacity-25"></i>
                                            <p>No categories found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($categories as $cat): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-tag me-2 text-primary"></i>
                                                    <strong><?php echo htmlspecialchars($cat['category_name']); ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($cat['parent_name']): ?>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($cat['parent_name']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">Main Category</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($cat['description']): ?>
                                                    <?php echo htmlspecialchars(substr($cat['description'], 0, 50)); ?>
                                                    <?php if (strlen($cat['description']) > 50) echo '...'; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $cat['product_count']; ?> products
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo $cat['sort_order']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=edit&id=<?php echo $cat['id']; ?>" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($cat['product_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteCategory(<?php echo $cat['id']; ?>)" 
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Category Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Category' : 'Edit Category'; ?></h2>
                    <p class="text-muted mb-0">Enter category details below</p>
                </div>
                <a href="categories.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="category_name" class="form-label">Category Name *</label>
                                    <input type="text" class="form-control" id="category_name" name="category_name" 
                                           value="<?php echo htmlspecialchars($category['category_name'] ?? ''); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="parent_id" class="form-label">Parent Category</label>
                                        <select class="form-select" id="parent_id" name="parent_id">
                                            <option value="">Main Category</option>
                                            <?php foreach ($parent_categories as $parent): ?>
                                                <?php if ($parent['id'] != $category_id): // Prevent self-parent ?>
                                                    <option value="<?php echo $parent['id']; ?>" 
                                                            <?php echo ($category['parent_id'] ?? '') == $parent['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($parent['category_name']); ?>
                                                    </option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="sort_order" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                               value="<?php echo $category['sort_order'] ?? '0'; ?>" min="0">
                                        <div class="form-text">Lower numbers appear first</div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="categories.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action === 'add' ? 'Add Category' : 'Update Category'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Naming Tips</h6>
                                <ul class="mb-0 small">
                                    <li>Use clear, descriptive names</li>
                                    <li>Keep names concise but meaningful</li>
                                    <li>Use consistent naming conventions</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-sitemap me-2"></i>Category Structure</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Main:</strong> Chains, Bangles, Rings</li>
                                    <li><strong>Sub:</strong> Gold Chains, Silver Chains</li>
                                    <li>Maximum 2 levels recommended</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-sort-numeric-down me-2"></i>Sort Order</h6>
                                <p class="mb-0 small">Use sort order to control the display sequence. Lower numbers appear first.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }
    </script>
</body>
</html>
