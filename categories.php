<?php
/**
 * Categories Management - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$category_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    try {
        if ($action === 'add' || $action === 'edit') {
            $data = [
                'category_name' => sanitizeInput($_POST['category_name']),
                'description' => sanitizeInput($_POST['description']),
                'parent_id' => $_POST['parent_id'] ?: null,
                'sort_order' => intval($_POST['sort_order'])
            ];
            
            if ($action === 'add') {
                $sql = "INSERT INTO categories (category_name, description, parent_id, sort_order) VALUES (?, ?, ?, ?)";
                $db->query($sql, array_values($data));
                $success = "Category added successfully!";
            } else {
                $sql = "UPDATE categories SET category_name=?, description=?, parent_id=?, sort_order=? WHERE id=?";
                $params = array_values($data);
                $params[] = $category_id;
                $db->query($sql, $params);
                $success = "Category updated successfully!";
            }
            
            $action = 'list';
        } elseif ($action === 'delete' && $category_id) {
            // Check if category has products
            $product_count = $db->fetch("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$category_id])['count'];

            if ($product_count > 0) {
                $error = "Cannot delete category. It has $product_count products assigned to it.";
            } else {
                $db->query("UPDATE categories SET is_active = 0 WHERE id = ?", [$category_id]);
                $success = "Category deleted successfully!";
            }
            $action = 'list';
        } elseif ($action === 'bulk_delete' && isset($_POST['selected_categories'])) {
            // Bulk delete categories
            $categoryIds = $_POST['selected_categories'];
            $deletedCount = 0;
            $errors = [];

            foreach ($categoryIds as $catId) {
                $product_count = $db->fetch("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$catId])['count'];
                if ($product_count > 0) {
                    $category_name = $db->fetch("SELECT category_name FROM categories WHERE id = ?", [$catId])['category_name'];
                    $errors[] = "$category_name has $product_count products";
                } else {
                    $db->query("UPDATE categories SET is_active = 0 WHERE id = ?", [$catId]);
                    $deletedCount++;
                }
            }

            if ($deletedCount > 0) {
                $success = "Successfully deleted $deletedCount categories.";
                if (!empty($errors)) {
                    $success .= " Errors: " . implode(', ', $errors);
                }
            } else {
                $error = "No categories could be deleted. " . implode(', ', $errors);
            }
            $action = 'list';
        } elseif ($action === 'bulk_reorder' && isset($_POST['category_orders'])) {
            // Bulk reorder categories
            $orders = $_POST['category_orders'];
            foreach ($orders as $catId => $order) {
                $db->query("UPDATE categories SET sort_order = ? WHERE id = ?", [intval($order), $catId]);
            }
            $success = "Category order updated successfully!";
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get categories list
if ($action === 'list') {
    $categories = $db->fetchAll("
        SELECT c.*,
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count,
               (SELECT COUNT(*) FROM categories WHERE parent_id = c.id AND is_active = 1) as subcategory_count,
               (SELECT COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0)
                FROM products pr
                JOIN inventory i ON pr.id = i.product_id
                WHERE pr.category_id = c.id AND pr.is_active = 1) as total_stock_value
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.sort_order, c.category_name
    ");
}

// Get single category for edit
if ($action === 'edit' && $category_id) {
    $category = $db->fetch("SELECT * FROM categories WHERE id = ? AND is_active = 1", [$category_id]);
    if (!$category) {
        $error = "Category not found!";
        $action = 'list';
    }
}

// Get parent categories for dropdown
$parent_categories = $db->fetchAll("SELECT id, category_name FROM categories WHERE is_active = 1 AND parent_id IS NULL ORDER BY category_name");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="main-content">
            <?php include 'includes/header.php'; ?>

            <div class="content-area">
                <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Categories List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Product Categories</h2>
                    <p class="text-muted mb-0">Organize your jewellery products</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group">
                        <button class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="export-categories.php?format=csv"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                            <li><a class="dropdown-item" href="export-categories.php?format=excel"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                            <li><a class="dropdown-item" href="export-categories.php?format=pdf"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-outline-info" onclick="showCategoryAnalytics()">
                        <i class="fas fa-chart-pie me-2"></i>Analytics
                    </button>
                    <button class="btn btn-outline-warning" onclick="showBulkOperations()">
                        <i class="fas fa-tasks me-2"></i>Bulk Operations
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showCategoryTree()">
                        <i class="fas fa-sitemap me-2"></i>Tree View
                    </button>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Category
                    </a>
                </div>
            </div>

            <!-- Categories Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAllCategories" onchange="toggleSelectAllCategories()">
                                    </th>
                                    <th>Category Name</th>
                                    <th>Parent Category</th>
                                    <th>Description</th>
                                    <th>Products</th>
                                    <th>Subcategories</th>
                                    <th>Stock Value</th>
                                    <th>Sort Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($categories)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            <i class="fas fa-tags fa-3x mb-3 opacity-25"></i>
                                            <p>No categories found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($categories as $cat): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="category-checkbox" value="<?php echo $cat['id']; ?>" onchange="updateBulkSelection()">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-tag me-2 text-primary"></i>
                                                    <strong><?php echo htmlspecialchars($cat['category_name']); ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($cat['parent_name']): ?>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($cat['parent_name']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">Main Category</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($cat['description']): ?>
                                                    <?php echo htmlspecialchars(substr($cat['description'], 0, 50)); ?>
                                                    <?php if (strlen($cat['description']) > 50) echo '...'; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $cat['product_count']; ?> products
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($cat['subcategory_count'] > 0): ?>
                                                    <span class="badge bg-success">
                                                        <?php echo $cat['subcategory_count']; ?> subcategories
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo formatCurrency($cat['total_stock_value']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo $cat['sort_order']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" title="View Details"
                                                            onclick="viewCategoryDetails(<?php echo $cat['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <a href="?action=edit&id=<?php echo $cat['id']; ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($cat['product_count'] > 0): ?>
                                                        <button class="btn btn-outline-success" title="View Products"
                                                                onclick="viewCategoryProducts(<?php echo $cat['id']; ?>)">
                                                            <i class="fas fa-box"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($cat['subcategory_count'] > 0): ?>
                                                        <button class="btn btn-outline-warning" title="View Subcategories"
                                                                onclick="viewSubcategories(<?php echo $cat['id']; ?>)">
                                                            <i class="fas fa-sitemap"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($cat['product_count'] == 0 && $cat['subcategory_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger"
                                                                onclick="deleteCategory(<?php echo $cat['id']; ?>, '<?php echo htmlspecialchars($cat['category_name']); ?>')"
                                                                title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Category Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><?php echo $action === 'add' ? 'Add New Category' : 'Edit Category'; ?></h2>
                    <p class="text-muted mb-0">Enter category details below</p>
                </div>
                <a href="categories.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="category_name" class="form-label">Category Name *</label>
                                    <input type="text" class="form-control" id="category_name" name="category_name" 
                                           value="<?php echo htmlspecialchars($category['category_name'] ?? ''); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="parent_id" class="form-label">Parent Category</label>
                                        <select class="form-select" id="parent_id" name="parent_id">
                                            <option value="">Main Category</option>
                                            <?php foreach ($parent_categories as $parent): ?>
                                                <?php if ($parent['id'] != $category_id): // Prevent self-parent ?>
                                                    <option value="<?php echo $parent['id']; ?>" 
                                                            <?php echo ($category['parent_id'] ?? '') == $parent['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($parent['category_name']); ?>
                                                    </option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="sort_order" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                               value="<?php echo $category['sort_order'] ?? '0'; ?>" min="0">
                                        <div class="form-text">Lower numbers appear first</div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="categories.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action === 'add' ? 'Add Category' : 'Update Category'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Category Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Naming Tips</h6>
                                <ul class="mb-0 small">
                                    <li>Use clear, descriptive names</li>
                                    <li>Keep names concise but meaningful</li>
                                    <li>Use consistent naming conventions</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-sitemap me-2"></i>Category Structure</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Main:</strong> Chains, Bangles, Rings</li>
                                    <li><strong>Sub:</strong> Gold Chains, Silver Chains</li>
                                    <li>Maximum 2 levels recommended</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-sort-numeric-down me-2"></i>Sort Order</h6>
                                <p class="mb-0 small">Use sort order to control the display sequence. Lower numbers appear first.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Category Analytics Modal -->
    <div class="modal fade" id="categoryAnalyticsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Category Analytics</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Product Distribution by Category</h6>
                            <canvas id="categoryDistributionChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h6>Category Statistics</h6>
                            <div class="list-group" id="categoryStatistics">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>Top Categories by Stock Value</h6>
                            <div id="topCategoriesByValue">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Category Performance</h6>
                            <div id="categoryPerformance">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportCategoryAnalytics()">Export Report</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Operations Modal -->
    <div class="modal fade" id="bulkOperationsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Operations</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Operation</label>
                        <select class="form-select" id="bulkOperation">
                            <option value="">Choose operation...</option>
                            <option value="export_selected">Export Selected Categories</option>
                            <option value="delete_selected">Delete Selected Categories</option>
                            <option value="reorder_categories">Reorder Categories</option>
                            <option value="move_to_parent">Move to Parent Category</option>
                        </select>
                    </div>
                    <div id="bulkOperationDetails" style="display: none;">
                        <!-- Dynamic content based on selected operation -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processBulkOperation()">Execute</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Tree Modal -->
    <div class="modal fade" id="categoryTreeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Category Tree View</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="categoryTreeContent">
                        <!-- Dynamic tree content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="printCategoryTree()">Print Tree</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Details Modal -->
    <div class="modal fade" id="categoryDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Category Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="categoryDetailsContent">
                        <!-- Dynamic content -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Selection Bar -->
    <div class="card mb-3" id="bulkSelectionBar" style="display: none; position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 1000; width: 400px;">
        <div class="card-body py-2">
            <div class="d-flex justify-content-between align-items-center">
                <span id="selectedCategoryCount">0 categories selected</span>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showBulkOperations()">
                        <i class="fas fa-tasks me-1"></i>Actions
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                </div>
            </div>
        </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function deleteCategory(id, name) {
            if (confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
                window.location.href = `?action=delete&id=${id}`;
            }
        }

        // Enhanced category management functions
        function toggleSelectAllCategories() {
            const selectAll = document.getElementById('selectAllCategories');
            const checkboxes = document.querySelectorAll('.category-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkSelection();
        }

        function updateBulkSelection() {
            const checkboxes = document.querySelectorAll('.category-checkbox:checked');
            const bulkBar = document.getElementById('bulkSelectionBar');
            const countSpan = document.getElementById('selectedCategoryCount');

            if (checkboxes.length > 0) {
                bulkBar.style.display = 'block';
                countSpan.textContent = checkboxes.length + ' categories selected';
            } else {
                bulkBar.style.display = 'none';
            }
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.category-checkbox');
            const selectAll = document.getElementById('selectAllCategories');

            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAll.checked = false;

            updateBulkSelection();
        }

        function showCategoryAnalytics() {
            const modal = new bootstrap.Modal(document.getElementById('categoryAnalyticsModal'));
            modal.show();
            loadCategoryAnalytics();
        }

        function loadCategoryAnalytics() {
            fetch('ajax/category-analytics.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAnalyticsDisplay(data);
                        drawCategoryChart(data.distribution);
                    }
                })
                .catch(error => {
                    console.error('Error loading analytics:', error);
                });
        }

        function updateAnalyticsDisplay(data) {
            // Update statistics
            const statisticsHtml = data.statistics.map(stat => `
                <div class="list-group-item d-flex justify-content-between">
                    <span>${stat.label}</span>
                    <strong>${stat.value}</strong>
                </div>
            `).join('');
            document.getElementById('categoryStatistics').innerHTML = statisticsHtml;

            // Update top categories
            const topCategoriesHtml = data.topCategories.map(cat => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                    <span><strong>${cat.name}</strong></span>
                    <span class="badge bg-success">₹${cat.value}</span>
                </div>
            `).join('');
            document.getElementById('topCategoriesByValue').innerHTML = topCategoriesHtml;

            // Update performance
            const performanceHtml = data.performance.map(perf => `
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>${perf.category}</span>
                        <span>${perf.products} products</span>
                    </div>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar" style="width: ${perf.percentage}%"></div>
                    </div>
                </div>
            `).join('');
            document.getElementById('categoryPerformance').innerHTML = performanceHtml;
        }

        function drawCategoryChart(distribution) {
            // This would integrate with Chart.js to draw the distribution chart
            console.log('Drawing category chart with data:', distribution);
        }

        function showBulkOperations() {
            const checkboxes = document.querySelectorAll('.category-checkbox:checked');

            if (checkboxes.length === 0) {
                alert('Please select categories first');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('bulkOperationsModal'));
            modal.show();
        }

        function processBulkOperation() {
            const operation = document.getElementById('bulkOperation').value;
            const checkboxes = document.querySelectorAll('.category-checkbox:checked');

            if (!operation || checkboxes.length === 0) {
                alert('Please select an operation and categories');
                return;
            }

            const categoryIds = Array.from(checkboxes).map(cb => cb.value);

            switch (operation) {
                case 'export_selected':
                    exportSelectedCategories(categoryIds);
                    break;
                case 'delete_selected':
                    bulkDeleteCategories(categoryIds);
                    break;
                case 'reorder_categories':
                    showReorderInterface(categoryIds);
                    break;
                case 'move_to_parent':
                    showMoveToParentInterface(categoryIds);
                    break;
            }
        }

        function exportSelectedCategories(categoryIds) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export-categories.php';

            categoryIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_categories[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }

        function bulkDeleteCategories(categoryIds) {
            if (confirm(`Are you sure you want to delete ${categoryIds.length} selected categories?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=bulk_delete';

                categoryIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'selected_categories[]';
                    input.value = id;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        }

        function showCategoryTree() {
            const modal = new bootstrap.Modal(document.getElementById('categoryTreeModal'));
            modal.show();

            // Load category tree
            document.getElementById('categoryTreeContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

            fetch('ajax/category-tree.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('categoryTreeContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('categoryTreeContent').innerHTML = '<div class="alert alert-danger">Error loading tree</div>';
                });
        }

        function viewCategoryDetails(id) {
            const modal = new bootstrap.Modal(document.getElementById('categoryDetailsModal'));
            modal.show();

            // Load category details
            document.getElementById('categoryDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

            fetch(`ajax/category-details.php?id=${id}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('categoryDetailsContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('categoryDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading details</div>';
                });
        }

        function viewCategoryProducts(id) {
            window.open(`products.php?category_id=${id}`, '_blank');
        }

        function viewSubcategories(id) {
            window.location.href = `categories.php?parent_id=${id}`;
        }

        function exportCategoryAnalytics() {
            window.open('export-category-analytics.php', '_blank');
        }

        function printCategoryTree() {
            const content = document.getElementById('categoryTreeContent').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Category Tree</title>
                    <style>
                        body { font-family: Arial, sans-serif; }
                        .tree-item { margin: 5px 0; }
                        .tree-indent { margin-left: 20px; }
                    </style>
                </head>
                <body>
                    <h1>Category Tree</h1>
                    ${content}
                    <script>window.print();</script>
                </body>
                </html>
            `);
        }
    </script>
</body>
</html>
