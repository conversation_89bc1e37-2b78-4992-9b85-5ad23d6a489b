<?php
// Complete Jewelry Management System with Correct Formula
// Formula: Metal Value = Tunch Weight × Base Rate (Direct Multiplication)

class JewelryDatabase {
    private $pdo;
    
    public function __construct() {
        try {
            $this->pdo = new PDO('sqlite:jewelry_complete.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createTables();
        } catch(PDOException $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }
    
    private function createTables() {
        // Suppliers table
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            contact TEXT,
            address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Inventory table
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            product_name TEXT NOT NULL,
            description TEXT,
            current_wt REAL NOT NULL DEFAULT 0,
            without_stone_wt REAL NOT NULL DEFAULT 0,
            with_stone_cost REAL NOT NULL DEFAULT 0,
            without_stone_cost REAL NOT NULL DEFAULT 0,
            procured_in_24k REAL NOT NULL DEFAULT 0,
            sold_value REAL NOT NULL DEFAULT 0,
            balance_in_stock REAL NOT NULL DEFAULT 0,
            with_stone_24k REAL NOT NULL DEFAULT 0,
            without_stone_24k REAL NOT NULL DEFAULT 0,
            weight_in_24k REAL NOT NULL DEFAULT 0,
            unit_price REAL NOT NULL DEFAULT 0,
            stone_price REAL NOT NULL DEFAULT 0,
            total_value REAL NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )");
        
        // Billing table with CORRECT formula
        $this->pdo->exec("CREATE TABLE IF NOT EXISTS billing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            location TEXT NOT NULL,
            product_name TEXT NOT NULL,
            gross_wt REAL NOT NULL,
            stone_wt REAL NOT NULL,
            net_wt REAL NOT NULL,
            tunch_percentage REAL NOT NULL,
            base_rate REAL NOT NULL,
            tunch_weight REAL NOT NULL,
            metal_value REAL NOT NULL,
            making_charges REAL NOT NULL DEFAULT 0,
            stone_charges REAL NOT NULL DEFAULT 0,
            final_value REAL NOT NULL,
            inventory_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (inventory_id) REFERENCES inventory (id)
        )");
    }
    
    public function execute($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }
    
    public function query($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}

$db = new JewelryDatabase();
$success = '';
$error = '';
$active_tab = $_GET['tab'] ?? 'billing';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_supplier':
                    $db->execute("INSERT INTO suppliers (name, contact, address) VALUES (?, ?, ?)", [
                        $_POST['supplier_name'],
                        $_POST['contact'],
                        $_POST['address']
                    ]);
                    $success = "✅ Supplier added successfully!";
                    break;
                    
                case 'add_inventory':
                    $db->execute("INSERT INTO inventory (
                        supplier_id, product_name, description, current_wt, without_stone_wt,
                        with_stone_cost, without_stone_cost, procured_in_24k, sold_value,
                        balance_in_stock, with_stone_24k, without_stone_24k, weight_in_24k,
                        unit_price, stone_price, total_value
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                        $_POST['supplier_id'],
                        $_POST['product_name'],
                        $_POST['description'],
                        floatval($_POST['current_wt']),
                        floatval($_POST['without_stone_wt']),
                        floatval($_POST['with_stone_cost']),
                        floatval($_POST['without_stone_cost']),
                        floatval($_POST['procured_in_24k']),
                        floatval($_POST['sold_value']),
                        floatval($_POST['balance_in_stock']),
                        floatval($_POST['with_stone_24k']),
                        floatval($_POST['without_stone_24k']),
                        floatval($_POST['weight_in_24k']),
                        floatval($_POST['unit_price']),
                        floatval($_POST['stone_price']),
                        floatval($_POST['total_value'])
                    ]);
                    $success = "✅ Inventory item added successfully!";
                    break;
                    
                case 'create_bill':
                    // Apply CORRECT formula: Metal Value = Tunch Weight × Base Rate
                    $gross_wt = floatval($_POST['gross_wt']);
                    $stone_wt = floatval($_POST['stone_wt']);
                    $net_wt = $gross_wt - $stone_wt;
                    $tunch_percentage = floatval($_POST['tunch_percentage']);
                    $base_rate = floatval($_POST['base_rate']);
                    $making_charges = floatval($_POST['making_charges']);
                    $stone_charges = floatval($_POST['stone_charges']);
                    
                    // CORRECT CALCULATION (matches your handwriting)
                    $tunch_weight = $net_wt * ($tunch_percentage / 100);
                    $metal_value = $tunch_weight * $base_rate; // DIRECT multiplication
                    $final_value = $metal_value + $making_charges + $stone_charges;
                    
                    $db->execute("INSERT INTO billing (
                        customer_name, location, product_name, gross_wt, stone_wt, net_wt,
                        tunch_percentage, base_rate, tunch_weight, metal_value,
                        making_charges, stone_charges, final_value, inventory_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                        $_POST['customer_name'],
                        $_POST['location'],
                        $_POST['product_name'],
                        $gross_wt, $stone_wt, $net_wt,
                        $tunch_percentage, $base_rate, $tunch_weight, $metal_value,
                        $making_charges, $stone_charges, $final_value,
                        $_POST['inventory_id'] ?: null
                    ]);
                    
                    $success = "✅ Bill created successfully! Final Value: ₹" . number_format($final_value, 2);
                    break;
            }
        }
    } catch (Exception $e) {
        $error = "❌ Error: " . $e->getMessage();
    }
}

// Get data for display
$suppliers = $db->query("SELECT * FROM suppliers ORDER BY name");
$inventory = $db->query("SELECT i.*, s.name as supplier_name FROM inventory i LEFT JOIN suppliers s ON i.supplier_id = s.id ORDER BY i.created_at DESC");
$recent_bills = $db->query("SELECT * FROM billing ORDER BY created_at DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Jewelry Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .formula-display {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        .calculation-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .handwritten-match {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .nav-tabs .nav-link.active {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">💎 Complete Jewelry Management System</h1>
                <div class="alert alert-success">
                    <h5>✅ CORRECT FORMULA APPLIED</h5>
                    <p><strong>Metal Value = Tunch Weight × Base Rate</strong> (Direct multiplication - matches your handwriting)</p>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'billing' ? 'active' : ''; ?>" href="?tab=billing">💰 Billing</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'inventory' ? 'active' : ''; ?>" href="?tab=inventory">📦 Inventory</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'suppliers' ? 'active' : ''; ?>" href="?tab=suppliers">🏪 Suppliers</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'reports' ? 'active' : ''; ?>" href="?tab=reports">📊 Reports</a>
            </li>
        </ul>

        <!-- BILLING TAB -->
        <?php if ($active_tab === 'billing'): ?>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 Correct Formula Used</h5>
                    </div>
                    <div class="card-body">
                        <div class="formula-display">
                            <strong>Your EXACT Handwritten Formula:</strong><br>
                            1. <strong>Net Weight</strong> = Gross Weight - Stone Weight<br>
                            2. <strong>Tunch Weight</strong> = Net Weight × (Tunch % ÷ 100)<br>
                            3. <strong>Metal Value</strong> = Tunch Weight × Base Rate<br>
                            4. <strong>Final Value</strong> = Metal Value + Making + Stone Charges<br>
                            <br>
                            <strong style="color: #28a745;">✅ Example: 10.120g × 96% = 9.7159g × ₹10,112 = ₹98,240.10</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>💎 Create New Bill</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="create_bill">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Customer Name</label>
                                        <input type="text" class="form-control" name="customer_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Location</label>
                                        <input type="text" class="form-control" name="location" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">Product Name</label>
                                        <input type="text" class="form-control" name="product_name" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Link to Inventory</label>
                                        <select class="form-control" name="inventory_id">
                                            <option value="">Select Item</option>
                                            <?php foreach ($inventory as $item): ?>
                                                <option value="<?php echo $item['id']; ?>">
                                                    <?php echo htmlspecialchars($item['product_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Gross Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="gross_wt" id="gross_wt" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Stone Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="stone_wt" id="stone_wt" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Net Weight (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="net_wt" id="net_wt" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Tunch Percentage (%)</label>
                                        <input type="number" step="0.01" class="form-control" name="tunch_percentage" id="tunch_percentage" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Base Rate (₹ per 10g)</label>
                                        <input type="number" step="0.01" class="form-control" name="base_rate" id="base_rate" required>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-preview">
                                <div class="row">
                                    <div class="col-md-4">
                                        <small><strong>Tunch Weight:</strong><br>
                                        <span id="tunchWeightDisplay">0.000g</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>Metal Value:</strong><br>
                                        <span id="metalValueDisplay">₹0.00</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>Preview Total:</strong><br>
                                        <span id="finalValueDisplay">₹0.00</span></small>
                                    </div>
                                </div>
                                <div class="mt-2" id="comparisonDisplay">
                                    <p class="text-muted">Enter values to see calculation</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Making Charges (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="making_charges" id="making_charges" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Stone Charges (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="stone_charges" id="stone_charges" value="0">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-success btn-lg w-100">💎 Create Bill (Correct Formula)</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bills -->
        <?php if (!empty($recent_bills)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Recent Bills</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Product</th>
                                        <th>Net Wt</th>
                                        <th>Tunch %</th>
                                        <th>Tunch Wt</th>
                                        <th>Base Rate</th>
                                        <th>Metal Value</th>
                                        <th>Making</th>
                                        <th>Stone</th>
                                        <th>Final Value</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_bills as $bill): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($bill['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($bill['product_name']); ?></td>
                                        <td><?php echo number_format($bill['net_wt'], 3); ?>g</td>
                                        <td><?php echo $bill['tunch_percentage']; ?>%</td>
                                        <td><?php echo number_format($bill['tunch_weight'], 3); ?>g</td>
                                        <td>₹<?php echo number_format($bill['base_rate'], 2); ?></td>
                                        <td>₹<?php echo number_format($bill['metal_value'], 2); ?></td>
                                        <td>₹<?php echo number_format($bill['making_charges'], 2); ?></td>
                                        <td>₹<?php echo number_format($bill['stone_charges'], 2); ?></td>
                                        <td><strong>₹<?php echo number_format($bill['final_value'], 2); ?></strong></td>
                                        <td><?php echo date('M j, Y', strtotime($bill['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>

        <!-- INVENTORY TAB -->
        <?php if ($active_tab === 'inventory'): ?>
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>📦 Add Inventory Item</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_inventory">

                            <div class="mb-3">
                                <label class="form-label">Supplier</label>
                                <select class="form-control" name="supplier_id" required>
                                    <option value="">Select Supplier</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <option value="<?php echo $supplier['id']; ?>">
                                            <?php echo htmlspecialchars($supplier['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Product Name</label>
                                <input type="text" class="form-control" name="product_name" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="2"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Current Wt (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="current_wt" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Without Stone Wt (g)</label>
                                        <input type="number" step="0.001" class="form-control" name="without_stone_wt" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">With Stone Cost (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="with_stone_cost" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Without Stone Cost (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="without_stone_cost" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Procured in 24K</label>
                                        <input type="number" step="0.001" class="form-control" name="procured_in_24k" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sold Value (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="sold_value" value="0">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Balance in Stock</label>
                                        <input type="number" step="0.001" class="form-control" name="balance_in_stock" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Unit Price (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="unit_price" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">With Stone 24K</label>
                                        <input type="number" step="0.001" class="form-control" name="with_stone_24k" value="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Without Stone 24K</label>
                                        <input type="number" step="0.001" class="form-control" name="without_stone_24k" value="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Weight in 24K</label>
                                        <input type="number" step="0.001" class="form-control" name="weight_in_24k" value="0">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Stone Price (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="stone_price" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Total Value (₹)</label>
                                        <input type="number" step="0.01" class="form-control" name="total_value" required>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">📦 Add to Inventory</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 Current Inventory</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Supplier</th>
                                        <th>Product</th>
                                        <th>Current Wt</th>
                                        <th>Without Stone Wt</th>
                                        <th>With Stone Cost</th>
                                        <th>Without Stone Cost</th>
                                        <th>Procured 24K</th>
                                        <th>Balance</th>
                                        <th>Unit Price</th>
                                        <th>Total Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inventory as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['supplier_name'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo number_format($item['current_wt'], 3); ?>g</td>
                                        <td><?php echo number_format($item['without_stone_wt'], 3); ?>g</td>
                                        <td>₹<?php echo number_format($item['with_stone_cost'], 2); ?></td>
                                        <td>₹<?php echo number_format($item['without_stone_cost'], 2); ?></td>
                                        <td><?php echo number_format($item['procured_in_24k'], 3); ?></td>
                                        <td><?php echo number_format($item['balance_in_stock'], 3); ?></td>
                                        <td>₹<?php echo number_format($item['unit_price'], 2); ?></td>
                                        <td><strong>₹<?php echo number_format($item['total_value'], 2); ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- SUPPLIERS TAB -->
        <?php if ($active_tab === 'suppliers'): ?>
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>🏪 Add New Supplier</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_supplier">

                            <div class="mb-3">
                                <label class="form-label">Supplier Name</label>
                                <input type="text" class="form-control" name="supplier_name" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Contact</label>
                                <input type="text" class="form-control" name="contact">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Address</label>
                                <textarea class="form-control" name="address" rows="3"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">🏪 Add Supplier</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 Current Suppliers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Address</th>
                                        <th>Added Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($suppliers as $supplier): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($supplier['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($supplier['contact'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($supplier['address'] ?? 'N/A'); ?></td>
                                        <td><?php echo date('M j, Y', strtotime($supplier['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- REPORTS TAB -->
        <?php if ($active_tab === 'reports'): ?>
        <?php
        // Calculate summary statistics
        $total_inventory_value = array_sum(array_column($inventory, 'total_value'));
        $total_sales = array_sum(array_column($recent_bills, 'final_value'));
        $total_items = count($inventory);
        $total_suppliers = count($suppliers);
        ?>
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">📦 Total Items</h5>
                        <h2 class="text-primary"><?php echo $total_items; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">🏪 Suppliers</h5>
                        <h2 class="text-info"><?php echo $total_suppliers; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">💰 Inventory Value</h5>
                        <h2 class="text-success">₹<?php echo number_format($total_inventory_value, 2); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">💎 Recent Sales</h5>
                        <h2 class="text-warning">₹<?php echo number_format($total_sales, 2); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Inventory Summary by Supplier</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Supplier</th>
                                        <th>Items Count</th>
                                        <th>Total Weight (g)</th>
                                        <th>Total Value (₹)</th>
                                        <th>Average Unit Price (₹)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $supplier_summary = [];
                                    foreach ($inventory as $item) {
                                        $supplier_name = $item['supplier_name'] ?? 'Unknown';
                                        if (!isset($supplier_summary[$supplier_name])) {
                                            $supplier_summary[$supplier_name] = [
                                                'count' => 0,
                                                'total_weight' => 0,
                                                'total_value' => 0,
                                                'unit_prices' => []
                                            ];
                                        }
                                        $supplier_summary[$supplier_name]['count']++;
                                        $supplier_summary[$supplier_name]['total_weight'] += $item['current_wt'];
                                        $supplier_summary[$supplier_name]['total_value'] += $item['total_value'];
                                        $supplier_summary[$supplier_name]['unit_prices'][] = $item['unit_price'];
                                    }

                                    foreach ($supplier_summary as $supplier => $data):
                                        $avg_price = array_sum($data['unit_prices']) / count($data['unit_prices']);
                                    ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($supplier); ?></strong></td>
                                        <td><?php echo $data['count']; ?></td>
                                        <td><?php echo number_format($data['total_weight'], 3); ?>g</td>
                                        <td>₹<?php echo number_format($data['total_value'], 2); ?></td>
                                        <td>₹<?php echo number_format($avg_price, 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Only run billing calculations if we're on the billing tab
            if (document.getElementById('gross_wt')) {
                // Form elements
                const grossWt = document.getElementById('gross_wt');
                const stoneWt = document.getElementById('stone_wt');
                const netWt = document.getElementById('net_wt');
                const tunchPercentage = document.getElementById('tunch_percentage');
                const baseRate = document.getElementById('base_rate');
                const makingCharges = document.getElementById('making_charges');
                const stoneCharges = document.getElementById('stone_charges');

                // Display elements
                const tunchWeightDisplay = document.getElementById('tunchWeightDisplay');
                const metalValueDisplay = document.getElementById('metalValueDisplay');
                const finalValueDisplay = document.getElementById('finalValueDisplay');
                const comparisonDisplay = document.getElementById('comparisonDisplay');

                function calculateNetWeight() {
                    const gross = parseFloat(grossWt.value) || 0;
                    const stone = parseFloat(stoneWt.value) || 0;
                    const net = gross - stone;
                    netWt.value = net.toFixed(3);
                    calculateAll();
                }

                function calculateAll() {
                    const net = parseFloat(netWt.value) || 0;
                    const tunchPct = parseFloat(tunchPercentage.value) || 0;
                    const rate = parseFloat(baseRate.value) || 0;
                    const making = parseFloat(makingCharges.value) || 0;
                    const stoneCharge = parseFloat(stoneCharges.value) || 0;

                    if (net <= 0 || tunchPct <= 0 || rate <= 0) {
                        tunchWeightDisplay.textContent = '0.000g';
                        metalValueDisplay.textContent = '₹0.00';
                        finalValueDisplay.textContent = '₹0.00';
                        comparisonDisplay.innerHTML = '<p class="text-muted">Enter values to see calculation</p>';
                        return;
                    }

                    // Apply CORRECT formula (matches your handwriting)
                    // 1. Tunch Weight = Net Weight × (Tunch Percentage / 100)
                    const tunchWeight = net * (tunchPct / 100);

                    // 2. Metal Value = Tunch Weight × Base Rate (DIRECT multiplication)
                    const metalValue = tunchWeight * rate;

                    // 3. Final Value = Metal Value + Making + Stone Charges
                    const finalValue = metalValue + making + stoneCharge;

                    // Update display
                    tunchWeightDisplay.textContent = tunchWeight.toFixed(3) + 'g';
                    metalValueDisplay.textContent = '₹' + metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    finalValueDisplay.textContent = '₹' + finalValue.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});

                    // Show calculation match
                    comparisonDisplay.innerHTML = `
                        <div class="handwritten-match">
                            <strong>✅ Correct Formula Applied:</strong><br>
                            ${net.toFixed(3)}g × ${tunchPct}% = ${tunchWeight.toFixed(3)}g × ₹${rate.toLocaleString('en-IN')} = ₹${metalValue.toLocaleString('en-IN', {minimumFractionDigits: 2})}
                        </div>
                    `;
                }

                // Event listeners
                grossWt.addEventListener('input', calculateNetWeight);
                stoneWt.addEventListener('input', calculateNetWeight);
                tunchPercentage.addEventListener('input', calculateAll);
                baseRate.addEventListener('input', calculateAll);
                makingCharges.addEventListener('input', calculateAll);
                stoneCharges.addEventListener('input', calculateAll);
            }
        });
    </script>
</body>
</html>
