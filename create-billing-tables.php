<?php
/**
 * Create Database Tables for Billing System
 * Matches your exact billing software requirements
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🗄️ Creating Database Tables for Billing System</h2>";
    
    // Create inventory_items table (matches your inventory section)
    $inventoryTableSQL = "
    CREATE TABLE IF NOT EXISTS inventory_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supplier_name VARCHAR(255) NOT NULL,
        description VARCHAR(255) NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        gross_wt DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        stone_cost_purity DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        stone_cost_points DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        procured_in_24k DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        sold_value DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        balance_in_stock DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        with_stone_24k DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        without_stone_24k DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        weight_in_24k DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        weight_in_22k DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->execute($inventoryTableSQL);
    echo "<p>✅ <strong>inventory_items</strong> table created successfully</p>";
    
    // Create billing_entries table (matches your billing section)
    $billingTableSQL = "
    CREATE TABLE IF NOT EXISTS billing_entries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_name VARCHAR(255) NOT NULL,
        location VARCHAR(255) NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        gross_wt DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        stone_wt DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        net_wt DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        va_stone DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        plain DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        wt_in_24k DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        stone_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->execute($billingTableSQL);
    echo "<p>✅ <strong>billing_entries</strong> table created successfully</p>";
    
    // Insert sample inventory data (matching your screenshot)
    $sampleInventoryData = [
        [
            'supplier_name' => 'Emerald Jewel Industry',
            'description' => 'Coimbatore Chain',
            'product_name' => 'Chain',
            'gross_wt' => 10.325,
            'stone_cost_purity' => 129.320,
            'stone_cost_points' => 95,
            'procured_in_24k' => 94,
            'sold_value' => 112.195,
            'balance_in_stock' => 0.000,
            'with_stone_24k' => 10.160,
            'without_stone_24k' => 9.754,
            'weight_in_24k' => 110.260,
            'weight_in_22k' => 218.220
        ],
        [
            'supplier_name' => 'Nalla Gold',
            'description' => 'Mumbai Bangles',
            'product_name' => 'Bangles',
            'gross_wt' => 245.890,
            'stone_cost_purity' => 0,
            'stone_cost_points' => 96,
            'procured_in_24k' => 96,
            'sold_value' => 236.489,
            'balance_in_stock' => 0.000,
            'with_stone_24k' => 12.340,
            'without_stone_24k' => 11.478,
            'weight_in_24k' => 218.220,
            'weight_in_22k' => 0
        ],
        [
            'supplier_name' => 'SSJ',
            'description' => 'Studs',
            'product_name' => 'Studs',
            'gross_wt' => 110.325,
            'stone_cost_purity' => 89.320,
            'stone_cost_points' => 95,
            'procured_in_24k' => 99,
            'sold_value' => 192.998,
            'balance_in_stock' => 1.560,
            'with_stone_24k' => 2.940,
            'without_stone_24k' => 3.388,
            'weight_in_24k' => 195.875,
            'weight_in_22k' => 0
        ]
    ];
    
    echo "<h3>📦 Inserting Sample Inventory Data</h3>";
    
    foreach ($sampleInventoryData as $index => $data) {
        $sql = "INSERT INTO inventory_items (
            supplier_name, description, product_name, gross_wt, 
            stone_cost_purity, stone_cost_points, procured_in_24k, sold_value,
            balance_in_stock, with_stone_24k, without_stone_24k, weight_in_24k, weight_in_22k
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['supplier_name'],
            $data['description'],
            $data['product_name'],
            $data['gross_wt'],
            $data['stone_cost_purity'],
            $data['stone_cost_points'],
            $data['procured_in_24k'],
            $data['sold_value'],
            $data['balance_in_stock'],
            $data['with_stone_24k'],
            $data['without_stone_24k'],
            $data['weight_in_24k'],
            $data['weight_in_22k']
        ];
        
        $db->execute($sql, $params);
        echo "<p>✅ Sample inventory item " . ($index + 1) . " added: <strong>{$data['product_name']}</strong></p>";
    }
    
    // Insert sample billing data (matching your screenshot)
    $sampleBillingData = [
        [
            'customer_name' => 'VS Jewellery',
            'location' => 'Namakkal',
            'product_name' => 'Chain',
            'gross_wt' => 10.160,
            'stone_wt' => 0,
            'net_wt' => 10.160,
            'va_stone' => 0,
            'plain' => 96,
            'wt_in_24k' => 9.754,
            'unit_price' => 6140.00,
            'stone_price' => 0,
            'total' => 59903.56
        ],
        [
            'customer_name' => 'Krishna Jewels',
            'location' => 'Madurai',
            'product_name' => 'Bangles',
            'gross_wt' => 32.120,
            'stone_wt' => 0,
            'net_wt' => 32.120,
            'va_stone' => 0,
            'plain' => 98,
            'wt_in_24k' => 31.478,
            'unit_price' => 6140.00,
            'stone_price' => 0,
            'total' => 193362.92
        ],
        [
            'customer_name' => 'Dhanpal Jewels',
            'location' => 'Erode',
            'product_name' => 'Studs',
            'gross_wt' => 2.560,
            'stone_wt' => 0.560,
            'net_wt' => 2.000,
            'va_stone' => 97,
            'plain' => 161,
            'wt_in_24k' => 3.388,
            'unit_price' => 6140.00,
            'stone_price' => 1600,
            'total' => 21565.39
        ]
    ];
    
    echo "<h3>🧾 Inserting Sample Billing Data</h3>";
    
    foreach ($sampleBillingData as $index => $data) {
        $sql = "INSERT INTO billing_entries (
            customer_name, location, product_name, gross_wt, stone_wt, net_wt,
            va_stone, plain, wt_in_24k, unit_price, stone_price, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['customer_name'],
            $data['location'],
            $data['product_name'],
            $data['gross_wt'],
            $data['stone_wt'],
            $data['net_wt'],
            $data['va_stone'],
            $data['plain'],
            $data['wt_in_24k'],
            $data['unit_price'],
            $data['stone_price'],
            $data['total']
        ];
        
        $db->execute($sql, $params);
        echo "<p>✅ Sample billing entry " . ($index + 1) . " added: <strong>{$data['customer_name']} - {$data['product_name']}</strong></p>";
    }
    
    // Test the Tunch Calculation Formula
    echo "<h3>🧮 Testing Tunch Calculation Formula</h3>";
    
    $testCases = [
        [
            'name' => 'Chain (22K Gold)',
            'gross_wt' => 10.160,
            'stone_wt' => 0.000,
            'tunch_percentage' => 91.6,
            'base_rate' => 6700,
            'making_charges' => 800,
            'stone_charges' => 0
        ],
        [
            'name' => 'Bangles (22K Gold)',
            'gross_wt' => 32.120,
            'stone_wt' => 0.000,
            'tunch_percentage' => 91.6,
            'base_rate' => 6700,
            'making_charges' => 1200,
            'stone_charges' => 0
        ],
        [
            'name' => 'Studs with Stone',
            'gross_wt' => 2.560,
            'stone_wt' => 0.560,
            'tunch_percentage' => 91.6,
            'base_rate' => 6700,
            'making_charges' => 500,
            'stone_charges' => 1600
        ]
    ];
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📐 Your Exact Formula:</h4>";
    echo "<ol>";
    echo "<li><strong>Net Weight</strong> = Gross Weight - Stone Weight</li>";
    echo "<li><strong>Tunch Weight</strong> = Net Weight × (Tunch Percentage ÷ 100)</li>";
    echo "<li><strong>Tunch Rate</strong> = Base Rate × (Tunch Percentage ÷ 100)</li>";
    echo "<li><strong>Final Value</strong> = Tunch Weight × Tunch Rate + Making + Stone Charges</li>";
    echo "</ol>";
    echo "</div>";
    
    foreach ($testCases as $index => $test) {
        echo "<h4>🧮 Test Case " . ($index + 1) . ": {$test['name']}</h4>";
        
        // Apply your exact formula
        $net_weight = $test['gross_wt'] - $test['stone_wt']; // Net Weight = Gross Weight - Stone Weight
        $tunch_weight = $net_weight * ($test['tunch_percentage'] / 100); // Tunch Weight = Net Weight × (Tunch Percentage / 100)
        $tunch_rate = $test['base_rate'] * ($test['tunch_percentage'] / 100); // Tunch Rate = Base Rate × (Tunch Percentage / 100)
        $metal_value = $tunch_weight * $tunch_rate; // Metal Value = Tunch Weight × Tunch Rate
        $final_value = $metal_value + $test['making_charges'] + $test['stone_charges']; // Final Value = Tunch Weight × Tunch Rate + Making + Stone Charges
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 14px;'>";
        echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>Parameter</th><th style='padding: 8px;'>Value</th><th style='padding: 8px;'>Calculation</th></tr>";
        
        echo "<tr><td style='padding: 8px;'><strong>Gross Weight</strong></td><td style='padding: 8px;'>{$test['gross_wt']}g</td><td style='padding: 8px;'>Input value</td></tr>";
        echo "<tr><td style='padding: 8px;'><strong>Stone Weight</strong></td><td style='padding: 8px;'>{$test['stone_wt']}g</td><td style='padding: 8px;'>Input value</td></tr>";
        echo "<tr style='background-color: #fff3cd;'><td style='padding: 8px;'><strong>Net Weight</strong></td><td style='padding: 8px;'>" . number_format($net_weight, 3) . "g</td><td style='padding: 8px;'>{$test['gross_wt']} - {$test['stone_wt']}</td></tr>";
        
        echo "<tr><td style='padding: 8px;'><strong>Tunch Percentage</strong></td><td style='padding: 8px;'>{$test['tunch_percentage']}%</td><td style='padding: 8px;'>Input value</td></tr>";
        echo "<tr style='background-color: #fff3cd;'><td style='padding: 8px;'><strong>Tunch Weight</strong></td><td style='padding: 8px;'>" . number_format($tunch_weight, 3) . "g</td><td style='padding: 8px;'>" . number_format($net_weight, 3) . " × ({$test['tunch_percentage']} ÷ 100)</td></tr>";
        
        echo "<tr><td style='padding: 8px;'><strong>Base Rate</strong></td><td style='padding: 8px;'>₹" . number_format($test['base_rate'], 0) . "</td><td style='padding: 8px;'>Input value</td></tr>";
        echo "<tr style='background-color: #fff3cd;'><td style='padding: 8px;'><strong>Tunch Rate</strong></td><td style='padding: 8px;'>₹" . number_format($tunch_rate, 2) . "</td><td style='padding: 8px;'>₹{$test['base_rate']} × ({$test['tunch_percentage']} ÷ 100)</td></tr>";
        
        echo "<tr style='background-color: #d4edda;'><td style='padding: 8px;'><strong>Metal Value</strong></td><td style='padding: 8px;'>₹" . number_format($metal_value, 2) . "</td><td style='padding: 8px;'>" . number_format($tunch_weight, 3) . "g × ₹" . number_format($tunch_rate, 2) . "</td></tr>";
        
        echo "<tr><td style='padding: 8px;'><strong>Making Charges</strong></td><td style='padding: 8px;'>₹" . number_format($test['making_charges'], 2) . "</td><td style='padding: 8px;'>Input value</td></tr>";
        echo "<tr><td style='padding: 8px;'><strong>Stone Charges</strong></td><td style='padding: 8px;'>₹" . number_format($test['stone_charges'], 2) . "</td><td style='padding: 8px;'>Input value</td></tr>";
        
        echo "<tr style='background-color: #d1ecf1; font-size: 16px;'><td style='padding: 8px;'><strong>FINAL VALUE</strong></td><td style='padding: 8px;'><strong>₹" . number_format($final_value, 2) . "</strong></td><td style='padding: 8px;'><strong>Metal Value + Making + Stone</strong></td></tr>";
        
        echo "</table>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ Database Setup Complete!</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>inventory_items</strong> table created with sample data</li>";
    echo "<li>✅ <strong>billing_entries</strong> table created with sample data</li>";
    echo "<li>✅ <strong>Tunch Calculation Formula</strong> tested and working</li>";
    echo "<li>✅ <strong>Sample Data</strong> matches your billing software format</li>";
    echo "</ul>";
    echo "</div>";
    
    // Create enhanced_calculations table for advanced purity handling
    $enhancedTableSQL = "
    CREATE TABLE IF NOT EXISTS enhanced_calculations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_name VARCHAR(255) NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        metal_type VARCHAR(50) NOT NULL,
        gross_weight DECIMAL(10,3) NOT NULL,
        stone_weight DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        net_weight DECIMAL(10,3) NOT NULL,
        purity_input VARCHAR(50) NOT NULL,
        purity_percentage DECIMAL(8,4) NOT NULL,
        tunch_value INT NOT NULL,
        purity_format VARCHAR(20) NOT NULL,
        purity_label VARCHAR(100) NOT NULL,
        rate_per_gram DECIMAL(10,2) NOT NULL,
        fine_weight DECIMAL(10,3) NOT NULL,
        metal_value DECIMAL(12,2) NOT NULL,
        wastage_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
        wastage_weight DECIMAL(10,3) NOT NULL DEFAULT 0.000,
        wastage_value DECIMAL(12,2) NOT NULL DEFAULT 0.00,
        making_charges_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        making_charges DECIMAL(12,2) NOT NULL DEFAULT 0.00,
        stone_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
        total_before_tax DECIMAL(12,2) NOT NULL,
        cgst DECIMAL(12,2) NOT NULL,
        sgst DECIMAL(12,2) NOT NULL,
        total_gst DECIMAL(12,2) NOT NULL,
        grand_total DECIMAL(12,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer (customer_name),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity_format (purity_format),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->execute($enhancedTableSQL);
    echo "<p>✅ <strong>enhanced_calculations</strong> table created successfully</p>";

    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='enhanced-billing-system.php' style='background-color: #6f42c1; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Enhanced Billing System</a>";
    echo "<a href='billing-system.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Basic Billing System</a>";
    echo "<a href='fast-add-product.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Fast Add Product</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Setup Error</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
