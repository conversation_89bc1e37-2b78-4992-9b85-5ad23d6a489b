<?php
/**
 * Sales History - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$action = $_GET['action'] ?? 'list';
$sale_id = $_GET['id'] ?? null;

// Handle success message from billing
if (isset($_GET['success'])) {
    $success = $_GET['success'];
    $bill_number = $_GET['bill_number'] ?? '';
}

// Get sales list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $payment_status = $_GET['payment_status'] ?? '';
    
    $where_conditions = ["s.is_cancelled = 0"];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(s.bill_number LIKE ? OR c.customer_name LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($date_from) {
        $where_conditions[] = "s.sale_date >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "s.sale_date <= ?";
        $params[] = $date_to;
    }
    
    if ($payment_status) {
        $where_conditions[] = "s.payment_status = ?";
        $params[] = $payment_status;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sales = $db->fetchAll("
        SELECT s.*, c.customer_name, c.business_name,
               (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE $where_clause
        ORDER BY s.created_at DESC
        LIMIT 100
    ", $params);
    
    // Get summary statistics
    $stats = $db->fetch("
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(grand_total), 0) as total_amount,
            COALESCE(AVG(grand_total), 0) as avg_amount,
            COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_count
        FROM sales s
        WHERE $where_clause
    ", $params);
}

// Get single sale for view
if ($action === 'view' && $sale_id) {
    $sale = $db->fetch("
        SELECT s.*, c.customer_name, c.business_name, c.phone, c.email, c.address,
               u.full_name as created_by_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        WHERE s.id = ?
    ", [$sale_id]);
    
    if (!$sale) {
        $error = "Sale not found!";
        $action = 'list';
    } else {
        // Get sale items
        $sale_items = $db->fetchAll("
            SELECT si.*, p.product_name, p.product_code, p.metal_type, p.purity
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
            ORDER BY si.id
        ", [$sale_id]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales History - <?php echo APP_NAME; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/app.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <?php if ($bill_number): ?>
                    <strong>Bill Number: <?php echo htmlspecialchars($bill_number); ?></strong>
                <?php endif; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- Sales List -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Sales History</h2>
                    <p class="text-muted mb-0">View and manage all sales transactions</p>
                </div>
                <a href="billing.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Sale
                </a>
            </div>

            <!-- Sales Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Sales</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['total_sales']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-invoice fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Amount</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($stats['total_amount']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-rupee-sign fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average Sale</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($stats['avg_amount']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Payments</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['pending_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Bill number or customer...">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="payment_status" class="form-label">Payment Status</label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <option value="">All Status</option>
                                <option value="paid" <?php echo $payment_status === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                <option value="partial" <?php echo $payment_status === 'partial' ? 'selected' : ''; ?>>Partial</option>
                                <option value="pending" <?php echo $payment_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="sales.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Bill Number</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Amount</th>
                                    <th>Payment</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($sales)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4 text-muted">
                                            <i class="fas fa-file-invoice fa-3x mb-3 opacity-25"></i>
                                            <p>No sales found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($sales as $sale): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($sale['bill_number']); ?></strong>
                                                <br><small class="text-muted"><?php echo formatDateTime($sale['created_at']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo formatDate($sale['sale_date']); ?>
                                            </td>
                                            <td>
                                                <?php if ($sale['customer_name']): ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($sale['customer_name']); ?></strong>
                                                        <?php if ($sale['business_name']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($sale['business_name']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">Walk-in Customer</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $sale['item_count']; ?> items
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo formatCurrency($sale['grand_total']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo ucfirst($sale['payment_method']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $sale['payment_status'] === 'paid' ? 'success' : ($sale['payment_status'] === 'partial' ? 'warning' : 'danger'); ?>">
                                                    <?php echo ucfirst($sale['payment_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?action=view&id=<?php echo $sale['id']; ?>" 
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-outline-primary" 
                                                            onclick="printBill(<?php echo $sale['id']; ?>)" 
                                                            title="Print Bill">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="generatePDF(<?php echo $sale['id']; ?>)" 
                                                            title="Download PDF">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'view'): ?>
            <!-- View Sale Details -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Sale Details</h2>
                    <p class="text-muted mb-0">Bill Number: <?php echo htmlspecialchars($sale['bill_number']); ?></p>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="printBill(<?php echo $sale['id']; ?>)">
                        <i class="fas fa-print me-2"></i>Print Bill
                    </button>
                    <button class="btn btn-outline-danger me-2" onclick="generatePDF(<?php echo $sale['id']; ?>)">
                        <i class="fas fa-file-pdf me-2"></i>Download PDF
                    </button>
                    <a href="sales.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Sales
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Sale Items -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Sale Items</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Product</th>
                                            <th>Qty</th>
                                            <th>Weight</th>
                                            <th>Rate</th>
                                            <th>Amount</th>
                                            <th>Tax</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sale_items as $index => $item): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['product_code']); ?></small>
                                                        <?php if ($item['metal_type']): ?>
                                                            <br><span class="badge bg-warning text-dark"><?php echo htmlspecialchars($item['metal_type'] . ' ' . $item['purity']); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td><?php echo $item['quantity']; ?></td>
                                                <td><?php echo formatWeight($item['total_weight']); ?></td>
                                                <td><?php echo formatCurrency($item['unit_price']); ?></td>
                                                <td><?php echo formatCurrency($item['total_price']); ?></td>
                                                <td>
                                                    <?php echo $item['tax_rate']; ?>%<br>
                                                    <small><?php echo formatCurrency($item['tax_amount']); ?></small>
                                                </td>
                                                <td><strong><?php echo formatCurrency($item['line_total']); ?></strong></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Sale Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Sale Summary</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Bill Number:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['bill_number']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date:</strong></td>
                                    <td><?php echo formatDate($sale['sale_date']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Time:</strong></td>
                                    <td><?php echo date('H:i:s', strtotime($sale['sale_time'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>
                                        <?php if ($sale['customer_name']): ?>
                                            <?php echo htmlspecialchars($sale['customer_name']); ?>
                                            <?php if ($sale['business_name']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($sale['business_name']); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            Walk-in Customer
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo ucfirst(str_replace('_', ' ', $sale['payment_method'])); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Status:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo $sale['payment_status'] === 'paid' ? 'success' : ($sale['payment_status'] === 'partial' ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst($sale['payment_status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created By:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['created_by_name'] ?: 'System'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Amount Breakdown -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Amount Breakdown</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td>Subtotal:</td>
                                    <td class="text-end"><?php echo formatCurrency($sale['subtotal']); ?></td>
                                </tr>
                                <?php if ($sale['discount_amount'] > 0): ?>
                                <tr>
                                    <td>Discount:</td>
                                    <td class="text-end text-success">-<?php echo formatCurrency($sale['discount_amount']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td>Tax (GST):</td>
                                    <td class="text-end"><?php echo formatCurrency($sale['tax_amount']); ?></td>
                                </tr>
                                <tr class="border-top">
                                    <td><strong>Grand Total:</strong></td>
                                    <td class="text-end"><strong class="text-primary fs-5"><?php echo formatCurrency($sale['grand_total']); ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($sale['notes']): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Notes</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($sale['notes'])); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

    </main>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        function printBill(saleId) {
            window.open(`print-bill.php?id=${saleId}`, '_blank');
        }
        
        function generatePDF(saleId) {
            // This would integrate with the PDF generation system
            alert('PDF generation feature will be implemented with jsPDF integration');
        }
    </script>
</body>
</html>
