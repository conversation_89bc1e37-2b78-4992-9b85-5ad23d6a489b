<?php
/**
 * Export Customers Data - Indian Jewellery Wholesale Management System v2.0
 */

require_once 'config/database.php';

startSession();
// requireLogin(); // Uncomment when authentication is implemented

$db = getDB();
$format = $_GET['format'] ?? 'csv';

try {
    // Check if specific customers are selected
    $selectedCustomers = $_POST['selected_customers'] ?? [];
    
    $whereClause = "c.is_active = 1";
    $params = [];
    
    if (!empty($selectedCustomers)) {
        $placeholders = str_repeat('?,', count($selectedCustomers) - 1) . '?';
        $whereClause .= " AND c.id IN ($placeholders)";
        $params = $selectedCustomers;
    }

    // Get customers data with comprehensive information
    $customers = $db->fetchAll("
        SELECT c.*, 
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as sales_count,
               (SELECT COALESCE(SUM(grand_total), 0) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as total_purchases,
               (SELECT MAX(sale_date) FROM sales WHERE customer_id = c.id AND is_cancelled = 0) as last_purchase_date,
               (SELECT COALESCE(SUM(grand_total - COALESCE((SELECT SUM(amount) FROM payments WHERE sale_id = sales.id), 0)), 0) 
                FROM sales WHERE customer_id = c.id AND is_cancelled = 0 AND payment_status != 'paid') as outstanding_amount
        FROM customers c
        WHERE $whereClause
        ORDER BY c.customer_name
    ", $params);

    if ($format === 'csv') {
        // CSV Export
        $filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM for UTF-8

        // CSV Headers
        $headers = [
            'Customer ID', 'Customer Name', 'Business Name', 'Customer Type', 'Phone', 'Email',
            'Address', 'City', 'State', 'Pincode', 'GST Number', 'PAN Number', 'Aadhar Number',
            'Credit Limit (₹)', 'Credit Days', 'Discount %', 'Sales Count', 'Total Purchases (₹)',
            'Outstanding Amount (₹)', 'Last Purchase Date', 'Created Date'
        ];

        fputcsv($output, $headers);

        foreach ($customers as $customer) {
            $row = [
                $customer['id'],
                $customer['customer_name'],
                $customer['business_name'],
                $customer['customer_type'],
                $customer['phone'],
                $customer['email'],
                $customer['address'],
                $customer['city'],
                $customer['state'],
                $customer['pincode'],
                $customer['gst_number'],
                $customer['pan_number'],
                $customer['aadhar_number'],
                $customer['credit_limit'],
                $customer['credit_days'],
                $customer['discount_percentage'],
                $customer['sales_count'],
                $customer['total_purchases'],
                $customer['outstanding_amount'] ?? 0,
                $customer['last_purchase_date'] ? date('d/m/Y', strtotime($customer['last_purchase_date'])) : '',
                date('d/m/Y H:i:s', strtotime($customer['created_at']))
            ];
            fputcsv($output, $row);
        }

        fclose($output);
        exit;

    } elseif ($format === 'pdf') {
        // PDF Export (HTML version)
        $filename = 'customers_report_' . date('Y-m-d_H-i-s') . '.pdf';
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Customers Report</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .contact-info { font-size: 11px; }
                .outstanding { color: #dc3545; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Customers Report</h1>
                <p>Generated on: " . date('d/m/Y H:i:s') . "</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Customer</th>
                        <th>Type</th>
                        <th>Contact</th>
                        <th>Location</th>
                        <th>Sales</th>
                        <th>Total Purchases</th>
                        <th>Outstanding</th>
                        <th>Credit Limit</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($customers as $customer) {
            $outstandingClass = $customer['outstanding_amount'] > 0 ? 'outstanding' : '';
            
            echo "<tr>
                    <td>
                        <strong>" . htmlspecialchars($customer['customer_name']) . "</strong><br>
                        " . ($customer['business_name'] ? '<small>' . htmlspecialchars($customer['business_name']) . '</small>' : '') . "
                    </td>
                    <td>" . ucfirst($customer['customer_type']) . "</td>
                    <td class='contact-info'>
                        " . htmlspecialchars($customer['phone']) . "<br>
                        " . htmlspecialchars($customer['email']) . "
                    </td>
                    <td>
                        " . htmlspecialchars($customer['city']) . "<br>
                        " . htmlspecialchars($customer['state']) . "
                    </td>
                    <td>" . $customer['sales_count'] . "</td>
                    <td>₹" . number_format($customer['total_purchases'], 2) . "</td>
                    <td class='$outstandingClass'>₹" . number_format($customer['outstanding_amount'] ?? 0, 2) . "</td>
                    <td>₹" . number_format($customer['credit_limit'], 2) . "</td>
                  </tr>";
        }
        
        echo "</tbody>
            </table>
            
            <script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>";
        
        exit;

    } elseif ($format === 'excel') {
        // Excel Export
        $filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo "<!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Customers Report</title>
        </head>
        <body>
            <table border='1'>
                <tr>
                    <th>Customer ID</th>
                    <th>Customer Name</th>
                    <th>Business Name</th>
                    <th>Type</th>
                    <th>Phone</th>
                    <th>Email</th>
                    <th>City</th>
                    <th>State</th>
                    <th>GST Number</th>
                    <th>Credit Limit</th>
                    <th>Credit Days</th>
                    <th>Sales Count</th>
                    <th>Total Purchases</th>
                    <th>Outstanding Amount</th>
                    <th>Last Purchase</th>
                </tr>";

        foreach ($customers as $customer) {
            echo "<tr>
                    <td>" . $customer['id'] . "</td>
                    <td>" . htmlspecialchars($customer['customer_name']) . "</td>
                    <td>" . htmlspecialchars($customer['business_name']) . "</td>
                    <td>" . ucfirst($customer['customer_type']) . "</td>
                    <td>" . htmlspecialchars($customer['phone']) . "</td>
                    <td>" . htmlspecialchars($customer['email']) . "</td>
                    <td>" . htmlspecialchars($customer['city']) . "</td>
                    <td>" . htmlspecialchars($customer['state']) . "</td>
                    <td>" . htmlspecialchars($customer['gst_number']) . "</td>
                    <td>" . $customer['credit_limit'] . "</td>
                    <td>" . $customer['credit_days'] . "</td>
                    <td>" . $customer['sales_count'] . "</td>
                    <td>" . $customer['total_purchases'] . "</td>
                    <td>" . ($customer['outstanding_amount'] ?? 0) . "</td>
                    <td>" . ($customer['last_purchase_date'] ? date('d/m/Y', strtotime($customer['last_purchase_date'])) : '') . "</td>
                  </tr>";
        }

        echo "</table>
        </body>
        </html>";
        
        exit;
    }

} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #dc3545;'>Export Error</h3>";
    echo "<p>Error exporting customers: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='customers.php'>← Back to Customers</a></p>";
    echo "</div>";
}
?>
