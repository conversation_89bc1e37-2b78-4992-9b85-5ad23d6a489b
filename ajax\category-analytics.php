<?php
/**
 * Category Analytics AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session
startSession();

try {
    $db = getDB();
    
    // Get category statistics
    $totalCategories = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'];
    $mainCategories = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1 AND parent_id IS NULL")['count'];
    $subCategories = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1 AND parent_id IS NOT NULL")['count'];
    $totalProducts = $db->fetch("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'];
    
    // Get categories with product distribution
    $categoryDistribution = $db->fetchAll("
        SELECT c.category_name,
               COUNT(p.id) as product_count,
               COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0) as stock_value
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE c.is_active = 1
        GROUP BY c.id, c.category_name
        ORDER BY product_count DESC
    ");
    
    // Get top categories by stock value
    $topCategories = $db->fetchAll("
        SELECT c.category_name,
               COUNT(p.id) as product_count,
               COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0) as stock_value
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE c.is_active = 1
        GROUP BY c.id, c.category_name
        HAVING stock_value > 0
        ORDER BY stock_value DESC
        LIMIT 5
    ");
    
    // Get categories without products
    $emptyCategories = $db->fetchAll("
        SELECT c.category_name
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        WHERE c.is_active = 1
        GROUP BY c.id, c.category_name
        HAVING COUNT(p.id) = 0
    ");
    
    // Calculate total stock value
    $totalStockValue = $db->fetch("
        SELECT COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0) as total
        FROM inventory i
        JOIN products p ON i.product_id = p.id
        WHERE p.is_active = 1
    ")['total'];
    
    // Prepare statistics
    $statistics = [
        [
            'label' => 'Total Categories',
            'value' => $totalCategories
        ],
        [
            'label' => 'Main Categories',
            'value' => $mainCategories
        ],
        [
            'label' => 'Subcategories',
            'value' => $subCategories
        ],
        [
            'label' => 'Total Products',
            'value' => $totalProducts
        ],
        [
            'label' => 'Empty Categories',
            'value' => count($emptyCategories)
        ],
        [
            'label' => 'Total Stock Value',
            'value' => '₹' . number_format($totalStockValue, 2)
        ]
    ];
    
    // Format top categories
    $formattedTopCategories = [];
    foreach ($topCategories as $category) {
        $formattedTopCategories[] = [
            'name' => $category['category_name'],
            'products' => $category['product_count'],
            'value' => number_format($category['stock_value'], 2)
        ];
    }
    
    // Format performance data
    $performance = [];
    $maxProducts = max(array_column($categoryDistribution, 'product_count'));
    
    foreach ($categoryDistribution as $category) {
        if ($category['product_count'] > 0) {
            $percentage = $maxProducts > 0 ? ($category['product_count'] / $maxProducts) * 100 : 0;
            $performance[] = [
                'category' => $category['category_name'],
                'products' => $category['product_count'],
                'percentage' => round($percentage, 1)
            ];
        }
    }
    
    // Limit performance to top 10
    $performance = array_slice($performance, 0, 10);
    
    // Format distribution for chart
    $distribution = [];
    foreach ($categoryDistribution as $category) {
        if ($category['product_count'] > 0) {
            $distribution[] = [
                'label' => $category['category_name'],
                'value' => $category['product_count'],
                'stockValue' => $category['stock_value']
            ];
        }
    }
    
    // Get category hierarchy depth
    $maxDepth = $db->fetch("
        SELECT MAX(depth) as max_depth
        FROM (
            SELECT c1.id,
                   CASE 
                       WHEN c1.parent_id IS NULL THEN 1
                       ELSE 2
                   END as depth
            FROM categories c1
            WHERE c1.is_active = 1
        ) as depths
    ")['max_depth'] ?? 1;
    
    // Get recent category additions
    $recentCategories = $db->fetchAll("
        SELECT category_name, created_at
        FROM categories
        WHERE is_active = 1
        ORDER BY created_at DESC
        LIMIT 5
    ");
    
    // Return analytics data
    echo json_encode([
        'success' => true,
        'statistics' => $statistics,
        'topCategories' => $formattedTopCategories,
        'performance' => $performance,
        'distribution' => $distribution,
        'insights' => [
            [
                'title' => 'Category Structure',
                'description' => "Your catalog has {$mainCategories} main categories with {$subCategories} subcategories (max depth: {$maxDepth})"
            ],
            [
                'title' => 'Product Distribution',
                'description' => count($emptyCategories) > 0 ? 
                    count($emptyCategories) . " categories don't have any products assigned" :
                    "All categories have products assigned"
            ],
            [
                'title' => 'Stock Concentration',
                'description' => count($topCategories) > 0 ?
                    "Top category '" . $topCategories[0]['category_name'] . "' holds ₹" . number_format($topCategories[0]['stock_value'], 2) . " in stock value" :
                    "No stock value data available"
            ]
        ],
        'summary' => [
            'totalCategories' => $totalCategories,
            'categoriesWithProducts' => $totalCategories - count($emptyCategories),
            'averageProductsPerCategory' => $totalCategories > 0 ? round($totalProducts / $totalCategories, 1) : 0,
            'totalStockValue' => number_format($totalStockValue, 2),
            'lastUpdated' => date('d/m/Y H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
