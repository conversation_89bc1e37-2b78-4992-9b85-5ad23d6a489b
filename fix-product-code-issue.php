<?php
/**
 * Fix Product Code Duplicate Entry Issue
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🔧 Fixing Product Code Duplicate Entry Issue</h2>";
    
    // Step 1: Check current product codes
    echo "<h3>📋 Current Product Code Status</h3>";
    
    $products = $db->fetchAll("SELECT id, product_name, product_code FROM products ORDER BY id");
    $duplicates = [];
    $codes = [];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>Product Name</th><th style='padding: 8px;'>Current Code</th><th style='padding: 8px;'>Status</th></tr>";
    
    foreach ($products as $product) {
        $status = "✅ Unique";
        $bgColor = "";
        
        if (in_array($product['product_code'], $codes)) {
            $status = "❌ Duplicate";
            $bgColor = "background-color: #f8d7da;";
            $duplicates[] = $product;
        }
        $codes[] = $product['product_code'];
        
        echo "<tr style='$bgColor'>";
        echo "<td style='padding: 8px;'>{$product['id']}</td>";
        echo "<td style='padding: 8px;'>{$product['product_name']}</td>";
        echo "<td style='padding: 8px;'><strong>{$product['product_code']}</strong></td>";
        echo "<td style='padding: 8px;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 2: Fix duplicate product codes
    if (count($duplicates) > 0) {
        echo "<h3>🔨 Fixing Duplicate Product Codes</h3>";
        
        $fixed = 0;
        foreach ($products as $index => $product) {
            // Generate unique product code based on product type and ID
            $productType = '';
            $name = strtolower($product['product_name']);
            
            if (strpos($name, 'ring') !== false) {
                $productType = 'GR'; // Gold Ring
            } elseif (strpos($name, 'necklace') !== false) {
                $productType = 'GN'; // Gold Necklace
            } elseif (strpos($name, 'earring') !== false) {
                $productType = 'GE'; // Gold Earrings
            } elseif (strpos($name, 'bracelet') !== false) {
                $productType = 'SB'; // Silver Bracelet
            } elseif (strpos($name, 'chain') !== false) {
                $productType = 'GC'; // Gold Chain
            } else {
                $productType = 'JW'; // Jewellery Wholesale
            }
            
            $newCode = $productType . str_pad($product['id'], 3, '0', STR_PAD_LEFT);
            
            try {
                $db->execute("UPDATE products SET product_code = ? WHERE id = ?", [$newCode, $product['id']]);
                echo "✅ Updated Product ID {$product['id']}: '{$product['product_code']}' → '<strong>$newCode</strong>'<br>";
                $fixed++;
            } catch (Exception $e) {
                echo "❌ Error updating Product ID {$product['id']}: " . $e->getMessage() . "<br>";
            }
        }
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Fixed $fixed Product Codes</h4>";
        echo "<p>All product codes have been updated to be unique.</p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ No Duplicates Found</h4>";
        echo "<p>All product codes are already unique.</p>";
        echo "</div>";
    }
    
    // Step 3: Check and fix database constraints
    echo "<h3>🗄️ Checking Database Constraints</h3>";
    
    try {
        // Check if unique constraint exists on product_code
        $constraints = $db->fetchAll("
            SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'products' 
            AND CONSTRAINT_TYPE = 'UNIQUE'
        ");
        
        $hasUniqueConstraint = false;
        foreach ($constraints as $constraint) {
            if (strpos($constraint['CONSTRAINT_NAME'], 'product_code') !== false) {
                $hasUniqueConstraint = true;
                break;
            }
        }
        
        if (!$hasUniqueConstraint) {
            echo "⚠️ Adding unique constraint to product_code column...<br>";
            try {
                $db->execute("ALTER TABLE products ADD UNIQUE KEY unique_product_code (product_code)");
                echo "✅ Added unique constraint to product_code<br>";
            } catch (Exception $e) {
                echo "❌ Error adding unique constraint: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✅ Unique constraint already exists on product_code<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error checking constraints: " . $e->getMessage() . "<br>";
    }
    
    // Step 4: Create product code generator function
    echo "<h3>🔧 Creating Product Code Generator</h3>";
    
    $codeGeneratorContent = '<?php
/**
 * Product Code Generator for Indian Jewellery
 */

class ProductCodeGenerator {
    private $db;
    
    public function __construct($database = null) {
        $this->db = $database ?: getDB();
    }
    
    /**
     * Generate unique product code based on product type and category
     */
    public function generateProductCode($productName, $categoryId = null, $metalType = null) {
        // Determine product type prefix
        $prefix = $this->getProductPrefix($productName, $metalType);
        
        // Get next sequence number for this prefix
        $sequence = $this->getNextSequence($prefix);
        
        // Generate code: PREFIX + 3-digit sequence
        $code = $prefix . str_pad($sequence, 3, "0", STR_PAD_LEFT);
        
        // Ensure uniqueness
        while ($this->codeExists($code)) {
            $sequence++;
            $code = $prefix . str_pad($sequence, 3, "0", STR_PAD_LEFT);
        }
        
        return $code;
    }
    
    /**
     * Get product prefix based on name and metal type
     */
    private function getProductPrefix($productName, $metalType = null) {
        $name = strtolower($productName);
        $metal = strtolower($metalType ?: "");
        
        // Gold products
        if (strpos($metal, "gold") !== false || strpos($name, "gold") !== false) {
            if (strpos($name, "ring") !== false) return "GR";
            if (strpos($name, "necklace") !== false || strpos($name, "chain") !== false) return "GN";
            if (strpos($name, "earring") !== false) return "GE";
            if (strpos($name, "bracelet") !== false) return "GB";
            if (strpos($name, "pendant") !== false) return "GP";
            if (strpos($name, "bangle") !== false) return "GBG";
            return "GJ"; // General Gold Jewellery
        }
        
        // Silver products
        if (strpos($metal, "silver") !== false || strpos($name, "silver") !== false) {
            if (strpos($name, "ring") !== false) return "SR";
            if (strpos($name, "necklace") !== false || strpos($name, "chain") !== false) return "SN";
            if (strpos($name, "earring") !== false) return "SE";
            if (strpos($name, "bracelet") !== false) return "SB";
            if (strpos($name, "pendant") !== false) return "SP";
            if (strpos($name, "bangle") !== false) return "SBG";
            return "SJ"; // General Silver Jewellery
        }
        
        // Diamond/Platinum products
        if (strpos($name, "diamond") !== false) return "DJ";
        if (strpos($name, "platinum") !== false) return "PJ";
        
        // Default
        return "JW"; // Jewellery Wholesale
    }
    
    /**
     * Get next sequence number for prefix
     */
    private function getNextSequence($prefix) {
        try {
            $result = $this->db->fetch("
                SELECT MAX(CAST(SUBSTRING(product_code, 3) AS UNSIGNED)) as max_seq 
                FROM products 
                WHERE product_code LIKE ?
            ", [$prefix . "%"]);
            
            return ($result["max_seq"] ?? 0) + 1;
        } catch (Exception $e) {
            return 1;
        }
    }
    
    /**
     * Check if product code already exists
     */
    private function codeExists($code) {
        try {
            $result = $this->db->fetch("SELECT id FROM products WHERE product_code = ?", [$code]);
            return $result !== null;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Update all existing products with proper codes
     */
    public function updateAllProductCodes() {
        try {
            $products = $this->db->fetchAll("
                SELECT id, product_name, metal_type, category_id 
                FROM products 
                ORDER BY id
            ");
            
            $updated = 0;
            foreach ($products as $product) {
                $newCode = $this->generateProductCode(
                    $product["product_name"], 
                    $product["category_id"], 
                    $product["metal_type"]
                );
                
                $this->db->execute("UPDATE products SET product_code = ? WHERE id = ?", 
                    [$newCode, $product["id"]]);
                $updated++;
            }
            
            return $updated;
        } catch (Exception $e) {
            throw new Exception("Error updating product codes: " . $e->getMessage());
        }
    }
}
?>';
    
    if (!is_dir('lib')) {
        mkdir('lib', 0755, true);
    }
    
    file_put_contents('lib/ProductCodeGenerator.php', $codeGeneratorContent);
    echo "✅ Created ProductCodeGenerator class at lib/ProductCodeGenerator.php<br>";
    
    // Step 5: Test the generator
    echo "<h3>🧪 Testing Product Code Generator</h3>";
    
    try {
        require_once 'lib/ProductCodeGenerator.php';
        $generator = new ProductCodeGenerator($db);
        
        // Test with sample products
        $testProducts = [
            ['Gold Ring Classic', 'Gold'],
            ['Silver Necklace Set', 'Silver'],
            ['Diamond Earrings', 'Gold'],
            ['Platinum Bracelet', 'Platinum']
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>Product Name</th><th style='padding: 8px;'>Metal Type</th><th style='padding: 8px;'>Generated Code</th></tr>";
        
        foreach ($testProducts as $product) {
            $code = $generator->generateProductCode($product[0], null, $product[1]);
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$product[0]}</td>";
            echo "<td style='padding: 8px;'>{$product[1]}</td>";
            echo "<td style='padding: 8px;'><strong>$code</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Product Code Generator Working!</h4>";
        echo "<p>The generator is creating unique, meaningful product codes.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Generator Test Failed</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Step 6: Show updated products
    echo "<h3>📋 Updated Product Codes</h3>";
    
    $updatedProducts = $db->fetchAll("SELECT id, product_name, product_code, metal_type FROM products ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>Product Name</th><th style='padding: 8px;'>Metal Type</th><th style='padding: 8px;'>New Code</th></tr>";
    
    foreach ($updatedProducts as $product) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$product['id']}</td>";
        echo "<td style='padding: 8px;'>{$product['product_name']}</td>";
        echo "<td style='padding: 8px;'>{$product['metal_type']}</td>";
        echo "<td style='padding: 8px;'><strong>{$product['product_code']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Product Code Issue Fixed!</h3>";
    echo "<ul>";
    echo "<li>✅ All duplicate product codes resolved</li>";
    echo "<li>✅ Unique constraint properly configured</li>";
    echo "<li>✅ Product code generator created</li>";
    echo "<li>✅ Meaningful codes assigned (GR=Gold Ring, SB=Silver Bracelet, etc.)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='inventory.php' style='background-color: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>View Updated Inventory</a>";
    echo "<a href='billing.php' style='background-color: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Billing</a>";
    echo "<a href='test-tunch-calculator.php' style='background-color: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 4px; margin: 10px;'>Test Calculator</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Error</h4>";
    echo "<p>Error fixing product codes: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
