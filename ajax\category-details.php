<?php
/**
 * Category Details AJAX Endpoint - Indian Jewellery Wholesale Management System v2.0
 */

require_once '../config/database.php';

// Start session
startSession();

try {
    $category_id = $_GET['id'] ?? null;
    
    if (!$category_id) {
        throw new Exception('Category ID required');
    }
    
    $db = getDB();
    
    // Get category details
    $category = $db->fetch("
        SELECT c.*, 
               p.category_name as parent_name,
               (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count,
               (SELECT COUNT(*) FROM categories WHERE parent_id = c.id AND is_active = 1) as subcategory_count,
               (SELECT COALESCE(SUM(i.quantity_in_stock * i.cost_price), 0) 
                FROM products pr 
                JOIN inventory i ON pr.id = i.product_id 
                WHERE pr.category_id = c.id AND pr.is_active = 1) as total_stock_value
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.id = ? AND c.is_active = 1
    ", [$category_id]);
    
    if (!$category) {
        throw new Exception('Category not found');
    }
    
    // Get subcategories
    $subcategories = $db->fetchAll("
        SELECT id, category_name, description,
               (SELECT COUNT(*) FROM products WHERE category_id = categories.id AND is_active = 1) as product_count
        FROM categories 
        WHERE parent_id = ? AND is_active = 1
        ORDER BY sort_order, category_name
    ", [$category_id]);
    
    // Get recent products in this category
    $recentProducts = $db->fetchAll("
        SELECT p.*, i.quantity_in_stock, i.cost_price
        FROM products p
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.category_id = ? AND p.is_active = 1
        ORDER BY p.created_at DESC
        LIMIT 5
    ", [$category_id]);
    
    // Get category sales performance
    $salesPerformance = $db->fetch("
        SELECT 
            COUNT(DISTINCT s.id) as total_sales,
            COALESCE(SUM(si.quantity), 0) as total_quantity_sold,
            COALESCE(SUM(si.total_price), 0) as total_revenue
        FROM products p
        LEFT JOIN sale_items si ON p.id = si.product_id
        LEFT JOIN sales s ON si.sale_id = s.id AND s.is_cancelled = 0
        WHERE p.category_id = ? AND p.is_active = 1
    ", [$category_id]);
    
    // Generate HTML response
    echo "<div class='row'>";
    
    // Category Information
    echo "<div class='col-md-6 mb-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h6 class='mb-0'><i class='fas fa-info-circle me-2'></i>Category Information</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-sm-4'><strong>Name:</strong></div>";
    echo "<div class='col-sm-8'>" . htmlspecialchars($category['category_name']) . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-sm-4'><strong>Parent:</strong></div>";
    echo "<div class='col-sm-8'>" . ($category['parent_name'] ? htmlspecialchars($category['parent_name']) : 'Main Category') . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-sm-4'><strong>Description:</strong></div>";
    echo "<div class='col-sm-8'>" . ($category['description'] ? htmlspecialchars($category['description']) : 'No description') . "</div>";
    echo "</div>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-sm-4'><strong>Sort Order:</strong></div>";
    echo "<div class='col-sm-8'><span class='badge bg-light text-dark'>" . $category['sort_order'] . "</span></div>";
    echo "</div>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-sm-4'><strong>Created:</strong></div>";
    echo "<div class='col-sm-8'>" . date('d/m/Y H:i:s', strtotime($category['created_at'])) . "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Statistics
    echo "<div class='col-md-6 mb-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h6 class='mb-0'><i class='fas fa-chart-bar me-2'></i>Statistics</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='row text-center'>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-primary'>" . $category['product_count'] . "</div>";
    echo "<small class='text-muted'>Products</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-success'>" . $category['subcategory_count'] . "</div>";
    echo "<small class='text-muted'>Subcategories</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-info'>₹" . number_format($category['total_stock_value'], 2) . "</div>";
    echo "<small class='text-muted'>Stock Value</small>";
    echo "</div>";
    echo "<div class='col-6 mb-3'>";
    echo "<div class='h4 text-warning'>" . ($salesPerformance['total_sales'] ?? 0) . "</div>";
    echo "<small class='text-muted'>Sales</small>";
    echo "</div>";
    echo "</div>";
    
    if ($salesPerformance && $salesPerformance['total_revenue'] > 0) {
        echo "<div class='alert alert-success'>";
        echo "<strong>Revenue:</strong> ₹" . number_format($salesPerformance['total_revenue'], 2) . "<br>";
        echo "<strong>Quantity Sold:</strong> " . $salesPerformance['total_quantity_sold'] . " items";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    // Subcategories
    if (!empty($subcategories)) {
        echo "<div class='row'>";
        echo "<div class='col-12 mb-4'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='fas fa-sitemap me-2'></i>Subcategories</h6>";
        echo "</div>";
        echo "<div class='card-body p-0'>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-hover mb-0'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>Name</th>";
        echo "<th>Description</th>";
        echo "<th>Products</th>";
        echo "<th>Actions</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($subcategories as $sub) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($sub['category_name']) . "</strong></td>";
            echo "<td>" . ($sub['description'] ? htmlspecialchars(substr($sub['description'], 0, 50)) : '-') . "</td>";
            echo "<td><span class='badge bg-info'>" . $sub['product_count'] . "</span></td>";
            echo "<td>";
            echo "<a href='categories.php?action=edit&id=" . $sub['id'] . "' class='btn btn-sm btn-outline-primary' target='_blank'>";
            echo "<i class='fas fa-edit'></i>";
            echo "</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Recent Products
    if (!empty($recentProducts)) {
        echo "<div class='row'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='fas fa-box me-2'></i>Recent Products</h6>";
        echo "</div>";
        echo "<div class='card-body p-0'>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-hover mb-0'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>Product Name</th>";
        echo "<th>SKU</th>";
        echo "<th>Stock</th>";
        echo "<th>Cost Price</th>";
        echo "<th>Added</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($recentProducts as $product) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($product['product_name']) . "</strong></td>";
            echo "<td><code>" . htmlspecialchars($product['sku']) . "</code></td>";
            echo "<td>" . ($product['quantity_in_stock'] ?? 0) . "</td>";
            echo "<td>₹" . number_format($product['cost_price'] ?? 0, 2) . "</td>";
            echo "<td>" . date('d/m/Y', strtotime($product['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Action buttons
    echo "<div class='text-center mt-4'>";
    echo "<a href='categories.php?action=edit&id=" . $category['id'] . "' class='btn btn-primary' target='_blank'>";
    echo "<i class='fas fa-edit me-2'></i>Edit Category";
    echo "</a>";
    
    if ($category['product_count'] > 0) {
        echo "<a href='products.php?category_id=" . $category['id'] . "' class='btn btn-success ms-2' target='_blank'>";
        echo "<i class='fas fa-box me-2'></i>View Products";
        echo "</a>";
    }
    
    echo "<button class='btn btn-outline-secondary ms-2' onclick='window.print()'>";
    echo "<i class='fas fa-print me-2'></i>Print Details";
    echo "</button>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
